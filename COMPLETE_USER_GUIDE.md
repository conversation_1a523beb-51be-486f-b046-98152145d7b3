# دليل المستخدم الشامل - نظام أرشفة ملفات الموظفين

## 📋 جدول المحتويات
1. [نظرة عامة](#نظرة-عامة)
2. [متطلبات النظام](#متطلبات-النظام)
3. [التثبيت والإعداد](#التثبيت-والإعداد)
4. [تشغيل النظام](#تشغيل-النظام)
5. [دليل الاستخدام](#دليل-الاستخدام)
6. [إدارة النسخ الاحتياطية](#إدارة-النسخ-الاحتياطية)
7. [استكشاف الأخطاء وإصلاحها](#استكشاف-الأخطاء-وإصلاحها)
8. [الأسئلة الشائعة](#الأسئلة-الشائعة)

---

## 🎯 نظرة عامة

نظام أرشفة ملفات الموظفين هو تطبيق ويب شامل لإدارة وأرشفة ملفات الموظفين بطريقة منظمة وآمنة.

### ✨ الميزات الرئيسية:
- **إدارة ملفات الموظفين**: رفع وتنظيم وعرض ملفات الموظفين
- **البحث المتقدم**: البحث في أسماء الموظفين والملفات
- **النسخ الاحتياطية**: إنشاء واستعادة النسخ الاحتياطية
- **إدارة المستخدمين**: نظام مصادقة وصلاحيات
- **واجهة عربية**: دعم كامل للغة العربية
- **عرض الملفات**: عرض PDF والصور مباشرة في المتصفح

---

## 💻 متطلبات النظام

### الحد الأدنى:
- **نظام التشغيل**: Windows 10/11, macOS 10.15+, Linux Ubuntu 18.04+
- **المعالج**: Intel Core i3 أو AMD Ryzen 3
- **الذاكرة**: 4 GB RAM
- **التخزين**: 2 GB مساحة فارغة
- **الشبكة**: اتصال إنترنت (للتثبيت الأولي)

### المتطلبات المسبقة:
- **Node.js**: الإصدار 16.0 أو أحدث
- **npm**: يأتي مع Node.js
- **متصفح ويب حديث**: Chrome, Firefox, Safari, Edge

---

## 🚀 التثبيت والإعداد

### الطريقة الأولى: التثبيت التلقائي (Windows)

1. **تنزيل المشروع**:
   ```bash
   git clone [repository-url]
   cd employee-archive-system
   ```

2. **تشغيل الإعداد التلقائي**:
   ```cmd
   setup.bat
   ```
   
   سيقوم هذا الملف بـ:
   - تثبيت جميع التبعيات
   - إعداد قاعدة البيانات
   - إنشاء بيانات تجريبية
   - إنشاء اختصار على سطح المكتب

### الطريقة الثانية: التثبيت اليدوي

1. **تثبيت تبعيات الخادم**:
   ```bash
   cd backend
   npm install
   ```

2. **تثبيت تبعيات الواجهة الأمامية**:
   ```bash
   cd ../frontend
   npm install
   ```

3. **إعداد قاعدة البيانات**:
   ```bash
   cd ../backend
   node scripts/init-sample-data.js
   ```

---

## ▶️ تشغيل النظام

### الطريقة الأولى: التشغيل التلقائي (Windows)
```cmd
start.bat
```

### الطريقة الثانية: التشغيل اليدوي

1. **تشغيل الخادم الخلفي**:
   ```bash
   cd backend
   npm start
   ```

2. **تشغيل الواجهة الأمامية** (في نافذة طرفية جديدة):
   ```bash
   cd frontend
   npm run dev
   ```

3. **فتح التطبيق**:
   - افتح المتصفح واذهب إلى: `http://localhost:5173`

### الطريقة الثالثة: التشغيل المتزامن
```bash
npm start
```

---

## 📖 دليل الاستخدام

### 🔐 تسجيل الدخول

**الحسابات الافتراضية:**
- **المدير**: 
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`
- **المستخدم العادي**:
  - اسم المستخدم: `user`
  - كلمة المرور: `user123`

### 👥 إدارة الموظفين

#### إضافة موظف جديد:
1. اذهب إلى صفحة "الموظفين"
2. اضغط على "إضافة موظف جديد"
3. املأ البيانات المطلوبة:
   - الاسم الكامل
   - رقم الهوية
   - المنصب
   - القسم
   - تاريخ التوظيف
4. اضغط "حفظ"

#### رفع ملفات الموظف:
1. اختر الموظف من القائمة
2. اضغط على "رفع ملفات"
3. اختر الملفات من جهازك
4. أضف وصف للملفات (اختياري)
5. اضغط "رفع"

#### البحث عن الموظفين:
- استخدم شريط البحث في أعلى الصفحة
- يمكن البحث بـ:
  - الاسم
  - رقم الهوية
  - المنصب
  - القسم

### 📁 إدارة الملفات

#### أنواع الملفات المدعومة:
- **المستندات**: PDF, DOC, DOCX, TXT
- **الصور**: JPG, JPEG, PNG, GIF
- **الجداول**: XLS, XLSX, CSV
- **العروض التقديمية**: PPT, PPTX

#### عرض الملفات:
- **PDF**: عرض مباشر في المتصفح
- **الصور**: عرض مباشر مع إمكانية التكبير
- **الملفات الأخرى**: تنزيل مباشر

#### تنظيم الملفات:
- يتم تنظيم الملفات تلقائياً حسب الموظف
- يمكن إضافة أوصاف للملفات
- يتم حفظ تاريخ الرفع تلقائياً

---

## 💾 إدارة النسخ الاحتياطية

### إنشاء نسخة احتياطية:

#### الطريقة الأولى: النسخ المحلي
1. اذهب إلى صفحة "النسخ الاحتياطي"
2. اضغط على "إنشاء نسخة احتياطية"
3. انتظر حتى اكتمال العملية
4. ستظهر النسخة في قائمة النسخ الاحتياطية

#### الطريقة الثانية: التنزيل المباشر
1. اضغط على "إنشاء وتنزيل نسخة احتياطية"
2. سيتم إنشاء النسخة وتنزيلها تلقائياً

### استعادة نسخة احتياطية:

#### الطريقة المتقدمة (موصى بها):
1. اضغط على "استعادة نسخة احتياطية"
2. اختر ملف النسخة الاحتياطية (.zip)
3. اضغط على "استعادة متقدمة"
4. انتظر حتى اكتمال العملية

#### الطريقة المبسطة:
1. اختر ملف النسخة الاحتياطية
2. اضغط على "استعادة مبسطة"
3. مناسبة للنسخ العادية

### فحص النسخة الاحتياطية:
- اضغط على "فحص النسخة" قبل الاستعادة
- يتحقق من سلامة الملفات
- يعرض محتويات النسخة الاحتياطية

---

## 🔧 استكشاف الأخطاء وإصلاحها

### مشاكل شائعة وحلولها:

#### 1. خطأ في تسجيل الدخول
**المشكلة**: "اسم المستخدم أو كلمة المرور غير صحيحة"
**الحل**:
- تأكد من استخدام الحسابات الافتراضية
- تحقق من أن Caps Lock غير مفعل
- جرب إعادة تعيين كلمة المرور

#### 2. فشل رفع الملفات
**المشكلة**: "فشل في رفع الملف"
**الحل**:
- تحقق من حجم الملف (الحد الأقصى 100MB)
- تأكد من نوع الملف مدعوم
- تحقق من مساحة التخزين المتاحة

#### 3. مشاكل النسخ الاحتياطية
**المشكلة**: "فشل في استعادة النسخة الاحتياطية"
**الحل**:
- استخدم الطريقة المبسطة أولاً
- تحقق من سلامة ملف النسخة الاحتياطية
- تأكد من وجود مساحة كافية

#### 4. بطء في الأداء
**المشكلة**: التطبيق بطيء
**الحل**:
- أغلق التطبيقات الأخرى
- تحقق من مساحة القرص الصلب
- أعد تشغيل التطبيق

### إعادة تعيين النظام:

#### إعادة تعيين قاعدة البيانات:
```bash
cd backend
node scripts/init-sample-data.js
```

#### حذف جميع البيانات:
```bash
rm -rf backend/data/employee_database/*
rm backend/data/archive.db
```

---

## ❓ الأسئلة الشائعة

### س: كيف يمكنني تغيير كلمة مرور المدير؟
**ج**: اذهب إلى صفحة "إدارة المستخدمين" واختر "تعديل المستخدم"

### س: هل يمكن استخدام النظام على أكثر من جهاز؟
**ج**: نعم، يمكن الوصول للنظام من أي جهاز على نفس الشبكة

### س: ما هو الحد الأقصى لحجم الملف؟
**ج**: 100 ميجابايت للملف الواحد

### س: هل البيانات آمنة؟
**ج**: نعم، يتم تشفير كلمات المرور وحماية الملفات

### س: كيف يمكنني نقل النظام لجهاز آخر؟
**ج**: انسخ مجلد المشروع كاملاً أو استخدم النسخ الاحتياطية

---

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. راجع قسم "استكشاف الأخطاء"
2. تحقق من ملفات السجل في وحدة التحكم
3. أعد تشغيل النظام
4. تواصل مع فريق الدعم

### ملفات السجل:
- **الخادم**: `backend_output.log`
- **الواجهة الأمامية**: `frontend_output.log`
- **وحدة التحكم**: F12 في المتصفح

---

## 🔄 التحديثات والصيانة

### النسخ الاحتياطية الدورية:
- أنشئ نسخة احتياطية يومياً
- احتفظ بنسخ متعددة
- اختبر استعادة النسخ دورياً

### تحديث النظام:
```bash
git pull origin main
npm install
```

### تنظيف النظام:
- احذف الملفات المؤقتة دورياً
- نظف ملفات السجل القديمة
- راقب مساحة التخزين

---

**تم إنشاء هذا الدليل بتاريخ**: مايو 2025  
**إصدار النظام**: 2.0  
**آخر تحديث**: مايو 2025

---

*لأي استفسارات إضافية، يرجى مراجعة الوثائق التقنية أو التواصل مع فريق التطوير.*
