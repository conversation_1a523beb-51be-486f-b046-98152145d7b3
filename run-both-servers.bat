@echo off
chcp 65001 >nul
title Employee Archive System - Starting Servers

echo.
echo ========================================
echo    Employee Archive System
echo    Starting Both Servers
echo ========================================
echo.

echo [INFO] Starting backend server...
cd backend
start "Backend Server" cmd /k "npm start"

echo [INFO] Waiting for backend to initialize...
timeout /t 3 /nobreak >nul

echo [INFO] Starting frontend server...
cd ..\frontend
start "Frontend Server" cmd /k "npm run dev"

echo.
echo ========================================
echo    Both servers are starting...
echo ========================================
echo.
echo Backend Server: http://localhost:5000
echo Frontend Server: http://localhost:5173
echo.
echo Press any key to exit this window...
pause >nul
