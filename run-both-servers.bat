@echo off
title Employee Archive System - Servers
color 0A

REM Start both servers without showing all the messages - optimized for speed
cd backend && start /b cmd /c "npm run dev --silent > ..\backend_output.log 2>&1" && cd ../frontend && start /b cmd /c "npm run dev --silent > ..\frontend_output.log 2>&1" && cd ..

echo Employee Archive System is running.
echo DO NOT CLOSE THIS WINDOW while using the system.
echo Closing this window will stop the entire system.

REM Add message to clarify server status
echo.
echo Server Status:
echo - Backend server: running on port 5000
echo - Frontend: running on port 5173
echo.
echo You can access the system at: http://localhost:5173
