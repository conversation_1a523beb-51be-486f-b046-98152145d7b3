import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import {
  FiDatabase,
  FiDownload,
  FiUpload,
  FiTrash2,
  FiRefreshCw,
  FiAlertTriangle,
  FiFile,
  FiSearch,
  FiCheckCircle,
  FiX,
  FiChevronRight,
  FiChevronLeft,
  FiAlertCircle,
  FiFolder,
  FiList
} from 'react-icons/fi';

const Backup = () => {
  const { token } = useAuth();
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [creatingBackup, setCreatingBackup] = useState(false);
  const [downloadingBackup, setDownloadingBackup] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  // متغيرات حالة للتصفح بالصفحات
  const [currentPage, setCurrentPage] = useState(1);
  const [backupsPerPage] = useState(10); // عدد النسخ الاحتياطية في كل صفحة
  const [backupToDelete, setBackupToDelete] = useState(null);
  const [restoring, setRestoring] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [successMessage, setSuccessMessage] = useState(null);
  const [successMessageType, setSuccessMessageType] = useState('add'); // 'add', 'download', 'restore', 'delete'
  const [errorMessage, setErrorMessage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [restoredBackupName, setRestoredBackupName] = useState('');

  // Validation state
  const [validating, setValidating] = useState(false);
  const [validationResult, setValidationResult] = useState(null);
  const [showValidationModal, setShowValidationModal] = useState(false);

  const fileInputRef = useRef(null);

  useEffect(() => {
    fetchBackups();
  }, []);

  // إزالة رسالة النجاح بعد 5 ثوانٍ
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // إزالة رسالة الخطأ بعد 5 ثوانٍ
  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => {
        setErrorMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  const fetchBackups = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/backup');
      setBackups(response.data);
      return response.data; // إعادة البيانات للاستخدام في then/catch
    } catch (err) {
      console.error('Error fetching backups:', err);
      setError('حدث خطأ أثناء جلب النسخ الاحتياطية');
      throw err; // إعادة رمي الخطأ للاستخدام في then/catch
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBackup = async () => {
    try {
      // إزالة أي رسائل سابقة
      setSuccessMessage(null);
      setErrorMessage(null);

      setCreatingBackup(true);

      const response = await axios.post('/api/backup/create');

      // إضافة النسخة الاحتياطية الجديدة إلى القائمة مباشرة
      if (response.data && response.data.backup) {
        setBackups(prevBackups => [response.data.backup, ...prevBackups]);
      } else {
        // إذا لم يتم إرجاع معلومات النسخة الاحتياطية، قم بتحديث القائمة بالكامل
        await fetchBackups();
      }

      // إظهار رسالة النجاح
      setSuccessMessage('تم إنشاء النسخة الاحتياطية بنجاح');
      setSuccessMessageType('add');
    } catch (err) {
      console.error('Error creating backup:', err);
      setErrorMessage('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
      // تأكد من عدم وجود رسالة نجاح في حالة حدوث خطأ
      setSuccessMessage(null);
    } finally {
      setCreatingBackup(false);
    }
  };

  const handleDownloadBackup = async () => {
    try {
      // إزالة أي رسائل سابقة
      setSuccessMessage(null);
      setErrorMessage(null);

      setDownloadingBackup(true);
      console.log('بدء تنزيل النسخة الاحتياطية...');

      // إنشاء نسخة احتياطية جديدة أولاً
      const createResponse = await axios.post('/api/backup/create');
      console.log('تم إنشاء النسخة الاحتياطية:', createResponse.data);

      if (createResponse.data && createResponse.data.backup) {
        // تحديث قائمة النسخ الاحتياطية
        setBackups(prevBackups => [createResponse.data.backup, ...prevBackups]);

        // تنزيل النسخة الاحتياطية التي تم إنشاؤها
        const backupName = createResponse.data.backup.name;
        console.log('تنزيل النسخة الاحتياطية:', backupName);

        // تنزيل النسخة الاحتياطية باستخدام نافذة جديدة
        console.log('فتح نافذة جديدة لتنزيل النسخة الاحتياطية');

        // الحصول على رمز المصادقة من localStorage أو sessionStorage
        const authToken = localStorage.getItem('token') || sessionStorage.getItem('token');

        // إنشاء URL للتنزيل
        const downloadUrl = `/api/backup/${backupName}`;

        try {
          // استخدام axios لتنزيل الملف كـ blob
          const response = await axios({
            url: downloadUrl,
            method: 'GET',
            responseType: 'blob',
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          });

          console.log('تم استلام الاستجابة من الخادم', response);

          // إنشاء blob من البيانات المستلمة
          const blob = new Blob([response.data], { type: 'application/zip' });

          // إنشاء URL للبلوب
          const url = window.URL.createObjectURL(blob);

          // إنشاء عنصر a للتنزيل
          const link = document.createElement('a');
          link.href = url;
          link.setAttribute('download', backupName);
          link.style.display = 'none';

          // إضافة الرابط إلى المستند والنقر عليه
          document.body.appendChild(link);
          link.click();

          // تنظيف الموارد
          window.URL.revokeObjectURL(url);
          document.body.removeChild(link);
          console.log('تم تنظيف الموارد');

          // إظهار رسالة النجاح فقط بعد نجاح التنزيل
          setSuccessMessage('تم إنشاء وتنزيل النسخة الاحتياطية بنجاح');
          setSuccessMessageType('download');
        } catch (downloadError) {
          console.error('خطأ في تنزيل النسخة الاحتياطية:', downloadError);
          setErrorMessage('حدث خطأ أثناء تنزيل النسخة الاحتياطية');
          throw downloadError; // إعادة رمي الخطأ للتعامل معه في الـ catch الخارجي
        }
      } else {
        throw new Error('لم يتم إنشاء النسخة الاحتياطية بشكل صحيح');
      }
    } catch (err) {
      console.error('Error downloading backup:', err);

      // رسائل خطأ أكثر تفصيلاً
      if (err.response) {
        console.error('Error response:', err.response.status, err.response.data);
        setErrorMessage(`حدث خطأ أثناء تنزيل النسخة الاحتياطية: ${err.response.status}`);
      } else if (err.request) {
        console.error('Error request:', err.request);
        setErrorMessage('لم يتم استلام استجابة من الخادم');
      } else {
        setErrorMessage(`حدث خطأ أثناء تنزيل النسخة الاحتياطية: ${err.message}`);
      }

      // تأكد من عدم وجود رسالة نجاح في حالة حدوث خطأ
      setSuccessMessage(null);
    } finally {
      setDownloadingBackup(false);
    }
  };

  const validateRestoreFile = () => {
    if (!selectedFile) {
      setErrorMessage('يرجى اختيار ملف النسخة الاحتياطية');
      return false;
    }

    if (!selectedFile.name.endsWith('.zip')) {
      setErrorMessage('يرجى اختيار ملف بصيغة ZIP');
      return false;
    }

    return true;
  };

  const handleShowConfirmModal = (e) => {
    e.preventDefault();

    if (validateRestoreFile()) {
      setShowConfirmModal(true);
    }
  };

  const handleRestoreBackup = async () => {
    try {
      // إزالة أي رسائل سابقة
      setSuccessMessage(null);
      setErrorMessage(null);

      if (!selectedFile) {
        setErrorMessage('يرجى اختيار ملف النسخة الاحتياطية');
        return;
      }

      setRestoring(true);
      setUploadProgress(0);
      setShowConfirmModal(false);

      const formData = new FormData();
      formData.append('backup', selectedFile);

      const response = await axios.post('/api/backup/restore', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      // Reset form
      if (fileInputRef.current) {
        fileInputRef.current.value = null;
      }

      // حفظ اسم الملف المستعاد
      setRestoredBackupName(selectedFile.name);

      setSelectedFile(null);
      setShowRestoreModal(false);

      // إظهار رسالة النجاح
      const successMsg = response.data?.message || 'تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.';
      setSuccessMessage(successMsg);
      setSuccessMessageType('restore');

      // عرض نافذة النجاح
      setShowSuccessModal(true);

      // تحديث قائمة النسخ الاحتياطية
      fetchBackups().catch(err => console.error('فشل في تحديث قائمة النسخ الاحتياطية:', err));
    } catch (err) {
      console.error('Error restoring backup:', err);
      setErrorMessage(err.response?.data?.message || 'حدث خطأ أثناء استعادة النسخة الاحتياطية');
      // تأكد من عدم وجود رسالة نجاح في حالة حدوث خطأ
      setSuccessMessage(null);
    } finally {
      setRestoring(false);
    }
  };

  const handleDownloadExistingBackup = async (backupName) => {
    try {
      // إزالة أي رسائل سابقة
      setSuccessMessage(null);
      setErrorMessage(null);

      console.log('تنزيل النسخة الاحتياطية الموجودة:', backupName);

      // الحصول على رمز المصادقة من localStorage أو sessionStorage
      const authToken = localStorage.getItem('token') || sessionStorage.getItem('token');

      if (!authToken) {
        setErrorMessage('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
        return;
      }

      // إنشاء URL للتنزيل
      const downloadUrl = `/api/backup/${backupName}`;

      try {
        // استخدام axios لتنزيل الملف كـ blob
        const response = await axios({
          url: downloadUrl,
          method: 'GET',
          responseType: 'blob',
          headers: {
            'Authorization': `Bearer ${authToken}`
          }
        });

        console.log('تم استلام الاستجابة من الخادم', response);

        // إنشاء blob من البيانات المستلمة
        const blob = new Blob([response.data], { type: 'application/zip' });

        // إنشاء URL للبلوب
        const url = window.URL.createObjectURL(blob);

        // إنشاء عنصر a للتنزيل
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', backupName);
        link.style.display = 'none';

        // إضافة الرابط إلى المستند والنقر عليه
        document.body.appendChild(link);
        link.click();

        // تنظيف الموارد
        window.URL.revokeObjectURL(url);
        document.body.removeChild(link);
        console.log('تم تنظيف الموارد');

        // إظهار رسالة النجاح
        setSuccessMessage(`تم تنزيل النسخة الاحتياطية "${backupName}" بنجاح`);
        setSuccessMessageType('download');
      } catch (downloadError) {
        console.error('خطأ في تنزيل النسخة الاحتياطية:', downloadError);
        setErrorMessage('حدث خطأ أثناء تنزيل النسخة الاحتياطية');
      }
    } catch (err) {
      console.error('Error downloading existing backup:', err);
      setErrorMessage('حدث خطأ أثناء تنزيل النسخة الاحتياطية');
      setSuccessMessage(null);
    }
  };

  const handleValidateBackup = async () => {
    if (!selectedFile) {
      setErrorMessage('يرجى اختيار ملف النسخة الاحتياطية أولاً');
      return;
    }

    try {
      setValidating(true);
      setValidationResult(null);
      setErrorMessage(null);

      const formData = new FormData();
      formData.append('backup', selectedFile);

      const response = await axios.post('/api/backup/validate', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setValidationResult(response.data);
      setShowValidationModal(true);
    } catch (err) {
      console.error('Error validating backup:', err);
      setErrorMessage(err.response?.data?.message || 'حدث خطأ أثناء فحص النسخة الاحتياطية');
    } finally {
      setValidating(false);
    }
  };

  const handleDeleteBackup = async () => {
    if (!backupToDelete) return;

    try {
      // إزالة أي رسائل سابقة
      setSuccessMessage(null);
      setErrorMessage(null);

      setDeleting(true);

      // حفظ اسم النسخة الاحتياطية قبل الحذف
      const backupName = backupToDelete.name;

      await axios.delete(`/api/backup/${backupName}`);

      // Refresh backups list
      fetchBackups();

      // Reset state
      setBackupToDelete(null);
      setShowDeleteModal(false);

      // إظهار رسالة النجاح
      setSuccessMessage(`تم حذف النسخة الاحتياطية "${backupName}" بنجاح`);
      setSuccessMessageType('delete');
    } catch (err) {
      console.error('Error deleting backup:', err);
      setErrorMessage('حدث خطأ أثناء حذف النسخة الاحتياطية');
      // تأكد من عدم وجود رسالة نجاح في حالة حدوث خطأ
      setSuccessMessage(null);
    } finally {
      setDeleting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(backups.length / backupsPerPage);

  // الحصول على النسخ الاحتياطية المعروضة في الصفحة الحالية
  const indexOfLastBackup = currentPage * backupsPerPage;
  const indexOfFirstBackup = indexOfLastBackup - backupsPerPage;
  const currentBackups = backups.slice(indexOfFirstBackup, indexOfLastBackup);

  // دالة للانتقال إلى صفحة معينة
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // التمرير إلى أعلى الصفحة عند تغيير الصفحة
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <div>
      {/* Header Section */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">النسخ الاحتياطي والاستعادة</h1>
              <p className="text-gray-600">إدارة النسخ الاحتياطية واستعادة البيانات في النظام</p>
            </div>

            <div className="flex space-x-2 space-x-reverse">
              <button
                onClick={handleDownloadBackup}
                className={`
                  flex items-center justify-center px-5 py-2.5 rounded-lg
                  font-medium text-white transition-all duration-300
                  shadow-md hover:shadow-lg transform hover:-translate-y-0.5
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                  ${downloadingBackup
                    ? 'bg-gradient-to-r from-blue-400 to-blue-500 cursor-wait opacity-80'
                    : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'
                  }
                `}
                disabled={downloadingBackup}
              >
                <div className="relative flex items-center">
                  {downloadingBackup ? (
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  ) : (
                    <FiDownload className="ml-2 h-5 w-5" />
                  )}
                  <span>{downloadingBackup ? 'جاري التنزيل...' : 'إنشاء وتنزيل نسخة احتياطية'}</span>
                </div>
              </button>

              <button
                onClick={() => setShowRestoreModal(true)}
                className={`
                  flex items-center justify-center px-5 py-2.5 rounded-lg
                  font-medium text-white transition-all duration-300
                  shadow-md hover:shadow-lg transform hover:-translate-y-0.5
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500
                  bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700
                `}
              >
                <div className="relative flex items-center">
                  <FiUpload className="ml-2 h-5 w-5" />
                  <span>استعادة نسخة احتياطية</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* رسالة النجاح */}
      {successMessage && (
        <div className={`mb-4 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in
          ${successMessageType === 'add'
            ? 'bg-primary-50 border border-primary-200 text-primary-800'
            : successMessageType === 'download'
            ? 'bg-blue-50 border border-blue-200 text-blue-800'
            : successMessageType === 'restore'
            ? 'bg-amber-50 border border-amber-200 text-amber-800'
            : 'bg-red-50 border border-red-200 text-red-800'
          }`}
        >
          <FiCheckCircle className={`text-xl ml-2 flex-shrink-0
            ${successMessageType === 'add'
              ? 'text-primary-500'
              : successMessageType === 'download'
              ? 'text-blue-500'
              : successMessageType === 'restore'
              ? 'text-amber-500'
              : 'text-red-500'
            }`}
          />
          <div className="flex-grow">{successMessage}</div>
          <button
            onClick={() => setSuccessMessage(null)}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      {/* رسالة الخطأ */}
      {errorMessage && (
        <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in">
          <FiAlertCircle className="text-red-500 text-xl ml-2 flex-shrink-0" />
          <div className="flex-grow">{errorMessage}</div>
          <button
            onClick={() => setErrorMessage(null)}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      {/* Warning */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-t-lg border-b border-yellow-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiAlertTriangle className="w-5 h-5 text-yellow-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">تنبيه هام</h2>
          </div>
        </div>
        <div className="p-4 bg-white rounded-b-lg">
          <p className="text-sm text-yellow-700">
            <span className="font-medium">تنبيه:</span> عملية استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية. تأكد من وجود نسخة احتياطية حديثة قبل الاستعادة.
          </p>
        </div>
      </div>

      {/* Backups List */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiDatabase className="w-5 h-5 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">قائمة النسخ الاحتياطية</h2>
          </div>
        </div>

        <div className="p-4 bg-white rounded-b-lg">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin h-12 w-12 border-4 border-primary-500 rounded-full border-t-transparent mb-4 mx-auto"></div>
              <p className="text-gray-500 font-medium">جاري تحميل النسخ الاحتياطية...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="bg-red-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-red-500 font-medium text-lg mb-4">{error}</p>
              <button
                onClick={fetchBackups}
                className="btn btn-secondary inline-flex items-center px-4 py-2"
              >
                <FiRefreshCw className="ml-2" />
                إعادة المحاولة
              </button>
            </div>
          ) : backups.length === 0 ? (
            <div className="text-center py-8">
              <FiDatabase className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">لا توجد نسخ احتياطية</h3>
              <p className="mt-1 text-gray-500">قم بإنشاء نسخة احتياطية جديدة للبدء.</p>
            </div>
          ) : (
            <div className="overflow-hidden rounded-lg shadow-sm border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم النسخة
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحجم
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الإنشاء
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentBackups.map((backup) => (
                    <tr key={backup.name} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-blue-600 ml-2">
                            <FiDatabase className="w-5 h-5" />
                          </div>
                          <div className="text-sm font-medium text-gray-900">{backup.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatFileSize(backup.size)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatDate(backup.createdAt)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-3 space-x-reverse">
                          <button
                            onClick={() => handleDownloadExistingBackup(backup.name)}
                            className="p-1.5 rounded-full bg-green-50 text-green-600 hover:bg-green-100 transition-colors"
                            title="تنزيل"
                          >
                            <FiDownload className="w-4 h-4" />
                          </button>

                          <button
                            onClick={() => {
                              setBackupToDelete(backup);
                              setShowDeleteModal(true);
                            }}
                            className="p-1.5 rounded-full bg-red-50 text-red-600 hover:bg-red-100 transition-colors"
                            title="حذف"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      عرض {indexOfFirstBackup + 1} إلى {Math.min(indexOfLastBackup, backups.length)} من أصل {backups.length} نسخة احتياطية
                    </div>
                    <div className="flex space-x-1 space-x-reverse">
                      <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <FiChevronRight className="ml-1" />
                        <span>السابق</span>
                      </button>

                      {/* أرقام الصفحات */}
                      {[...Array(totalPages)].map((_, index) => {
                        const pageNumber = index + 1;
                        // عرض أول صفحتين وآخر صفحتين والصفحة الحالية وما حولها
                        if (
                          pageNumber === 1 ||
                          pageNumber === totalPages ||
                          (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                        ) {
                          return (
                            <button
                              key={pageNumber}
                              onClick={() => paginate(pageNumber)}
                              className={`px-3 py-1 rounded-md ${
                                currentPage === pageNumber
                                  ? 'bg-primary-600 text-white'
                                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                              }`}
                            >
                              {pageNumber}
                            </button>
                          );
                        } else if (
                          (pageNumber === 2 && currentPage > 3) ||
                          (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                        ) {
                          // عرض نقاط للصفحات المحذوفة
                          return <span key={pageNumber} className="px-2 py-1">...</span>;
                        }
                        return null;
                      })}

                      <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === totalPages
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <span>التالي</span>
                        <FiChevronLeft className="mr-1" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Restore Backup Modal */}
      {showRestoreModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-amber-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center ml-4 shadow-md">
                    <FiUpload className="h-6 w-6 text-amber-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      استعادة نسخة احتياطية
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      اختر ملف النسخة الاحتياطية (بصيغة ZIP) لاستعادته.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-amber-50 p-4 rounded-lg border border-amber-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <FiAlertTriangle className="h-5 w-5 text-amber-500" />
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-amber-800">تنبيه</h3>
                      <div className="mt-2 text-sm text-amber-700">
                        <p>
                          سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المختارة.
                        </p>
                        <p className="font-bold mt-1">
                          تأكد من اختيار النسخة الاحتياطية الصحيحة.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <form id="restoreForm" onSubmit={handleShowConfirmModal}>
                  <div className="mb-4">
                    <label htmlFor="backup" className="block text-sm font-medium text-gray-700 mb-2">
                      اختر ملف النسخة الاحتياطية
                    </label>
                    <div className="flex flex-col space-y-4">
                      <button
                        type="button"
                        onClick={() => {
                          // إنشاء عنصر input مؤقت
                          const input = document.createElement('input');
                          input.type = 'file';
                          input.accept = '.zip';
                          input.onchange = (e) => {
                            if (e.target.files && e.target.files[0]) {
                              setSelectedFile(e.target.files[0]);
                              setUploadProgress(0);

                              // تخزين الملف في fileInputRef للاستخدام لاحقًا
                              if (fileInputRef.current) {
                                // إنشاء DataTransfer جديد
                                const dataTransfer = new DataTransfer();
                                dataTransfer.items.add(e.target.files[0]);
                                fileInputRef.current.files = dataTransfer.files;
                              }
                            }
                          };
                          input.click();
                        }}
                        className="flex items-center justify-center px-4 py-3 bg-amber-50 text-amber-700 rounded-md border border-amber-200 cursor-pointer hover:bg-amber-100 transition-colors duration-300 w-full"
                        disabled={restoring}
                      >
                        <FiUpload className="ml-2" />
                        <span>اختر ملف النسخة الاحتياطية</span>
                      </button>

                      <input
                        type="file"
                        id="backup"
                        ref={fileInputRef}
                        accept=".zip"
                        className="hidden"
                        disabled={restoring}
                        required
                      />

                      {selectedFile ? (
                        <div className="mt-2 p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <FiFile className="h-10 w-10 text-amber-500" />
                            </div>
                            <div className="mr-3">
                              <h3 className="text-sm font-medium text-gray-800">الملف المختار</h3>
                              <p className="text-sm text-gray-600">{selectedFile.name}</p>
                              <p className="text-xs text-gray-500 mt-1">
                                حجم الملف: {(selectedFile.size / (1024 * 1024)).toFixed(2)} ميجابايت
                              </p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-center p-6 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                          <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
                          <p className="mt-2 text-sm text-gray-600">
                            لم يتم اختيار ملف بعد
                          </p>
                          <p className="mt-1 text-xs text-gray-500">
                            يجب أن يكون الملف بصيغة ZIP
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {restoring && (
                    <div className="mb-4">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-amber-600 h-2.5 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 text-center">{uploadProgress}%</p>
                    </div>
                  )}
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="submit"
                  form="restoreForm"
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${restoring
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 hover:shadow-lg'
                    }
                  `}
                  disabled={restoring}
                >
                  {restoring ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>جاري الاستعادة...</span>
                    </>
                  ) : (
                    <>
                      <FiUpload className="ml-2" />
                      <span>استعادة</span>
                    </>
                  )}
                </button>

                {selectedFile && (
                  <button
                    type="button"
                    onClick={handleValidateBackup}
                    className={`
                      flex items-center justify-center px-4 py-2 rounded-lg border font-medium
                      transition-all duration-300 sm:ml-3 mt-3 sm:mt-0
                      ${validating
                        ? 'bg-gray-100 border-gray-300 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-50 border-blue-300 text-blue-700 hover:bg-blue-100'
                      }
                    `}
                    disabled={validating || restoring}
                  >
                    {validating ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>جاري الفحص...</span>
                      </>
                    ) : (
                      <>
                        <FiCheckCircle className="ml-2 h-4 w-4" />
                        <span>فحص النسخة</span>
                      </>
                    )}
                  </button>
                )}

                <button
                  type="button"
                  onClick={() => setShowRestoreModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                  disabled={restoring}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Confirm Restore Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiAlertTriangle className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      تأكيد استعادة النسخة الاحتياطية
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية. هل أنت متأكد من المتابعة؟
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير هام</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المختارة.
                        </p>
                        <p className="font-bold mt-1">
                          هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {selectedFile && (
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <FiFile className="h-10 w-10 text-amber-500" />
                      </div>
                      <div className="mr-3">
                        <h3 className="text-sm font-medium text-gray-800">الملف المختار</h3>
                        <p className="text-sm text-gray-600">{selectedFile.name}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          حجم الملف: {(selectedFile.size / (1024 * 1024)).toFixed(2)} ميجابايت
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleRestoreBackup}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${restoring
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={restoring}
                >
                  {restoring ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>جاري الاستعادة...</span>
                    </>
                  ) : (
                    <>
                      <FiUpload className="ml-2" />
                      <span>تأكيد الاستعادة</span>
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => setShowConfirmModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                  disabled={restoring}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Backup Modal */}
      {showDeleteModal && backupToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      حذف النسخة الاحتياطية
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      هل أنت متأكد من رغبتك في حذف النسخة الاحتياطية؟
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <FiDatabase className="h-10 w-10 text-blue-500" />
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-gray-800">معلومات النسخة الاحتياطية</h3>
                      <p className="text-sm text-gray-600">{backupToDelete.name}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        الحجم: {formatFileSize(backupToDelete.size)} | تاريخ الإنشاء: {formatDate(backupToDelete.createdAt)}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 p-4 rounded-lg border border-red-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم حذف النسخة الاحتياطية نهائيًا من النظام.
                        </p>
                        <p className="font-bold mt-1">
                          هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleDeleteBackup}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${deleting
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={deleting}
                >
                  {deleting ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span>جاري الحذف...</span>
                    </>
                  ) : (
                    <>
                      <FiTrash2 className="ml-2" />
                      <span>حذف</span>
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setBackupToDelete(null);
                    setShowDeleteModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                  disabled={deleting}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Restore Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-green-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center ml-4 shadow-md">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      تمت الاستعادة بنجاح
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      تم استعادة النسخة الاحتياطية بنجاح.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-green-800">تمت العملية بنجاح</h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>
                          تم استعادة النسخة الاحتياطية <span className="font-bold">{restoredBackupName}</span> بنجاح.
                        </p>
                        <p className="mt-1">
                          يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowSuccessModal(false);
                    // إعادة تحميل الصفحة
                    window.location.href = window.location.href.split('#')[0];
                  }}
                  className="flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 hover:shadow-lg"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                  </svg>
                  <span>إعادة تشغيل التطبيق</span>
                </button>
                <button
                  type="button"
                  onClick={() => setShowSuccessModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Validation Result Modal */}
      {showValidationModal && validationResult && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              {/* Header */}
              <div className={`p-6 rounded-t-lg ${validationResult.valid ? 'bg-gradient-to-r from-green-50 to-white' : 'bg-gradient-to-r from-red-50 to-white'}`}>
                <div className="flex items-center">
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center ml-4 shadow-md ${validationResult.valid ? 'bg-green-100' : 'bg-red-100'}`}>
                    {validationResult.valid ? (
                      <FiCheckCircle className="h-6 w-6 text-green-600" />
                    ) : (
                      <FiAlertTriangle className="h-6 w-6 text-red-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      نتيجة فحص النسخة الاحتياطية
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {validationResult.message}
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {/* Validation Status */}
                <div className={`p-4 rounded-lg border mb-4 ${validationResult.valid ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                  <div className="flex">
                    <div className="flex-shrink-0">
                      {validationResult.valid ? (
                        <FiCheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <FiAlertTriangle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                    <div className="mr-3">
                      <h3 className={`text-sm font-medium ${validationResult.valid ? 'text-green-800' : 'text-red-800'}`}>
                        {validationResult.valid ? 'النسخة الاحتياطية صالحة' : 'النسخة الاحتياطية غير صالحة'}
                      </h3>
                      <div className={`mt-2 text-sm ${validationResult.valid ? 'text-green-700' : 'text-red-700'}`}>
                        <p style={{ whiteSpace: 'pre-line' }}>{validationResult.message}</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* File Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                      <FiDatabase className="ml-2 text-blue-500" />
                      ملف قاعدة البيانات
                    </h4>
                    <div className="flex items-center">
                      {validationResult.database.found ? (
                        <FiCheckCircle className="h-4 w-4 text-green-500 ml-2" />
                      ) : (
                        <FiX className="h-4 w-4 text-red-500 ml-2" />
                      )}
                      <span className={`text-sm ${validationResult.database.found ? 'text-green-700' : 'text-red-700'}`}>
                        {validationResult.database.found ? 'موجود' : 'غير موجود'}
                      </span>
                    </div>
                    {validationResult.database.path && (
                      <p className="text-xs text-gray-500 mt-1">المسار: {validationResult.database.path}</p>
                    )}
                    {validationResult.database.potentialFiles && validationResult.database.potentialFiles.length > 0 && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs font-medium text-yellow-800">ملفات محتملة:</p>
                        {validationResult.database.potentialFiles.map((file, idx) => (
                          <p key={idx} className="text-xs text-yellow-700">⭐ {file.name}</p>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-800 mb-2 flex items-center">
                      <FiFolder className="ml-2 text-blue-500" />
                      مجلد بيانات الموظفين
                    </h4>
                    <div className="flex items-center">
                      {validationResult.employeeData.found ? (
                        <FiCheckCircle className="h-4 w-4 text-green-500 ml-2" />
                      ) : (
                        <FiX className="h-4 w-4 text-red-500 ml-2" />
                      )}
                      <span className={`text-sm ${validationResult.employeeData.found ? 'text-green-700' : 'text-red-700'}`}>
                        {validationResult.employeeData.found ? 'موجود' : 'غير موجود'}
                      </span>
                    </div>
                    {validationResult.employeeData.path && (
                      <p className="text-xs text-gray-500 mt-1">المسار: {validationResult.employeeData.path}</p>
                    )}
                    {validationResult.employeeData.potentialDirs && validationResult.employeeData.potentialDirs.length > 0 && (
                      <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
                        <p className="text-xs font-medium text-yellow-800">مجلدات محتملة:</p>
                        {validationResult.employeeData.potentialDirs.map((dir, idx) => (
                          <p key={idx} className="text-xs text-yellow-700">⭐ {dir.name}</p>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* Contents List */}
                {validationResult.contents && validationResult.contents.length > 0 && (
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h4 className="font-medium text-gray-800 mb-3 flex items-center">
                      <FiList className="ml-2 text-blue-500" />
                      محتويات النسخة الاحتياطية
                    </h4>
                    <div className="max-h-60 overflow-y-auto">
                      {validationResult.contents.map((item, index) => (
                        <div key={index} className={`flex items-center py-1 text-sm rounded px-2 ${
                          item.isPotentialDbFile || item.isPotentialEmployeeDir ? 'bg-yellow-50 border border-yellow-200' : ''
                        }`}>
                          {item.type === 'directory' ? (
                            <FiFolder className={`h-4 w-4 ml-2 ${
                              item.isPotentialEmployeeDir ? 'text-yellow-600' : 'text-blue-500'
                            }`} />
                          ) : (
                            <FiFile className={`h-4 w-4 ml-2 ${
                              item.isPotentialDbFile ? 'text-yellow-600' : 'text-gray-500'
                            }`} />
                          )}
                          <span className={`flex-grow ${
                            item.isPotentialDbFile || item.isPotentialEmployeeDir ? 'text-yellow-800 font-medium' : 'text-gray-700'
                          }`}>
                            {item.path}
                          </span>
                          {(item.isPotentialDbFile || item.isPotentialEmployeeDir) && (
                            <span className="text-yellow-600 text-xs mr-2 font-medium">⭐ محتمل</span>
                          )}
                          {item.size && (
                            <span className="text-gray-500 text-xs mr-2">({(item.size / 1024).toFixed(1)} KB)</span>
                          )}
                        </div>
                      ))}
                    </div>

                    {/* Legend */}
                    <div className="mt-3 pt-3 border-t border-gray-200">
                      <div className="flex items-center text-xs text-gray-600">
                        <span className="text-yellow-600 mr-2">⭐</span>
                        <span>الملفات المميزة بالنجمة قد تكون الملفات المطلوبة</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowValidationModal(false)}
                  className="flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 hover:shadow-lg"
                >
                  <span>إغلاق</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Backup;
