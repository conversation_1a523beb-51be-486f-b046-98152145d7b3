const fs = require('fs-extra');
const path = require('path');
const crypto = require('crypto');

const dataDir = path.join(__dirname, 'backend', 'data', 'employee_database');

// دالة لحساب hash للملف
function getFileHash(filePath) {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    const hashSum = crypto.createHash('md5');
    hashSum.update(fileBuffer);
    return hashSum.digest('hex');
  } catch (error) {
    console.error(`خطأ في حساب hash للملف ${filePath}:`, error.message);
    return null;
  }
}

// دالة لتنظيف أسماء المجلدات
function cleanupDuplicateFolders() {
  console.log('🔍 البحث عن مجلدات الموظفين المكررة...');

  if (!fs.existsSync(dataDir)) {
    console.log('❌ مجلد قاعدة البيانات غير موجود');
    return;
  }

  const folders = fs.readdirSync(dataDir)
    .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory());

  const folderMap = new Map();
  const duplicates = [];

  // تجميع المجلدات حسب الاسم المنظف
  folders.forEach(folder => {
    const cleanName = folder.trim().toLowerCase();
    if (folderMap.has(cleanName)) {
      duplicates.push({
        original: folderMap.get(cleanName),
        duplicate: folder
      });
    } else {
      folderMap.set(cleanName, folder);
    }
  });

  console.log(`📊 تم العثور على ${duplicates.length} مجلد مكرر`);

  // دمج المجلدات المكررة
  duplicates.forEach(({ original, duplicate }) => {
    console.log(`🔄 دمج المجلد "${duplicate}" مع "${original}"`);

    const originalPath = path.join(dataDir, original);
    const duplicatePath = path.join(dataDir, duplicate);

    try {
      // نسخ جميع الملفات من المجلد المكرر إلى المجلد الأصلي
      const duplicateFiles = fs.readdirSync(duplicatePath);

      duplicateFiles.forEach(file => {
        const srcFile = path.join(duplicatePath, file);
        const destFile = path.join(originalPath, file);

        if (!fs.existsSync(destFile)) {
          fs.copyFileSync(srcFile, destFile);
          console.log(`  ✅ تم نسخ الملف: ${file}`);
        } else {
          console.log(`  ⚠️  الملف موجود بالفعل: ${file}`);
        }
      });

      // حذف المجلد المكرر
      fs.removeSync(duplicatePath);
      console.log(`  🗑️  تم حذف المجلد المكرر: ${duplicate}`);

    } catch (error) {
      console.error(`❌ خطأ في دمج المجلد ${duplicate}:`, error.message);
    }
  });
}

// دالة لتنظيف الملفات المكررة
function cleanupDuplicateFiles() {
  console.log('\n🔍 البحث عن الملفات المكررة...');

  const fileHashes = new Map(); // hash -> {path, name, size}
  const duplicateFiles = [];

  // فحص جميع مجلدات الموظفين
  const folders = fs.readdirSync(dataDir)
    .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory());

  folders.forEach(folder => {
    const folderPath = path.join(dataDir, folder);

    try {
      const files = fs.readdirSync(folderPath)
        .filter(file => fs.statSync(path.join(folderPath, file)).isFile());

      files.forEach(file => {
        const filePath = path.join(folderPath, file);
        const fileHash = getFileHash(filePath);

        if (fileHash) {
          const fileStats = fs.statSync(filePath);
          const fileInfo = {
            path: filePath,
            folder: folder,
            name: file,
            size: fileStats.size,
            hash: fileHash
          };

          if (fileHashes.has(fileHash)) {
            // ملف مكرر
            duplicateFiles.push({
              original: fileHashes.get(fileHash),
              duplicate: fileInfo
            });
          } else {
            fileHashes.set(fileHash, fileInfo);
          }
        }
      });
    } catch (error) {
      console.error(`❌ خطأ في فحص مجلد ${folder}:`, error.message);
    }
  });

  console.log(`📊 تم العثور على ${duplicateFiles.length} ملف مكرر`);

  // حذف الملفات المكررة
  duplicateFiles.forEach(({ original, duplicate }) => {
    console.log(`🗑️  حذف الملف المكرر: ${duplicate.name} من مجلد "${duplicate.folder}"`);
    console.log(`   الملف الأصلي في: "${original.folder}"`);

    try {
      fs.removeSync(duplicate.path);
      console.log(`   ✅ تم الحذف بنجاح`);
    } catch (error) {
      console.error(`   ❌ خطأ في الحذف:`, error.message);
    }
  });
}

// دالة لإصلاح ترميز أسماء الملفات
function fixFileEncoding() {
  console.log('\n🔧 إصلاح ترميز أسماء الملفات...');

  const folders = fs.readdirSync(dataDir)
    .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory());

  let fixedCount = 0;

  folders.forEach(folder => {
    const folderPath = path.join(dataDir, folder);

    try {
      const files = fs.readdirSync(folderPath);

      files.forEach(file => {
        // التحقق من وجود أحرف ترميز خاطئة
        if (file.includes('Ã') || file.includes('Â') || file.includes('Ø') || file.includes('Ù')) {
          const oldPath = path.join(folderPath, file);

          // محاولة إصلاح الترميز
          try {
            const decodedName = decodeURIComponent(file.replace(/Ã/g, '%C3').replace(/Â/g, '%C2'));
            const newPath = path.join(folderPath, decodedName);

            if (oldPath !== newPath && !fs.existsSync(newPath)) {
              fs.renameSync(oldPath, newPath);
              console.log(`  ✅ تم إصلاح: ${file} -> ${decodedName}`);
              fixedCount++;
            }
          } catch (error) {
            console.log(`  ⚠️  لا يمكن إصلاح: ${file}`);
          }
        }
      });
    } catch (error) {
      console.error(`❌ خطأ في إصلاح ترميز مجلد ${folder}:`, error.message);
    }
  });

  console.log(`📊 تم إصلاح ${fixedCount} ملف`);
}

// دالة لعرض إحصائيات النظام
function showStatistics() {
  console.log('\n📊 إحصائيات النظام:');

  const folders = fs.readdirSync(dataDir)
    .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory());

  let totalFiles = 0;
  let totalSize = 0;

  folders.forEach(folder => {
    const folderPath = path.join(dataDir, folder);

    try {
      const files = fs.readdirSync(folderPath)
        .filter(file => fs.statSync(path.join(folderPath, file)).isFile());

      const folderSize = files.reduce((size, file) => {
        const filePath = path.join(folderPath, file);
        return size + fs.statSync(filePath).size;
      }, 0);

      totalFiles += files.length;
      totalSize += folderSize;

      console.log(`  📁 ${folder}: ${files.length} ملف (${(folderSize / 1024 / 1024).toFixed(2)} MB)`);
    } catch (error) {
      console.error(`❌ خطأ في فحص مجلد ${folder}:`, error.message);
    }
  });

  console.log(`\n📈 الإجمالي:`);
  console.log(`  👥 عدد الموظفين: ${folders.length}`);
  console.log(`  📄 إجمالي الملفات: ${totalFiles}`);
  console.log(`  💾 الحجم الإجمالي: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
}

// تشغيل عملية التنظيف
async function runCleanup() {
  console.log('🚀 بدء عملية تنظيف النظام...\n');

  try {
    // 1. تنظيف المجلدات المكررة
    cleanupDuplicateFolders();

    // 2. تنظيف الملفات المكررة
    cleanupDuplicateFiles();

    // 3. إصلاح ترميز أسماء الملفات
    fixFileEncoding();

    // 4. عرض الإحصائيات النهائية
    showStatistics();

    console.log('\n✅ تم الانتهاء من تنظيف النظام بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في عملية التنظيف:', error.message);
  }
}

// تشغيل الأداة
if (require.main === module) {
  console.log('Starting cleanup process...');
  runCleanup().catch(error => {
    console.error('Error in cleanup process:', error);
    process.exit(1);
  });
}

module.exports = {
  cleanupDuplicateFolders,
  cleanupDuplicateFiles,
  fixFileEncoding,
  showStatistics,
  runCleanup
};
