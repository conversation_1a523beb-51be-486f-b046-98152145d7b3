// أداة فحص النسخ الاحتياطية الموجودة
// يمكن استخدام هذا الملف لفحص نسخة احتياطية موجودة

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

// تحديد مسار النسخة الاحتياطية المراد فحصها
const backupPath = process.argv[2];

if (!backupPath) {
  console.log('الاستخدام: node check-existing-backup.js <مسار_النسخة_الاحتياطية>');
  console.log('مثال: node check-existing-backup.js ./backup-2024-01-01.zip');
  process.exit(1);
}

console.log('=== فحص النسخة الاحتياطية الموجودة ===\n');
console.log(`النسخة الاحتياطية: ${backupPath}\n`);

// 1. التحقق من وجود الملف
if (!fs.existsSync(backupPath)) {
  console.log('❌ الملف غير موجود');
  process.exit(1);
}

const stats = fs.statSync(backupPath);
if (!stats.isFile()) {
  console.log('❌ المسار المحدد ليس ملفاً');
  process.exit(1);
}

console.log(`✅ الملف موجود (${stats.size} بايت)`);

// 2. التحقق من امتداد الملف
if (!backupPath.toLowerCase().endsWith('.zip')) {
  console.log('⚠️ تحذير: الملف لا ينتهي بامتداد .zip');
}

// 3. استخراج النسخة الاحتياطية وفحص محتوياتها
const extractDir = path.join(__dirname, 'temp-check-extract');

try {
  // إزالة مجلد الاستخراج السابق إن وجد
  if (fs.existsSync(extractDir)) {
    fs.removeSync(extractDir);
  }

  fs.ensureDirSync(extractDir);

  console.log('\nجاري استخراج النسخة الاحتياطية...');

  // استخراج النسخة الاحتياطية
  execSync(`powershell -command "Expand-Archive -Path '${backupPath}' -DestinationPath '${extractDir}' -Force"`, {
    stdio: 'inherit'
  });

  console.log('✅ تم استخراج النسخة الاحتياطية بنجاح\n');

  // 4. فحص محتويات النسخة الاحتياطية
  console.log('محتويات النسخة الاحتياطية:');

  const listContents = (dir, prefix = '') => {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        console.log(`${prefix}📁 ${entry.name}/`);
        if (prefix.length < 8) { // تحديد العمق
          listContents(fullPath, prefix + '  ');
        }
      } else {
        const fileStats = fs.statSync(fullPath);
        console.log(`${prefix}📄 ${entry.name} (${fileStats.size} بايت)`);
      }
    }
  };

  listContents(extractDir);

  // 5. البحث عن الملفات المطلوبة
  console.log('\n=== فحص الملفات المطلوبة ===');

  const findFileOrDir = (dir, nameToFind, isDir = false) => {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      // فحص مطابقة دقيقة
      if (entry.name === nameToFind && entry.isDirectory() === isDir) {
        return fullPath;
      }

      // فحص متغيرات لملف قاعدة البيانات
      if (!isDir && nameToFind === 'archive.db') {
        if (entry.name.endsWith('.db') && entry.name.includes('archive')) {
          return fullPath;
        }
      }

      // فحص متغيرات لمجلد الموظفين
      if (isDir && nameToFind === 'employee_database') {
        if (entry.name.toLowerCase().includes('employee') ||
            entry.name.toLowerCase().includes('data') ||
            entry.name === 'employees') {
          return fullPath;
        }
      }

      // البحث في المجلدات الفرعية
      if (entry.isDirectory()) {
        const found = findFileOrDir(fullPath, nameToFind, isDir);
        if (found) return found;
      }
    }
    return null;
  };

  const dbPath = findFileOrDir(extractDir, 'archive.db', false);
  const employeeDir = findFileOrDir(extractDir, 'employee_database', true);

  console.log(`ملف قاعدة البيانات (archive.db): ${dbPath ? '✅ موجود' : '❌ غير موجود'}`);
  if (dbPath) {
    console.log(`  المسار: ${dbPath}`);
    const dbStats = fs.statSync(dbPath);
    console.log(`  الحجم: ${dbStats.size} بايت`);
  }

  console.log(`مجلد بيانات الموظفين (employee_database): ${employeeDir ? '✅ موجود' : '❌ غير موجود'}`);
  if (employeeDir) {
    console.log(`  المسار: ${employeeDir}`);
    try {
      const employees = fs.readdirSync(employeeDir);
      console.log(`  عدد المجلدات الفرعية: ${employees.length}`);
      if (employees.length > 0) {
        console.log(`  أمثلة: ${employees.slice(0, 5).join(', ')}`);
      }
    } catch (err) {
      console.log(`  خطأ في قراءة المحتويات: ${err.message}`);
    }
  }

  // 6. تقييم صحة النسخة الاحتياطية
  console.log('\n=== تقييم النسخة الاحتياطية ===');

  if (dbPath && employeeDir) {
    console.log('✅ النسخة الاحتياطية صالحة - تحتوي على جميع الملفات المطلوبة');
  } else {
    console.log('❌ النسخة الاحتياطية غير صالحة - ملفات مفقودة:');
    if (!dbPath) {
      console.log('  - ملف قاعدة البيانات (archive.db)');
    }
    if (!employeeDir) {
      console.log('  - مجلد بيانات الموظفين (employee_database)');
    }
    
    console.log('\nاقتراحات:');
    console.log('1. تأكد من أن النسخة الاحتياطية تم إنشاؤها بواسطة هذا النظام');
    console.log('2. جرب إنشاء نسخة احتياطية جديدة من النظام');
    console.log('3. تحقق من أن النسخة الاحتياطية لم تتلف أثناء النقل');
  }

} catch (err) {
  console.log(`❌ فشل في فحص النسخة الاحتياطية: ${err.message}`);
} finally {
  // تنظيف مجلد الاستخراج
  try {
    if (fs.existsSync(extractDir)) {
      fs.removeSync(extractDir);
      console.log('\n✅ تم تنظيف الملفات المؤقتة');
    }
  } catch (cleanupErr) {
    console.log(`\n⚠️ تحذير: فشل في تنظيف الملفات المؤقتة: ${cleanupErr.message}`);
  }
}

console.log('\n=== انتهاء الفحص ===');
