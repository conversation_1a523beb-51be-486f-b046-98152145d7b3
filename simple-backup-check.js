// أداة تشخيص مبسطة للنسخة الاحتياطية (بدون مكتبات إضافية)
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const backupPath = process.argv[2];

if (!backupPath) {
  console.log('❌ يرجى تحديد مسار النسخة الاحتياطية');
  console.log('الاستخدام: node simple-backup-check.js <مسار_النسخة_الاحتياطية>');
  process.exit(1);
}

console.log('🔧 أداة تشخيص النسخة الاحتياطية المبسطة');
console.log('==========================================\n');

console.log(`📁 النسخة الاحتياطية: ${backupPath}`);

// التحقق من وجود الملف
if (!fs.existsSync(backupPath)) {
  console.log('❌ الملف غير موجود');
  process.exit(1);
}

const stats = fs.statSync(backupPath);
console.log(`📏 حجم الملف: ${(stats.size / (1024 * 1024)).toFixed(2)} ميجابايت`);
console.log(`📅 تاريخ التعديل: ${stats.mtime.toLocaleString('ar')}\n`);

// إنشاء مجلد مؤقت للاستخراج
const extractDir = path.join(__dirname, 'simple-extract');

try {
  // تنظيف المجلد المؤقت إذا كان موجوداً
  if (fs.existsSync(extractDir)) {
    console.log('🧹 تنظيف المجلد المؤقت...');
    
    // حذف المحتويات بشكل تكراري
    const deleteRecursive = (dirPath) => {
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        files.forEach(file => {
          const filePath = path.join(dirPath, file);
          if (fs.statSync(filePath).isDirectory()) {
            deleteRecursive(filePath);
          } else {
            fs.unlinkSync(filePath);
          }
        });
        fs.rmdirSync(dirPath);
      }
    };
    
    deleteRecursive(extractDir);
  }
  
  fs.mkdirSync(extractDir, { recursive: true });
  console.log('✅ تم إنشاء المجلد المؤقت');

  // استخراج النسخة الاحتياطية
  console.log('\n📦 جاري استخراج النسخة الاحتياطية...');
  
  try {
    execSync(`powershell -command "Expand-Archive -Path '${backupPath}' -DestinationPath '${extractDir}' -Force"`, {
      stdio: 'pipe'
    });
    console.log('✅ تم الاستخراج بنجاح');
  } catch (extractError) {
    console.error('❌ فشل في الاستخراج:', extractError.message);
    
    // جرب طريقة أخرى
    console.log('🔄 جاري المحاولة بطريقة أخرى...');
    try {
      execSync(`powershell -command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('${backupPath}', '${extractDir}')"`, {
        stdio: 'pipe'
      });
      console.log('✅ تم الاستخراج بالطريقة البديلة');
    } catch (altError) {
      console.error('❌ فشل في الاستخراج بالطريقة البديلة:', altError.message);
      throw new Error('فشل في استخراج النسخة الاحتياطية بجميع الطرق');
    }
  }

  // فحص المحتويات المستخرجة
  console.log('\n🔍 فحص المحتويات المستخرجة...');
  
  const rootEntries = fs.readdirSync(extractDir, { withFileTypes: true });
  console.log(`📋 عدد العناصر في الجذر: ${rootEntries.length}`);
  
  if (rootEntries.length === 0) {
    console.log('❌ المجلد فارغ! قد تكون النسخة الاحتياطية تالفة');
    process.exit(1);
  }

  console.log('\n📂 محتويات الجذر:');
  rootEntries.forEach(entry => {
    console.log(`   ${entry.isDirectory() ? '📁' : '📄'} ${entry.name}`);
  });

  // البحث الشامل
  console.log('\n🔍 البحث الشامل في جميع المستويات...');
  
  const allFiles = [];
  const allDirs = [];
  
  const scanRecursively = (dir, relativePath = '', depth = 0) => {
    if (depth > 10) return;
    
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relPath = relativePath ? `${relativePath}/${entry.name}` : entry.name;
        
        if (entry.isDirectory()) {
          allDirs.push({
            name: entry.name,
            path: relPath,
            fullPath: fullPath,
            depth: depth
          });
          
          console.log(`${'  '.repeat(depth)}📁 ${relPath}/`);
          
          if (depth < 5) {
            scanRecursively(fullPath, relPath, depth + 1);
          }
        } else {
          const fileStats = fs.statSync(fullPath);
          allFiles.push({
            name: entry.name,
            path: relPath,
            fullPath: fullPath,
            size: fileStats.size,
            depth: depth
          });
          
          console.log(`${'  '.repeat(depth)}📄 ${relPath} (${(fileStats.size / 1024).toFixed(1)} KB)`);
        }
      }
    } catch (err) {
      console.error(`❌ خطأ في قراءة ${dir}:`, err.message);
    }
  };
  
  scanRecursively(extractDir);
  
  // تحليل النتائج
  console.log('\n📊 تحليل النتائج:');
  console.log(`📄 إجمالي الملفات: ${allFiles.length}`);
  console.log(`📁 إجمالي المجلدات: ${allDirs.length}`);
  
  // البحث عن ملفات قاعدة البيانات
  console.log('\n🎯 البحث عن ملفات قاعدة البيانات:');
  const dbFiles = allFiles.filter(f => {
    const name = f.name.toLowerCase();
    return name.endsWith('.db') || 
           name.includes('archive') || 
           name.includes('database') ||
           name.includes('db');
  });
  
  if (dbFiles.length > 0) {
    console.log(`✅ وُجد ${dbFiles.length} ملف محتمل لقاعدة البيانات:`);
    dbFiles.forEach(f => {
      console.log(`   📄 ${f.path} (${(f.size / 1024).toFixed(1)} KB)`);
      
      if (f.name === 'archive.db') {
        console.log(`      ⭐ هذا هو الملف المطلوب بالضبط!`);
      } else if (f.name.endsWith('.db')) {
        console.log(`      💡 ملف قاعدة بيانات محتمل`);
      }
    });
  } else {
    console.log('❌ لم يتم العثور على أي ملفات قاعدة بيانات');
  }
  
  // البحث عن مجلدات بيانات الموظفين
  console.log('\n🎯 البحث عن مجلدات بيانات الموظفين:');
  const employeeDirs = allDirs.filter(d => {
    const name = d.name.toLowerCase();
    return name.includes('employee') ||
           name.includes('data') ||
           name === 'employees' ||
           name === 'uploads' ||
           name === 'files';
  });
  
  if (employeeDirs.length > 0) {
    console.log(`✅ وُجد ${employeeDirs.length} مجلد محتمل لبيانات الموظفين:`);
    employeeDirs.forEach(d => {
      console.log(`   📁 ${d.path}/`);
      
      try {
        const contents = fs.readdirSync(d.fullPath);
        console.log(`      📊 يحتوي على ${contents.length} عنصر`);
        
        if (d.name === 'employee_database') {
          console.log(`      ⭐ هذا هو المجلد المطلوب بالضبط!`);
        } else {
          console.log(`      💡 مجلد محتمل لبيانات الموظفين`);
        }
      } catch (err) {
        console.log(`      ❌ خطأ في قراءة المجلد: ${err.message}`);
      }
    });
  } else {
    console.log('❌ لم يتم العثور على أي مجلدات لبيانات الموظفين');
  }
  
  // الخلاصة
  console.log('\n🎯 الخلاصة:');
  console.log('============');
  
  const hasExactDb = allFiles.some(f => f.name === 'archive.db');
  const hasExactEmployeeDir = allDirs.some(d => d.name === 'employee_database');
  
  if (hasExactDb && hasExactEmployeeDir) {
    console.log('✅ النسخة الاحتياطية تحتوي على الملفات المطلوبة بالأسماء الصحيحة');
    console.log('💡 المشكلة قد تكون في كود البحث أو مسارات الملفات');
  } else if (dbFiles.length > 0 || employeeDirs.length > 0) {
    console.log('⚠️ النسخة الاحتياطية تحتوي على ملفات محتملة لكن بأسماء مختلفة');
    console.log('💡 قد تحتاج لإعادة تسمية الملفات أو تحسين كود البحث');
    
    if (!hasExactDb && dbFiles.length > 0) {
      console.log(`   🔧 اقتراح: إعادة تسمية "${dbFiles[0].name}" إلى "archive.db"`);
    }
    if (!hasExactEmployeeDir && employeeDirs.length > 0) {
      console.log(`   🔧 اقتراح: إعادة تسمية "${employeeDirs[0].name}" إلى "employee_database"`);
    }
  } else {
    console.log('❌ النسخة الاحتياطية لا تحتوي على الملفات المطلوبة');
    console.log('💡 قد تكون النسخة الاحتياطية تالفة أو من نظام مختلف');
    console.log('🔧 اقتراح: إنشاء نسخة احتياطية جديدة من النظام الحالي');
  }

} catch (err) {
  console.error('❌ خطأ في التشخيص:', err.message);
} finally {
  // تنظيف المجلد المؤقت
  try {
    if (fs.existsSync(extractDir)) {
      const deleteRecursive = (dirPath) => {
        if (fs.existsSync(dirPath)) {
          const files = fs.readdirSync(dirPath);
          files.forEach(file => {
            const filePath = path.join(dirPath, file);
            if (fs.statSync(filePath).isDirectory()) {
              deleteRecursive(filePath);
            } else {
              fs.unlinkSync(filePath);
            }
          });
          fs.rmdirSync(dirPath);
        }
      };
      
      deleteRecursive(extractDir);
      console.log('\n🧹 تم تنظيف الملفات المؤقتة');
    }
  } catch (cleanupErr) {
    console.log('\n⚠️ تحذير: فشل في تنظيف الملفات المؤقتة:', cleanupErr.message);
  }
}

console.log('\n==========================================');
console.log('انتهى التشخيص');
