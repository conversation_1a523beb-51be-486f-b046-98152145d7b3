// Simple Backup Diagnostic Tool (without additional libraries)
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const backupPath = process.argv[2];

if (!backupPath) {
  console.log('❌ Please specify the backup file path');
  console.log('Usage: node simple-backup-check.js <backup_file_path>');
  process.exit(1);
}

console.log('🔧 Simple Backup Diagnostic Tool');
console.log('==========================================\n');

console.log(`📁 Backup file: ${backupPath}`);

// Check if file exists
if (!fs.existsSync(backupPath)) {
  console.log('❌ File not found');
  process.exit(1);
}

const stats = fs.statSync(backupPath);
console.log(`📏 File size: ${(stats.size / (1024 * 1024)).toFixed(2)} MB`);
console.log(`📅 Last modified: ${stats.mtime.toLocaleString()}\n`);

// Create temporary directory for extraction
const extractDir = path.join(__dirname, 'simple-extract');

try {
  // Clean temporary directory if it exists
  if (fs.existsSync(extractDir)) {
    console.log('🧹 Cleaning temporary directory...');

    // Delete contents recursively
    const deleteRecursive = (dirPath) => {
      if (fs.existsSync(dirPath)) {
        const files = fs.readdirSync(dirPath);
        files.forEach(file => {
          const filePath = path.join(dirPath, file);
          if (fs.statSync(filePath).isDirectory()) {
            deleteRecursive(filePath);
          } else {
            fs.unlinkSync(filePath);
          }
        });
        fs.rmdirSync(dirPath);
      }
    };

    deleteRecursive(extractDir);
  }

  fs.mkdirSync(extractDir, { recursive: true });
  console.log('✅ Temporary directory created');

  // Extract backup file
  console.log('\n📦 Extracting backup file...');

  try {
    execSync(`powershell -command "Expand-Archive -Path '${backupPath}' -DestinationPath '${extractDir}' -Force"`, {
      stdio: 'pipe'
    });
    console.log('✅ Extraction successful');
  } catch (extractError) {
    console.error('❌ Extraction failed:', extractError.message);

    // Try alternative method
    console.log('🔄 Trying alternative method...');
    try {
      execSync(`powershell -command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::ExtractToDirectory('${backupPath}', '${extractDir}')"`, {
        stdio: 'pipe'
      });
      console.log('✅ Extraction successful with alternative method');
    } catch (altError) {
      console.error('❌ Alternative extraction failed:', altError.message);
      throw new Error('Failed to extract backup with all methods');
    }
  }

  // Check extracted contents
  console.log('\n🔍 Checking extracted contents...');

  const rootEntries = fs.readdirSync(extractDir, { withFileTypes: true });
  console.log(`📋 Number of items in root: ${rootEntries.length}`);

  if (rootEntries.length === 0) {
    console.log('❌ Directory is empty! Backup might be corrupted');
    process.exit(1);
  }

  console.log('\n📂 Root contents:');
  rootEntries.forEach(entry => {
    console.log(`   ${entry.isDirectory() ? '📁' : '📄'} ${entry.name}`);
  });

  // Comprehensive search
  console.log('\n🔍 Comprehensive search at all levels...');

  const allFiles = [];
  const allDirs = [];

  const scanRecursively = (dir, relativePath = '', depth = 0) => {
    if (depth > 10) return;

    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relPath = relativePath ? `${relativePath}/${entry.name}` : entry.name;

        if (entry.isDirectory()) {
          allDirs.push({
            name: entry.name,
            path: relPath,
            fullPath: fullPath,
            depth: depth
          });

          console.log(`${'  '.repeat(depth)}📁 ${relPath}/`);

          if (depth < 5) {
            scanRecursively(fullPath, relPath, depth + 1);
          }
        } else {
          const fileStats = fs.statSync(fullPath);
          allFiles.push({
            name: entry.name,
            path: relPath,
            fullPath: fullPath,
            size: fileStats.size,
            depth: depth
          });

          console.log(`${'  '.repeat(depth)}📄 ${relPath} (${(fileStats.size / 1024).toFixed(1)} KB)`);
        }
      }
    } catch (err) {
      console.error(`❌ Error reading ${dir}:`, err.message);
    }
  };

  scanRecursively(extractDir);

  // Analyze results
  console.log('\n📊 Results Analysis:');
  console.log(`📄 Total files: ${allFiles.length}`);
  console.log(`📁 Total directories: ${allDirs.length}`);

  // Search for database files
  console.log('\n🎯 Searching for database files:');
  const dbFiles = allFiles.filter(f => {
    const name = f.name.toLowerCase();
    return name.endsWith('.db') ||
           name.includes('archive') ||
           name.includes('database') ||
           name.includes('db');
  });

  if (dbFiles.length > 0) {
    console.log(`✅ Found ${dbFiles.length} potential database file(s):`);
    dbFiles.forEach(f => {
      console.log(`   📄 ${f.path} (${(f.size / 1024).toFixed(1)} KB)`);

      if (f.name === 'archive.db') {
        console.log(`      ⭐ This is the exact required file!`);
      } else if (f.name.endsWith('.db')) {
        console.log(`      💡 Potential database file`);
      }
    });
  } else {
    console.log('❌ No database files found');
  }

  // Search for employee data directories
  console.log('\n🎯 Searching for employee data directories:');
  const employeeDirs = allDirs.filter(d => {
    const name = d.name.toLowerCase();
    return name.includes('employee') ||
           name.includes('data') ||
           name === 'employees' ||
           name === 'uploads' ||
           name === 'files';
  });

  if (employeeDirs.length > 0) {
    console.log(`✅ Found ${employeeDirs.length} potential employee data directory(ies):`);
    employeeDirs.forEach(d => {
      console.log(`   📁 ${d.path}/`);

      try {
        const contents = fs.readdirSync(d.fullPath);
        console.log(`      📊 Contains ${contents.length} item(s)`);

        if (d.name === 'employee_database') {
          console.log(`      ⭐ This is the exact required directory!`);
        } else {
          console.log(`      💡 Potential employee data directory`);
        }
      } catch (err) {
        console.log(`      ❌ Error reading directory: ${err.message}`);
      }
    });
  } else {
    console.log('❌ No employee data directories found');
  }

  // Summary
  console.log('\n🎯 Summary:');
  console.log('============');

  const hasExactDb = allFiles.some(f => f.name === 'archive.db');
  const hasExactEmployeeDir = allDirs.some(d => d.name === 'employee_database');

  if (hasExactDb && hasExactEmployeeDir) {
    console.log('✅ Backup contains required files with correct names');
    console.log('💡 Issue might be in search code or file paths');
  } else if (dbFiles.length > 0 || employeeDirs.length > 0) {
    console.log('⚠️ Backup contains potential files but with different names');
    console.log('💡 May need to rename files or improve search code');

    if (!hasExactDb && dbFiles.length > 0) {
      console.log(`   🔧 Suggestion: Rename "${dbFiles[0].name}" to "archive.db"`);
    }
    if (!hasExactEmployeeDir && employeeDirs.length > 0) {
      console.log(`   🔧 Suggestion: Rename "${employeeDirs[0].name}" to "employee_database"`);
    }
  } else {
    console.log('❌ Backup does not contain required files');
    console.log('💡 Backup might be corrupted or from different system');
    console.log('🔧 Suggestion: Create new backup from current system');
  }

} catch (err) {
  console.error('❌ Diagnostic error:', err.message);
} finally {
  // Clean temporary directory
  try {
    if (fs.existsSync(extractDir)) {
      const deleteRecursive = (dirPath) => {
        if (fs.existsSync(dirPath)) {
          const files = fs.readdirSync(dirPath);
          files.forEach(file => {
            const filePath = path.join(dirPath, file);
            if (fs.statSync(filePath).isDirectory()) {
              deleteRecursive(filePath);
            } else {
              fs.unlinkSync(filePath);
            }
          });
          fs.rmdirSync(dirPath);
        }
      };

      deleteRecursive(extractDir);
      console.log('\n🧹 Temporary files cleaned');
    }
  } catch (cleanupErr) {
    console.log('\n⚠️ Warning: Failed to clean temporary files:', cleanupErr.message);
  }
}

console.log('\n==========================================');
console.log('Diagnosis completed');
