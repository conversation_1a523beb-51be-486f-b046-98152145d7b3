import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import {
  FiDatabase,
  FiDownload,
  FiUpload,
  FiTrash2,
  FiRefreshCw,
  FiAlertTriangle
} from 'react-icons/fi';

const Backup = () => {
  const [backups, setBackups] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [creatingBackup, setCreatingBackup] = useState(false);
  const [showRestoreModal, setShowRestoreModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [backupToDelete, setBackupToDelete] = useState(null);
  const [restoring, setRestoring] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const fileInputRef = useRef(null);

  useEffect(() => {
    fetchBackups();
  }, []);

  const fetchBackups = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/backup');
      setBackups(response.data);
    } catch (err) {
      console.error('Error fetching backups:', err);
      setError('حدث خطأ أثناء جلب النسخ الاحتياطية');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateBackup = async () => {
    try {
      setCreatingBackup(true);

      await axios.post('/api/backup/create');

      // Refresh backups list
      fetchBackups();
    } catch (err) {
      console.error('Error creating backup:', err);
      alert('حدث خطأ أثناء إنشاء النسخة الاحتياطية');
    } finally {
      setCreatingBackup(false);
    }
  };

  const handleRestoreBackup = async (e) => {
    e.preventDefault();

    const file = fileInputRef.current.files[0];

    if (!file) {
      alert('يرجى اختيار ملف النسخة الاحتياطية');
      return;
    }

    if (!file.name.endsWith('.zip')) {
      alert('يرجى اختيار ملف بصيغة ZIP');
      return;
    }

    if (!confirm('سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية. هل أنت متأكد من المتابعة؟')) {
      return;
    }

    try {
      setRestoring(true);
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('backup', file);

      await axios.post('/api/backup/restore', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        }
      });

      // Refresh backups list
      fetchBackups();

      // Reset form
      fileInputRef.current.value = null;
      setShowRestoreModal(false);

      alert('تم استعادة النسخة الاحتياطية بنجاح. قد تحتاج إلى إعادة تسجيل الدخول.');

      // Reload the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err) {
      console.error('Error restoring backup:', err);
      alert(err.response?.data?.message || 'حدث خطأ أثناء استعادة النسخة الاحتياطية');
    } finally {
      setRestoring(false);
    }
  };

  const handleDeleteBackup = async () => {
    if (!backupToDelete) return;

    try {
      setDeleting(true);

      await axios.delete(`/api/backup/${backupToDelete.name}`);

      // Refresh backups list
      fetchBackups();

      // Reset state
      setBackupToDelete(null);
      setShowDeleteModal(false);
    } catch (err) {
      console.error('Error deleting backup:', err);
      alert('حدث خطأ أثناء حذف النسخة الاحتياطية');
    } finally {
      setDeleting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">النسخ الاحتياطي والاستعادة</h1>

        <div className="flex space-x-2 space-x-reverse">
          <button
            onClick={handleCreateBackup}
            className="btn btn-primary flex items-center"
            disabled={creatingBackup}
          >
            <FiDatabase className="ml-1" />
            {creatingBackup ? 'جاري الإنشاء...' : 'إنشاء نسخة احتياطية'}
          </button>

          <button
            onClick={() => setShowRestoreModal(true)}
            className="btn btn-secondary flex items-center"
          >
            <FiUpload className="ml-1" />
            استعادة نسخة احتياطية
          </button>
        </div>
      </div>

      {/* Warning */}
      <div className="bg-yellow-50 border-r-4 border-yellow-400 p-4 mb-6 rounded-md">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiAlertTriangle className="h-5 w-5 text-yellow-400" />
          </div>
          <div className="mr-3">
            <p className="text-sm text-yellow-700">
              <span className="font-medium">تنبيه:</span> عملية استعادة النسخة الاحتياطية ستؤدي إلى استبدال جميع البيانات الحالية. تأكد من وجود نسخة احتياطية حديثة قبل الاستعادة.
            </p>
          </div>
        </div>
      </div>

      {/* Backups List */}
      {loading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">جاري تحميل النسخ الاحتياطية...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <button
            onClick={fetchBackups}
            className="mt-2 btn btn-secondary flex items-center mx-auto"
          >
            <FiRefreshCw className="ml-1" />
            إعادة المحاولة
          </button>
        </div>
      ) : backups.length === 0 ? (
        <div className="text-center py-8">
          <FiDatabase className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">لا توجد نسخ احتياطية</h3>
          <p className="mt-1 text-gray-500">قم بإنشاء نسخة احتياطية جديدة للبدء.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  اسم النسخة
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحجم
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {backups.map((backup) => (
                <tr key={backup.name}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <FiDatabase className="ml-2 text-gray-500" />
                      <div className="text-sm font-medium text-gray-900">{backup.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{formatFileSize(backup.size)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{formatDate(backup.createdAt)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2 space-x-reverse">
                      <a
                        href={`/api/backup/${backup.name}`}
                        download
                        className="text-green-600 hover:text-green-900"
                        title="تنزيل"
                      >
                        <FiDownload />
                      </a>

                      <button
                        onClick={() => {
                          setBackupToDelete(backup);
                          setShowDeleteModal(true);
                        }}
                        className="text-red-600 hover:text-red-900"
                        title="حذف"
                      >
                        <FiTrash2 />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Restore Backup Modal */}
      {showRestoreModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      استعادة نسخة احتياطية
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        اختر ملف النسخة الاحتياطية (بصيغة ZIP) لاستعادته. سيتم استبدال جميع البيانات الحالية.
                      </p>
                      <p className="text-sm text-red-500 mt-2">
                        تحذير: هذا الإجراء لا يمكن التراجع عنه.
                      </p>
                    </div>
                  </div>
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 sm:mx-0 sm:h-10 sm:w-10">
                    <FiUpload className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>

                <form onSubmit={handleRestoreBackup} className="mt-4">
                  <div className="mb-4">
                    <label htmlFor="backup" className="block text-sm font-medium text-gray-700">
                      اختر ملف النسخة الاحتياطية
                    </label>
                    <input
                      type="file"
                      id="backup"
                      ref={fileInputRef}
                      accept=".zip"
                      className="mt-1 block w-full text-sm text-gray-500
                        file:ml-4 file:py-2 file:px-4
                        file:rounded-md file:border-0
                        file:text-sm file:font-semibold
                        file:bg-yellow-50 file:text-yellow-700
                        hover:file:bg-yellow-100"
                      disabled={restoring}
                      required
                    />
                  </div>

                  {restoring && (
                    <div className="mb-4">
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div
                          className="bg-yellow-600 h-2.5 rounded-full"
                          style={{ width: `${uploadProgress}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-gray-500 mt-1 text-center">{uploadProgress}%</p>
                    </div>
                  )}
                </form>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleRestoreBackup}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-yellow-600 text-base font-medium text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={restoring}
                >
                  {restoring ? 'جاري الاستعادة...' : 'استعادة'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowRestoreModal(false)}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={restoring}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Backup Modal */}
      {showDeleteModal && backupToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      حذف النسخة الاحتياطية
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        هل أنت متأكد من رغبتك في حذف النسخة الاحتياطية "{backupToDelete.name}"؟
                      </p>
                      <p className="text-sm text-red-500 mt-2">
                        هذا الإجراء لا يمكن التراجع عنه.
                      </p>
                    </div>
                  </div>
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleDeleteBackup}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={deleting}
                >
                  {deleting ? 'جاري الحذف...' : 'حذف'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setBackupToDelete(null);
                    setShowDeleteModal(false);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Backup;
