# تعليمات حل مشكلة "Invalid token" في رفع الملفات

## المشكلة التي تم حلها
تم إصلاح مشكلة ظهور رسالة "Invalid token: حدث خطأ أثناء رفع الملفات" عند محاولة رفع ملفات لعدة موظفين.

## الخطوات للتأكد من عمل الحل

### 1. إعادة تشغيل الخادم
```bash
# في terminal منفصل للـ backend
cd backend
node server.js

# في terminal منفصل للـ frontend  
cd frontend
npm run dev
```

### 2. تسجيل الدخول مرة أخرى
- اذهب إلى صفحة تسجيل الدخول
- سجل دخولك بحساب له صلاحيات `editor` أو `admin`
- تأكد من نجاح تسجيل الدخول

### 3. اختبار رفع الملفات
1. اذهب إلى صفحة "رفع ملفات لعدة موظفين" (`/bulk-upload`)
2. اختر ملف أو عدة ملفات
3. اختر موظف أو عدة موظفين
4. اضغط على زر "رفع الملفات للموظفين المحددين"

### 4. مراقبة النتائج
- يجب أن تختفي رسالة "Invalid token"
- يجب أن يتم رفع الملفات بنجاح
- ستظهر رسالة نجاح أو تنبيه إذا كانت الملفات موجودة مسبقاً

## اختبار متقدم (اختياري)

### فتح Developer Tools
1. اضغط `F12` أو `Ctrl+Shift+I`
2. اذهب إلى تبويب `Console`
3. انسخ والصق الكود التالي:

```javascript
// فحص الـ token
const token = localStorage.getItem('token') || sessionStorage.getItem('token');
console.log('Token available:', !!token);

// اختبار API
if (token) {
  fetch('/api/auth/verify', {
    headers: { 'Authorization': `Bearer ${token}` }
  })
  .then(r => r.json())
  .then(data => console.log('Auth test:', data))
  .catch(e => console.log('Auth error:', e));
}
```

### النتائج المتوقعة في Console
```
Token available: true
Auth test: {user: {id: 1, username: "admin", role: "admin"}}
```

## إذا استمرت المشكلة

### 1. تحقق من صلاحيات المستخدم
- تأكد أن المستخدم له دور `editor` أو `admin`
- المستخدمون بدور `viewer` لا يمكنهم رفع الملفات

### 2. تحقق من انتهاء صلاحية الـ token
- الـ token ينتهي بعد 24 ساعة
- سجل خروج ثم دخول مرة أخرى

### 3. مسح cache المتصفح
```javascript
// في console المتصفح
localStorage.clear();
sessionStorage.clear();
location.reload();
```

### 4. تحقق من logs الخادم
في terminal الخادم، يجب أن ترى:
```
Auth middleware - Authorization header: Bearer [token]
Auth middleware - Token verified successfully for user: [id]
isEditor middleware - User has editor privileges
```

## معلومات إضافية

### الملفات التي تم تعديلها
1. `frontend/src/contexts/AuthContext.jsx` - إدارة الـ token
2. `frontend/src/pages/BulkUpload.jsx` - استخدام الـ token
3. `backend/middleware/auth.js` - تحسين التوثيق

### ميزات جديدة
- تحسين إدارة الـ token في AuthContext
- دعم أفضل لـ sessionStorage و localStorage
- رسائل خطأ أوضح
- logging مفصل للتشخيص

## الدعم
إذا استمرت المشكلة بعد اتباع هذه التعليمات، تحقق من:
1. أن الخادم يعمل على المنفذ الصحيح
2. أن قاعدة البيانات تحتوي على مستخدمين بصلاحيات مناسبة
3. أن جميع التبعيات مثبتة بشكل صحيح

---
**ملاحظة**: هذا الإصلاح يحسن من أمان وموثوقية نظام التوثيق في التطبيق.
