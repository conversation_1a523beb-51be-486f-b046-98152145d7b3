import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { FiArrowRight, FiPrinter, FiZoomIn, FiZoomOut, FiRotateCw, FiDownload, FiCheckCircle, FiX } from 'react-icons/fi';
import * as pdfjsLib from 'pdfjs-dist';
import { useAuth } from '../contexts/AuthContext';

// Set the PDF.js worker source
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

const FileViewer = () => {
  const { employee, filename } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const canvasRef = useRef(null);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadSuccessMessage, setDownloadSuccessMessage] = useState('');
  const [showDownloadSuccessMessage, setShowDownloadSuccessMessage] = useState(false);

  useEffect(() => {
    loadPdf();
  }, [employee, filename]);

  useEffect(() => {
    if (pdfDoc) {
      renderPage();
    }
  }, [pdfDoc, pageNum, scale, rotation]);

  // إزالة رسالة نجاح التنزيل بعد 5 ثوانٍ
  useEffect(() => {
    if (showDownloadSuccessMessage) {
      const timer = setTimeout(() => {
        setShowDownloadSuccessMessage(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [showDownloadSuccessMessage]);

  const loadPdf = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get the PDF file
      const response = await axios.get(`/api/files/employee/${employee}/${filename}`, {
        responseType: 'arraybuffer',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      });

      // Load the PDF
      const loadingTask = pdfjsLib.getDocument({
        data: response.data,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
        standardFontDataUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/standard_fonts/'
      });

      const pdf = await loadingTask.promise;

      setPdfDoc(pdf);
      setPageCount(pdf.numPages);
      setPageNum(1);
    } catch (err) {
      console.error('Error loading PDF:', err);
      setError('حدث خطأ أثناء تحميل الملف');
    } finally {
      setLoading(false);
    }
  };

  const renderPage = async () => {
    if (!pdfDoc) return;

    try {
      // Get the page
      const page = await pdfDoc.getPage(pageNum);

      // Set viewport with scale and rotation
      const viewport = page.getViewport({ scale, rotation: rotation * 90 });

      // Prepare canvas
      const canvas = canvasRef.current;
      if (!canvas) return;

      const context = canvas.getContext('2d', { alpha: false });
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // Clear canvas
      context.fillStyle = 'rgb(255, 255, 255)';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Render PDF page into canvas context
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
        enableWebGL: true,
        renderInteractiveForms: true
      };

      try {
        await page.render(renderContext).promise;
      } catch (renderError) {
        console.error('Error during render:', renderError);

        // Try again with simpler options
        const simpleRenderContext = {
          canvasContext: context,
          viewport: viewport
        };

        await page.render(simpleRenderContext).promise;
      }
    } catch (err) {
      console.error('Error rendering page:', err);
      setError('حدث خطأ أثناء عرض الصفحة');
    }
  };

  const prevPage = () => {
    if (pageNum <= 1) return;
    setPageNum(pageNum - 1);
  };

  const nextPage = () => {
    if (pageNum >= pageCount) return;
    setPageNum(pageNum + 1);
  };

  const zoomIn = () => {
    setScale(scale + 0.25);
  };

  const zoomOut = () => {
    if (scale <= 0.5) return;
    setScale(scale - 0.25);
  };

  const rotate = () => {
    setRotation((rotation + 1) % 4);
  };

  const handlePrint = () => {
    // Get token from localStorage
    const token = localStorage.getItem('token');

    // Create a blob URL with the file content
    const printWindow = window.open('', '_blank');

    if (printWindow) {
      printWindow.document.write('<html><body><h1>جاري تحميل المستند...</h1></body></html>');

      // Fetch the file with authorization header
      fetch(`/api/files/employee/${employee}/${filename}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('فشل في تحميل الملف');
        }
        return response.blob();
      })
      .then(blob => {
        // Create object URL
        const objectUrl = URL.createObjectURL(blob);

        // Redirect to the object URL
        printWindow.location.href = objectUrl;
      })
      .catch(error => {
        printWindow.document.write(`<html><body><h1>خطأ: ${error.message}</h1></body></html>`);
      });
    }
  };

  const handleDownloadFile = () => {
    // Get token from localStorage
    const token = localStorage.getItem('token');

    // Fetch the file with authorization header
    fetch(`/api/files/employee/${employee}/${filename}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('فشل في تحميل الملف');
      }
      return response.blob();
    })
    .then(blob => {
      // Create object URL
      const objectUrl = URL.createObjectURL(blob);

      // Create a temporary link element
      const a = document.createElement('a');
      a.href = objectUrl;
      a.download = filename; // Set the download attribute to the filename
      document.body.appendChild(a);

      // Trigger the download
      a.click();

      // Clean up
      URL.revokeObjectURL(objectUrl);
      document.body.removeChild(a);

      // Show success message
      setDownloadSuccessMessage(`تم تنزيل الملف "${filename}" بنجاح`);
      setShowDownloadSuccessMessage(true);

      // Log activity
      axios.post('/api/logs', {
        action: 'download_file',
        details: `تم تنزيل الملف: ${filename} للموظف: ${employee}`
      }).catch(err => console.error('Error logging activity:', err));
    })
    .catch(error => {
      console.error('Error downloading file:', error);
      alert('حدث خطأ أثناء تنزيل الملف');
    });
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/employees/${employee}`)}
          className="flex items-center text-gray-600 hover:text-gray-900"
        >
          <FiArrowRight className="ml-1" />
          <span>العودة إلى ملفات الموظف</span>
        </button>

        <h1 className="text-xl font-bold text-gray-800" dir="auto">{filename}</h1>
      </div>

      {/* Download Success Message */}
      {showDownloadSuccessMessage && (
        <div className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in">
          <FiCheckCircle className="text-green-500 text-xl ml-2 flex-shrink-0" />
          <div className="flex-grow" dir="auto">{downloadSuccessMessage}</div>
          <button
            onClick={() => setShowDownloadSuccessMessage(false)}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      {/* PDF Controls */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-4">
        <div className="flex flex-wrap items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={prevPage}
              disabled={pageNum <= 1 || loading}
              className="btn btn-secondary"
            >
              الصفحة السابقة
            </button>

            <span className="text-gray-700">
              صفحة <span className="font-medium">{pageNum}</span> من <span className="font-medium">{pageCount}</span>
            </span>

            <button
              onClick={nextPage}
              disabled={pageNum >= pageCount || loading}
              className="btn btn-secondary"
            >
              الصفحة التالية
            </button>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse mt-2 sm:mt-0">
            <button
              onClick={zoomIn}
              disabled={loading}
              className="p-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
              title="تكبير"
            >
              <FiZoomIn />
            </button>

            <button
              onClick={zoomOut}
              disabled={loading || scale <= 0.5}
              className="p-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
              title="تصغير"
            >
              <FiZoomOut />
            </button>

            <button
              onClick={rotate}
              disabled={loading}
              className="p-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
              title="تدوير"
            >
              <FiRotateCw />
            </button>

            <button
              onClick={handleDownloadFile}
              disabled={loading}
              className="p-2 rounded-md bg-gray-100 text-green-600 hover:bg-gray-200"
              title="تنزيل"
            >
              <FiDownload />
            </button>

            <button
              onClick={handlePrint}
              disabled={loading}
              className="p-2 rounded-md bg-gray-100 text-gray-700 hover:bg-gray-200"
              title="طباعة"
            >
              <FiPrinter />
            </button>
          </div>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="bg-gray-800 rounded-lg shadow-md p-4 flex justify-center">
        {loading ? (
          <div className="text-center py-8 text-white">
            <div className="animate-pulse">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="mt-2">جاري تحميل الملف...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-white">
            <svg className="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className="mt-2 text-red-400 font-medium text-lg">{error}</p>
            <button
              onClick={loadPdf}
              className="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors duration-300 flex items-center mx-auto"
            >
              <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              إعادة المحاولة
            </button>
          </div>
        ) : (
          <canvas ref={canvasRef} className="max-w-full" />
        )}
      </div>
    </div>
  );
};

export default FileViewer;
