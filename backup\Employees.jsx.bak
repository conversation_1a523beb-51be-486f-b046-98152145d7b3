import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { FiUsers, FiPlus, FiSearch, FiTrash2, FiFolder, FiUpload, FiEdit, FiUser, FiFile, FiCalendar, FiAlertCircle } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Employees = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [newEmployeeName, setNewEmployeeName] = useState('');
  const [addingEmployee, setAddingEmployee] = useState(false);
  const [addEmployeeError, setAddEmployeeError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [employeeToDelete, setEmployeeToDelete] = useState(null);
  const [deletingEmployee, setDeletingEmployee] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [employeeToEdit, setEmployeeToEdit] = useState(null);
  const [editedEmployeeName, setEditedEmployeeName] = useState('');
  const [editingEmployee, setEditingEmployee] = useState(false);

  useEffect(() => {
    fetchEmployees();
  }, []);

  // إزالة رسالة النجاح بعد 5 ثوانٍ
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/employees');
      setEmployees(response.data);
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError('حدث خطأ أثناء جلب بيانات الموظفين');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEmployee = async (e) => {
    e.preventDefault();

    if (!newEmployeeName.trim()) {
      return;
    }

    try {
      setAddingEmployee(true);
      setAddEmployeeError(null); // إعادة تعيين رسالة الخطأ

      await axios.post('/api/employees', { name: newEmployeeName });

      // Refresh employees list
      fetchEmployees();

      // إظهار رسالة النجاح
      setSuccessMessage(`تم إضافة الموظف "${newEmployeeName}" بنجاح`);

      // Reset form
      setNewEmployeeName('');
      setShowAddModal(false);
    } catch (err) {
      console.error('Error adding employee:', err);
      // عرض رسالة الخطأ الفعلية من الخادم بدلاً من رسالة عامة
      setAddEmployeeError(err.response?.data?.message || 'حدث خطأ أثناء إضافة الموظف');
    } finally {
      setAddingEmployee(false);
    }
  };

  const handleDeleteEmployee = async () => {
    if (!employeeToDelete) return;

    try {
      setDeletingEmployee(true);
      setDeleteEmployeeError(null);

      // Store employee name before deletion
      const employeeName = employeeToDelete.name;

      await axios.delete(`/api/employees/${employeeName}`);

      // Refresh employees list
      fetchEmployees();

      // Show success message
      setSuccessMessage(`تم حذف الموظف "${employeeName}" بنجاح`);

      // Reset state
      setEmployeeToDelete(null);
      setShowDeleteModal(false);
    } catch (err) {
      console.error('Error deleting employee:', err);
      setDeleteEmployeeError(err.response?.data?.message || 'حدث خطأ أثناء حذف الموظف');
    } finally {
      setDeletingEmployee(false);
    }
  };

  // وظيفة لحذف موظف بالاسم مباشرة
  const deleteEmployeeByName = async (employeeName) => {
    try {
      await axios.delete(`/api/employees/${employeeName}`);
      // Refresh employees list
      fetchEmployees();
      // Show success message
      setSuccessMessage(`تم حذف الموظف "${employeeName}" بنجاح`);
      return true;
    } catch (err) {
      console.error(`Error deleting employee ${employeeName}:`, err);
      const errorMessage = err.response?.data?.message || `حدث خطأ أثناء حذف الموظف ${employeeName}`;
      setSuccessMessage(null); // إزالة رسالة النجاح السابقة إن وجدت
      alert(errorMessage);
      return false;
    }
  };

  const [editEmployeeError, setEditEmployeeError] = useState(null);
  const [deleteEmployeeError, setDeleteEmployeeError] = useState(null);

  const handleEditEmployee = async (e) => {
    e.preventDefault();

    if (!employeeToEdit || !editedEmployeeName.trim()) return;

    try {
      setEditingEmployee(true);
      setEditEmployeeError(null);

      // Check if name is actually changed
      if (editedEmployeeName !== employeeToEdit.name) {
        await axios.put(`/api/employees/${employeeToEdit.name}`, {
          newName: editedEmployeeName
        });

        // Refresh employees list
        fetchEmployees();

        // Show success message
        setSuccessMessage(`تم تعديل اسم الموظف من "${employeeToEdit.name}" إلى "${editedEmployeeName}" بنجاح`);

        // Reset state
        setEmployeeToEdit(null);
        setEditedEmployeeName('');
        setShowEditModal(false);
      } else {
        // No changes made, just close the modal
        setEmployeeToEdit(null);
        setEditedEmployeeName('');
        setShowEditModal(false);
      }
    } catch (err) {
      console.error('Error updating employee:', err);
      setEditEmployeeError(err.response?.data?.message || 'حدث خطأ أثناء تحديث بيانات الموظف');
    } finally {
      setEditingEmployee(false);
    }
  };

  const filteredEmployees = employees.filter(employee =>
    employee.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">الموظفين</h1>

        {(user.role === 'admin' || user.role === 'editor') && (
          <div className="flex space-x-2 space-x-reverse">
            <button
              onClick={() => navigate('/bulk-upload')}
              className="btn btn-secondary flex items-center ml-2"
            >
              <FiUpload className="ml-1" />
              رفع ملفات لعدة موظفين
            </button>
            <button
              onClick={() => {
                setShowAddModal(true);
                setAddEmployeeError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
              }}
              className="btn btn-primary flex items-center"
            >
              <FiPlus className="ml-1" />
              إضافة موظف
            </button>
          </div>
        )}
      </div>

      {/* Search */}
      <div className="mb-6">
        <div className="relative">
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <FiSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            className="input pr-10"
            placeholder="ابحث عن موظف..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* رسالة النجاح */}
      {successMessage && (
        <div className="mb-6 p-3 rounded-lg bg-green-50 border border-green-200 text-green-700 text-sm flex items-center animate-fade-in-out">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span>{successMessage}</span>
        </div>
      )}

      {/* Employees List */}
      {loading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">جاري تحميل بيانات الموظفين...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <button
            onClick={fetchEmployees}
            className="mt-2 btn btn-secondary"
          >
            إعادة المحاولة
          </button>
        </div>
      ) : filteredEmployees.length === 0 ? (
        <div className="text-center py-8">
          <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-lg font-medium text-gray-900">لا يوجد موظفين</h3>
          {searchQuery ? (
            <p className="mt-1 text-gray-500">لا توجد نتائج تطابق بحثك. حاول بكلمات أخرى.</p>
          ) : (
            <p className="mt-1 text-gray-500">قم بإضافة موظف جديد للبدء.</p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredEmployees.map((employee) => (
            <div key={employee.name} className="card hover:shadow-md transition-shadow duration-300">
              <div className="flex justify-between items-start">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center">
                    <FiUser className="w-6 h-6" />
                  </div>
                  <div className="mr-3">
                    <h3 className="text-lg font-semibold text-gray-800">{employee.name}</h3>
                    <div className="flex items-center text-sm text-gray-500 mt-1">
                      <FiCalendar className="ml-1 w-3 h-3" />
                      <span>{formatDate(employee.createdAt)}</span>
                    </div>
                  </div>
                </div>

                {(user.role === 'admin' || user.role === 'editor') && (
                  <div className="flex space-x-2 space-x-reverse">
                    <button
                      onClick={() => {
                        setEmployeeToEdit(employee);
                        setEditedEmployeeName(employee.name);
                        setEditEmployeeError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
                        setShowEditModal(true);
                      }}
                      className="text-green-500 hover:text-green-700 p-1"
                      title="تعديل"
                    >
                      <FiEdit className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => {
                        setEmployeeToDelete(employee);
                        setDeleteEmployeeError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
                        setShowDeleteModal(true);
                      }}
                      className="text-red-500 hover:text-red-700 p-1"
                      title="حذف"
                    >
                      <FiTrash2 className="w-4 h-4" />
                    </button>
                  </div>
                )}
              </div>

              <div className="mt-6 border-t pt-4 flex">
                <Link
                  to={`/employees/${employee.name}`}
                  className="flex-1 py-2 px-3 bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors flex items-center justify-center"
                >
                  <FiFolder className="ml-2" />
                  <span>عرض الملفات</span>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Employee Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-primary-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center ml-4 shadow-md">
                    <FiPlus className="h-6 w-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      إضافة موظف جديد
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      أدخل اسم الموظف الجديد. سيتم إنشاء مجلد جديد بهذا الاسم.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {addEmployeeError && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{addEmployeeError}</span>
                  </div>
                )}
                <form onSubmit={handleAddEmployee}>
                  <div className="mb-6">
                    <label htmlFor="employeeName" className="block text-sm font-medium text-gray-700 mb-2">
                      اسم الموظف
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiUser className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="employeeName"
                        className="input pr-10 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 shadow-sm"
                        placeholder="أدخل اسم الموظف الكامل"
                        value={newEmployeeName}
                        onChange={(e) => setNewEmployeeName(e.target.value)}
                        required
                        autoFocus
                      />
                    </div>

                    <div className="mt-4 bg-blue-50 p-3 rounded-lg border border-blue-100">
                      <div className="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 ml-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <div className="text-sm text-blue-700">
                          <p>يرجى إدخال الاسم الكامل للموظف بشكل صحيح.</p>
                          <p className="mt-1">سيتم استخدام هذا الاسم لإنشاء مجلد خاص بالموظف لحفظ ملفاته.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleAddEmployee}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${addingEmployee || !newEmployeeName.trim()
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-primary-600 hover:bg-primary-700 hover:shadow-lg'
                    }
                  `}
                  disabled={addingEmployee || !newEmployeeName.trim()}
                >
                  <FiPlus className="ml-2" />
                  {addingEmployee ? 'جاري الإضافة...' : 'إضافة موظف'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Employee Modal */}
      {showDeleteModal && employeeToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      حذف موظف
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      تأكيد حذف الموظف <span className="font-semibold text-red-600">"{employeeToDelete.name}"</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {deleteEmployeeError && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{deleteEmployeeError}</span>
                  </div>
                )}

                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير هام</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم حذف جميع الملفات المرتبطة بالموظف "{employeeToDelete.name}" بشكل نهائي.
                        </p>
                        <p className="font-bold mt-1">
                          هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-yellow-800">نصيحة</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          إذا كنت تريد الاحتفاظ بالملفات، يمكنك تنزيلها قبل حذف الموظف.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleDeleteEmployee}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${deletingEmployee
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-red-600 hover:bg-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={deletingEmployee}
                >
                  <FiTrash2 className="ml-2" />
                  {deletingEmployee ? 'جاري الحذف...' : 'تأكيد الحذف'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setEmployeeToDelete(null);
                    setShowDeleteModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Employee Modal */}
      {showEditModal && employeeToEdit && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-green-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center ml-4 shadow-md">
                    <FiEdit className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      تعديل بيانات الموظف
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      قم بتعديل بيانات الموظف <span className="font-semibold text-green-600">"{employeeToEdit.name}"</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {editEmployeeError && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{editEmployeeError}</span>
                  </div>
                )}
                <form onSubmit={handleEditEmployee}>
                  <div className="mb-6">
                    <label htmlFor="editEmployeeName" className="block text-sm font-medium text-gray-700 mb-2">
                      اسم الموظف
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiUser className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="editEmployeeName"
                        className="input pr-10 focus:ring-green-500 focus:border-green-500 transition-all duration-300 shadow-sm"
                        placeholder="أدخل اسم الموظف الجديد"
                        value={editedEmployeeName}
                        onChange={(e) => setEditedEmployeeName(e.target.value)}
                        required
                        autoFocus
                      />
                    </div>
                    <div className="mt-4 bg-green-50 p-3 rounded-lg border border-green-100">
                      <div className="flex items-start">
                        <div className="flex-shrink-0 mt-0.5">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="mr-2">
                          <p className="text-xs text-green-700">
                            سيتم تغيير اسم المجلد الخاص بالموظف. جميع الملفات ستبقى محفوظة تحت الاسم الجديد.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleEditEmployee}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${editingEmployee || !editedEmployeeName.trim() || editedEmployeeName === employeeToEdit.name
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-green-600 hover:bg-green-700 hover:shadow-lg'
                    }
                  `}
                  disabled={editingEmployee || !editedEmployeeName.trim() || editedEmployeeName === employeeToEdit.name}
                >
                  <FiEdit className="ml-2" />
                  {editingEmployee ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setEmployeeToEdit(null);
                    setEditedEmployeeName('');
                    setShowEditModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Employees;
