import { useEffect } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { useAuth } from './contexts/AuthContext';

// Layouts
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';

// Pages
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Employees from './pages/Employees';
import EmployeeDetails from './pages/EmployeeDetails';
import FileViewer from './pages/FileViewer';
import BulkUpload from './pages/BulkUpload';
import Users from './pages/Users';
import ActivityLogs from './pages/ActivityLogs';
import Backup from './pages/Backup';
import Profile from './pages/Profile';
import NotFound from './pages/NotFound';

// Protected route component
const ProtectedRoute = ({ children, requiredRole }) => {
  const { user, isAuthenticated, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center h-screen">جاري التحميل...</div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (requiredRole && user.role !== requiredRole && user.role !== 'admin') {
    return <Navigate to="/dashboard" />;
  }

  return children;
};

function App() {
  const { verifyToken } = useAuth();
  const location = useLocation();

  useEffect(() => {
    verifyToken();
  }, [verifyToken]);

  // إضافة تأثير لتسجيل تغييرات المسار وإعادة تحميل الصفحة إذا كان المسار يحتوي على "files"
  useEffect(() => {
    console.log('Current location:', location.pathname);

    // إذا كان المسار يحتوي على "files" وتم تحديث الصفحة، قم بإعادة تحميل البيانات
    if (location.pathname.includes('/files/')) {
      // تنفيذ أي منطق إضافي هنا إذا لزم الأمر
      console.log('File path detected:', location.pathname);
    }
  }, [location]);

  return (
    <Routes>
      {/* Auth routes */}
      <Route element={<AuthLayout />}>
        <Route path="/login" element={<Login />} />
      </Route>

      {/* Protected routes */}
      <Route element={<MainLayout />}>
        <Route path="/" element={<Navigate to="/dashboard" />} />

        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />

        <Route
          path="/employees"
          element={
            <ProtectedRoute>
              <Employees />
            </ProtectedRoute>
          }
        />

        <Route
          path="/employees/:name"
          element={
            <ProtectedRoute>
              <EmployeeDetails />
            </ProtectedRoute>
          }
        />

        <Route
          path="/bulk-upload"
          element={
            <ProtectedRoute requiredRole="editor">
              <BulkUpload />
            </ProtectedRoute>
          }
        />

        <Route
          path="/files/:employee/:filename"
          element={
            <ProtectedRoute>
              <FileViewer />
            </ProtectedRoute>
          }
        />

        <Route
          path="/users"
          element={
            <ProtectedRoute requiredRole="admin">
              <Users />
            </ProtectedRoute>
          }
        />

        <Route
          path="/logs"
          element={
            <ProtectedRoute requiredRole="admin">
              <ActivityLogs />
            </ProtectedRoute>
          }
        />

        <Route
          path="/backup"
          element={
            <ProtectedRoute requiredRole="admin">
              <Backup />
            </ProtectedRoute>
          }
        />

        <Route
          path="/profile"
          element={
            <ProtectedRoute>
              <Profile />
            </ProtectedRoute>
          }
        />
      </Route>

      {/* 404 route */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
