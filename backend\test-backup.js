// Test script to verify backup functionality
const fs = require('fs-extra');
const path = require('path');

console.log('🧪 اختبار وظائف النسخ الاحتياطي...');

// Test paths
const dataDir = path.join(__dirname, 'data');
const backupDir = path.join(dataDir, 'backups');

console.log(`📁 مجلد البيانات: ${dataDir}`);
console.log(`📁 مجلد النسخ الاحتياطية: ${backupDir}`);

// Check if directories exist
console.log(`✅ مجلد البيانات موجود: ${fs.existsSync(dataDir)}`);
console.log(`✅ مجلد النسخ الاحتياطية موجود: ${fs.existsSync(backupDir)}`);

// Check for database file
const dbPath = path.join(dataDir, 'archive.db');
console.log(`✅ ملف قاعدة البيانات موجود: ${fs.existsSync(dbPath)}`);

// Check for employee data directory
const employeeDataDir = path.join(dataDir, 'employee_database');
console.log(`✅ مجلد بيانات الموظفين موجود: ${fs.existsSync(employeeDataDir)}`);

if (fs.existsSync(employeeDataDir)) {
  const employees = fs.readdirSync(employeeDataDir);
  console.log(`👥 عدد الموظفين: ${employees.length}`);
  console.log(`👥 أسماء الموظفين: ${employees.slice(0, 5).join(', ')}${employees.length > 5 ? '...' : ''}`);
}

// Check for backup files
if (fs.existsSync(backupDir)) {
  const backups = fs.readdirSync(backupDir).filter(file => file.endsWith('.zip'));
  console.log(`💾 عدد النسخ الاحتياطية: ${backups.length}`);
  if (backups.length > 0) {
    console.log(`💾 أحدث نسخة احتياطية: ${backups[backups.length - 1]}`);
  }
}

console.log('✅ انتهى الاختبار');
