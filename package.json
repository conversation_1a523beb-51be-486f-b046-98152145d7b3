{"name": "employee-archive-system", "version": "1.0.0", "description": "A web application for managing and archiving employee data and files", "scripts": {"install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "start-backend": "cd backend && npm run dev", "start-frontend": "cd frontend && npm run dev", "dev": "concurrently \"npm run start-backend\" \"npm run start-frontend\"", "init-data": "cd backend && node scripts/init-sample-data.js"}, "keywords": ["employee", "archive", "file-management", "react", "node", "express"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.0.1"}, "dependencies": {"fs-extra": "^11.3.0"}}