import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import App from './App.jsx'
import { AuthProvider } from './contexts/AuthContext'
import './index.css'

// استخدام BrowserRouter بدلاً من HashRouter لإزالة علامة # من المسار
// ملاحظة: قد يتطلب هذا إعدادات إضافية على الخادم للتعامل مع التوجيه بشكل صحيح
ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <AuthProvider>
        <App />
      </AuthProvider>
    </BrowserRouter>
  </React.StrictMode>,
)
