const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const archiver = require('archiver');
const multer = require('multer');
const { authenticateToken, isAdmin } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

const router = express.Router();

// Base directory for data
const dataDir = path.join(__dirname, '../data');
const backupDir = path.join(dataDir, 'backups');

// Ensure backup directory exists
fs.ensureDirSync(backupDir);

// Configure multer for backup uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, backupDir);
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 1024 * 1024 * 100 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    // Accept only zip files
    if (file.mimetype === 'application/zip' || file.originalname.endsWith('.zip')) {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP files are allowed'));
    }
  }
});

// Create backup (admin only)
router.post('/create', authenticateToken, isAdmin, (req, res) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `backup-${timestamp}.zip`;
  const backupPath = path.join(backupDir, backupFileName);

  try {
    console.log(`Creating backup: ${backupFileName}`);

    // Create a file to stream archive data to
    const output = fs.createWriteStream(backupPath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Listen for all archive data to be written
    output.on('close', () => {
      console.log(`Backup created successfully: ${backupFileName} (${archive.pointer()} bytes)`);

      // Log activity
      logActivity(req.user.id, 'create_backup', `Created backup: ${backupFileName} (${archive.pointer()} bytes)`);

      res.json({
        message: 'Backup created successfully',
        backup: {
          name: backupFileName,
          path: backupPath,
          size: archive.pointer(),
          createdAt: new Date()
        }
      });
    });

    // Handle errors
    archive.on('error', (err) => {
      console.error(`Error creating backup: ${err.message}`);
      throw err;
    });

    // Pipe archive data to the file
    archive.pipe(output);

    // Add the database file
    const dbPath = path.join(dataDir, 'archive.db');
    if (fs.existsSync(dbPath)) {
      console.log(`Adding database file to backup: ${dbPath}`);
      archive.file(dbPath, { name: 'archive.db' });
    } else {
      console.error(`Database file not found: ${dbPath}`);
    }

    // Add the employee data directory
    const employeeDataDir = path.join(dataDir, 'employee_database');
    if (fs.existsSync(employeeDataDir)) {
      console.log(`Adding employee data directory to backup: ${employeeDataDir}`);
      archive.directory(employeeDataDir, 'employee_database');
    } else {
      console.error(`Employee data directory not found: ${employeeDataDir}`);
    }

    // Log the structure of the backup
    console.log('Backup structure:');
    console.log('- archive.db');
    console.log('- employee_database/');
    console.log('  - [employee folders]');

    // Finalize the archive
    archive.finalize();
  } catch (err) {
    console.error('Error creating backup:', err);
    res.status(500).json({ message: 'Error creating backup' });
  }
});

// Create and download backup (admin only)
router.get('/download', authenticateToken, isAdmin, (req, res) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `backup-${timestamp}.zip`;

  try {
    console.log(`Creating backup for direct download: ${backupFileName}`);

    // Create a zip archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Set the response headers for file download
    res.attachment(backupFileName);
    res.setHeader('Content-Type', 'application/zip');

    // Pipe the archive to the response
    archive.pipe(res);

    // Handle errors
    archive.on('error', (err) => {
      console.error(`Error creating backup for download: ${err.message}`);
      // إنهاء الاستجابة بحالة خطأ
      if (!res.headersSent) {
        res.status(500).json({ message: `Error creating backup: ${err.message}` });
      } else {
        res.end();
      }
    });

    // Add the database file
    const dbPath = path.join(dataDir, 'archive.db');
    if (fs.existsSync(dbPath)) {
      console.log(`Adding database file to download backup: ${dbPath}`);
      archive.file(dbPath, { name: 'archive.db' });
    } else {
      console.error(`Database file not found: ${dbPath}`);
      throw new Error('Database file not found');
    }

    // Add the employee data directory
    const employeeDataDir = path.join(dataDir, 'employee_database');
    if (fs.existsSync(employeeDataDir)) {
      console.log(`Adding employee data directory to download backup: ${employeeDataDir}`);
      archive.directory(employeeDataDir, 'employee_database');
    } else {
      console.error(`Employee data directory not found: ${employeeDataDir}`);
      throw new Error('Employee data directory not found');
    }

    // Log activity
    logActivity(req.user.id, 'download_backup', `Created and downloaded backup: ${backupFileName}`);

    // Listen for archive close event
    archive.on('end', () => {
      console.log(`Backup archive finalized: ${backupFileName}`);
    });

    // Finalize the archive
    archive.finalize();

    console.log('Archive finalization started');

  } catch (err) {
    console.error('Error creating backup for download:', err);
    res.status(500).json({ message: `Error creating backup for download: ${err.message}` });
  }
});

// Get all backups (admin only)
router.get('/', authenticateToken, isAdmin, (req, res) => {
  try {
    // Read all backup files
    const backups = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.zip'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: file,
          path: filePath,
          size: stats.size,
          createdAt: stats.birthtime
        };
      })
      .sort((a, b) => b.createdAt - a.createdAt); // Sort by creation date (newest first)

    res.json(backups);
  } catch (err) {
    console.error('Error reading backups:', err);
    res.status(500).json({ message: 'Error reading backups' });
  }
});

// Download backup (admin only)
router.get('/:filename', authenticateToken, isAdmin, (req, res) => {
  const { filename } = req.params;
  const backupPath = path.join(backupDir, filename);

  try {
    console.log(`Attempting to download backup: ${filename}`);
    console.log(`Full backup path: ${backupPath}`);
    console.log(`Current directory: ${__dirname}`);
    console.log(`Backup directory: ${backupDir}`);

    // Check if backup directory exists
    if (!fs.existsSync(backupDir)) {
      console.error(`Backup directory not found: ${backupDir}`);
      fs.ensureDirSync(backupDir);
      console.log(`Created backup directory: ${backupDir}`);
    }

    // Check if backup file exists
    if (!fs.existsSync(backupPath)) {
      console.error(`Backup file not found: ${backupPath}`);
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Check if it's a file
    const stats = fs.statSync(backupPath);
    if (!stats.isFile()) {
      console.error(`Path exists but is not a file: ${backupPath}`);
      return res.status(404).json({ message: 'Backup is not a valid file' });
    }

    console.log(`Backup file found: ${backupPath}, size: ${stats.size} bytes`);

    // Log activity
    logActivity(req.user.id, 'download_backup', `Downloaded backup: ${filename}`);

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', stats.size);

    // Send file directly
    console.log(`Sending file: ${backupPath}`);
    fs.createReadStream(backupPath)
      .on('error', (err) => {
        console.error(`Error reading file: ${err.message}`);
        if (!res.headersSent) {
          res.status(500).send(`Error downloading backup: ${err.message}`);
        } else {
          res.end();
        }
      })
      .pipe(res)
      .on('finish', () => {
        console.log(`File download completed successfully: ${filename}`);
      });

  } catch (err) {
    console.error(`Error downloading backup ${filename}:`, err);
    res.status(500).json({ message: `Error downloading backup ${filename}: ${err.message}` });
  }
});

// Restore backup (admin only) - Enhanced Method with Better Error Handling
router.post('/restore', authenticateToken, isAdmin, upload.single('backup'), async (req, res) => {
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'لم يتم رفع ملف النسخة الاحتياطية' });
  }

  console.log(`🔄 بدء عملية استعادة النسخة الاحتياطية: ${file.originalname}`);
  console.log(`📁 مسار الملف: ${file.path}`);
  console.log(`📏 حجم الملف: ${file.size} بايت`);

  // Create a temporary directory for extraction
  const tempDir = path.join(dataDir, 'temp-restore');

  try {
    // Ensure temp directory is clean
    if (fs.existsSync(tempDir)) {
      console.log('🧹 إزالة المجلد المؤقت الموجود');
      fs.removeSync(tempDir);
    }

    fs.ensureDirSync(tempDir);
    console.log(`📂 تم إنشاء المجلد المؤقت: ${tempDir}`);

    // Extract the backup using extract-zip library for better reliability
    console.log('📦 بدء استخراج النسخة الاحتياطية...');

    const extractZip = require('extract-zip');

    try {
      await extractZip(file.path, { dir: tempDir });
      console.log('✅ تم استخراج النسخة الاحتياطية بنجاح');
    } catch (extractError) {
      console.error('❌ فشل في استخراج النسخة الاحتياطية:', extractError);
      throw new Error(`فشل في استخراج النسخة الاحتياطية: ${extractError.message}`);
    }

    // List all extracted files for debugging
    console.log('📋 فحص محتويات النسخة الاحتياطية المستخرجة:');
    const listAllFiles = (dir, basePath = '', depth = 0) => {
      if (depth > 5) return; // منع التكرار العميق

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          const relativePath = path.join(basePath, entry.name);

          if (entry.isDirectory()) {
            console.log(`📁 ${relativePath}/`);
            if (depth < 3) {
              listAllFiles(fullPath, relativePath, depth + 1);
            }
          } else {
            const stats = fs.statSync(fullPath);
            console.log(`📄 ${relativePath} (${stats.size} بايت)`);
          }
        }
      } catch (err) {
        console.error(`❌ خطأ في قراءة المجلد ${dir}:`, err.message);
      }
    };

    listAllFiles(tempDir);

    // Enhanced search function for files and directories
    console.log('🔍 البحث عن ملف قاعدة البيانات ومجلد بيانات الموظفين...');

    // Enhanced search function for files and directories
    const findFileOrDir = (dir, nameToFind, isDir = false, depth = 0) => {
      if (depth > 5) return null; // منع التكرار اللانهائي

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        console.log(`🔍 البحث في ${dir} (العمق: ${depth}):`, entries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          // البحث عن تطابق دقيق أولاً
          if (entry.name === nameToFind && entry.isDirectory() === isDir) {
            console.log(`✅ تم العثور على تطابق دقيق: ${fullPath}`);
            return fullPath;
          }

          // البحث المحسن عن ملفات قاعدة البيانات
          if (!isDir && nameToFind === 'archive.db' && entry.isFile()) {
            const fileName = entry.name.toLowerCase();
            if (fileName.endsWith('.db') ||
                fileName.includes('archive') ||
                fileName.includes('database') ||
                fileName.includes('db')) {
              console.log(`✅ تم العثور على ملف قاعدة بيانات: ${entry.name} في ${fullPath}`);
              return fullPath;
            }
          }

          // البحث المحسن عن مجلد بيانات الموظفين
          if (isDir && nameToFind === 'employee_database' && entry.isDirectory()) {
            const lowerName = entry.name.toLowerCase();
            if (lowerName.includes('employee') ||
                lowerName.includes('data') ||
                lowerName === 'employees' ||
                lowerName === 'employee_data' ||
                lowerName === 'emp_data' ||
                lowerName === 'uploads' ||
                lowerName === 'files') {
              console.log(`✅ تم العثور على مجلد بيانات الموظفين: ${entry.name} في ${fullPath}`);
              return fullPath;
            }
          }

          // البحث في المجلدات الفرعية
          if (entry.isDirectory() && depth < 4) {
            const found = findFileOrDir(fullPath, nameToFind, isDir, depth + 1);
            if (found) return found;
          }
        }
      } catch (err) {
        console.error(`❌ خطأ في قراءة المجلد ${dir}:`, err.message);
      }

      return null;
    };

    // البحث عن ملف قاعدة البيانات
    console.log('🎯 البحث عن ملف قاعدة البيانات...');
    let dbPath = findFileOrDir(tempDir, 'archive.db', false);

    if (!dbPath) {
      // البحث في مجلد data إذا لم يتم العثور عليه مباشرة
      console.log('🔍 البحث في مجلد data...');
      const foundDataDir = findFileOrDir(tempDir, 'data', true);
      if (foundDataDir) {
        const potentialDbPath = path.join(foundDataDir, 'archive.db');
        if (fs.existsSync(potentialDbPath)) {
          dbPath = potentialDbPath;
          console.log(`✅ تم العثور على قاعدة البيانات في مجلد data: ${dbPath}`);
        }
      }
    }

    // البحث عن مجلد بيانات الموظفين
    console.log('🎯 البحث عن مجلد بيانات الموظفين...');
    let employeeDataDir = findFileOrDir(tempDir, 'employee_database', true);

    if (!employeeDataDir) {
      // البحث في مجلد data إذا لم يتم العثور عليه مباشرة
      console.log('🔍 البحث في مجلد data...');
      const foundDataDir = findFileOrDir(tempDir, 'data', true);
      if (foundDataDir) {
        const potentialEmployeeDir = path.join(foundDataDir, 'employee_database');
        if (fs.existsSync(potentialEmployeeDir)) {
          employeeDataDir = potentialEmployeeDir;
          console.log(`✅ تم العثور على مجلد الموظفين في مجلد data: ${employeeDataDir}`);
        }
      }
    }

    console.log(`📄 ملف قاعدة البيانات: ${dbPath || 'لم يتم العثور عليه'}`);
    console.log(`📁 مجلد بيانات الموظفين: ${employeeDataDir || 'لم يتم العثور عليه'}`);

    // التحقق من وجود الملفات المطلوبة
    const missingItems = [];
    if (!dbPath) {
      missingItems.push('ملف قاعدة البيانات (archive.db)');
    }
    if (!employeeDataDir) {
      missingItems.push('مجلد بيانات الموظفين (employee_database)');
    }

    if (missingItems.length > 0) {
      const missingText = missingItems.join(' و ');
      console.log('❌ عناصر مفقودة في النسخة الاحتياطية:', missingItems);

      // تحليل مفصل لمحتويات النسخة الاحتياطية
      console.log('🔍 تحليل مفصل لمحتويات النسخة الاحتياطية:');
      try {
        const analyzeContents = (dir, prefix = '', depth = 0) => {
          if (depth > 3) return; // منع التكرار العميق

          const entries = fs.readdirSync(dir, { withFileTypes: true });
          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);

            if (entry.isDirectory()) {
              console.log(`${prefix}📁 ${entry.name}/`);

              // فحص إذا كان هذا مجلد موظفين محتمل
              const lowerName = entry.name.toLowerCase();
              if (lowerName.includes('employee') || lowerName.includes('data') ||
                  lowerName === 'employees' || lowerName === 'uploads' || lowerName === 'files') {
                console.log(`${prefix}   ⭐ مجلد موظفين محتمل!`);
              }

              if (depth < 2) {
                analyzeContents(fullPath, prefix + '  ', depth + 1);
              }
            } else {
              const stats = fs.statSync(fullPath);
              console.log(`${prefix}📄 ${entry.name} (${stats.size} بايت)`);

              // فحص إذا كان هذا ملف قاعدة بيانات محتمل
              if (entry.name.endsWith('.db') ||
                  entry.name.toLowerCase().includes('archive') ||
                  entry.name.toLowerCase().includes('database')) {
                console.log(`${prefix}   ⭐ ملف قاعدة بيانات محتمل!`);
              }
            }
          }
        };

        analyzeContents(tempDir);

        // البحث عن أي ملفات .db
        console.log('\n🔍 البحث عن أي ملفات .db:');
        const findAllDbFiles = (dir, depth = 0) => {
          if (depth > 5) return;

          const entries = fs.readdirSync(dir, { withFileTypes: true });
          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);

            if (entry.isFile() && entry.name.endsWith('.db')) {
              const stats = fs.statSync(fullPath);
              console.log(`   📄 تم العثور على ملف .db: ${fullPath} (${stats.size} بايت)`);
            } else if (entry.isDirectory() && depth < 3) {
              findAllDbFiles(fullPath, depth + 1);
            }
          }
        };

        findAllDbFiles(tempDir);

      } catch (listErr) {
        console.error('❌ خطأ في تحليل محتويات النسخة الاحتياطية:', listErr);
      }

      // تنظيف المجلد المؤقت
      if (fs.existsSync(tempDir)) {
        fs.removeSync(tempDir);
      }

      throw new Error(`بنية ملف النسخة الاحتياطية غير صالحة - لم يتم العثور على: ${missingText}. تأكد من أن النسخة الاحتياطية تم إنشاؤها بواسطة هذا النظام.`);
    }

    console.log('✅ تم التحقق من صحة الملفات المستخرجة بنجاح');

    // إنشاء نسخة احتياطية من البيانات الحالية
    const backupDbPath = path.join(dataDir, 'archive.db.bak');
    const backupEmployeeDataDir = path.join(dataDir, 'employee_database.bak');

    console.log('💾 إنشاء نسخة احتياطية من البيانات الحالية...');

    // نسخ احتياطي لقاعدة البيانات الحالية إذا كانت موجودة
    const currentDbPath = path.join(dataDir, 'archive.db');
    if (fs.existsSync(currentDbPath)) {
      console.log(`📄 نسخ احتياطي لقاعدة البيانات: ${currentDbPath} -> ${backupDbPath}`);
      fs.copySync(currentDbPath, backupDbPath);
    }

    // نسخ احتياطي لبيانات الموظفين الحالية إذا كانت موجودة
    const currentEmployeeDataDir = path.join(dataDir, 'employee_database');
    if (fs.existsSync(currentEmployeeDataDir)) {
      console.log(`📁 نسخ احتياطي لبيانات الموظفين: ${currentEmployeeDataDir} -> ${backupEmployeeDataDir}`);
      fs.copySync(currentEmployeeDataDir, backupEmployeeDataDir);
    }

    console.log('✅ تم إكمال النسخ الاحتياطي للبيانات الحالية');

    // الآن محاولة استبدال الملفات
    try {
      console.log('🔄 بدء عملية استبدال البيانات...');

      // إزالة البيانات الحالية
      if (fs.existsSync(currentDbPath)) {
        console.log(`🗑️ إزالة ملف قاعدة البيانات الحالي: ${currentDbPath}`);
        fs.unlinkSync(currentDbPath);
      }

      if (fs.existsSync(currentEmployeeDataDir)) {
        console.log(`🗑️ إزالة مجلد بيانات الموظفين الحالي: ${currentEmployeeDataDir}`);
        fs.removeSync(currentEmployeeDataDir);
      }

      // نسخ البيانات الجديدة
      console.log(`📄 نسخ ملف قاعدة البيانات الجديد: ${dbPath} -> ${currentDbPath}`);
      fs.copySync(dbPath, currentDbPath);

      console.log(`📁 نسخ مجلد بيانات الموظفين الجديد: ${employeeDataDir} -> ${currentEmployeeDataDir}`);
      fs.copySync(employeeDataDir, currentEmployeeDataDir);

      console.log('✅ تم إكمال استبدال البيانات بنجاح');

      // تنظيف الملفات المؤقتة
      console.log('🧹 تنظيف الملفات المؤقتة...');
      fs.removeSync(tempDir);

      // إزالة النسخ الاحتياطية إذا تم كل شيء بنجاح
      if (fs.existsSync(backupDbPath)) {
        console.log(`🗑️ إزالة النسخة الاحتياطية لقاعدة البيانات: ${backupDbPath}`);
        fs.unlinkSync(backupDbPath);
      }

      if (fs.existsSync(backupEmployeeDataDir)) {
        console.log(`🗑️ إزالة النسخة الاحتياطية لبيانات الموظفين: ${backupEmployeeDataDir}`);
        fs.removeSync(backupEmployeeDataDir);
      }

      // تسجيل النشاط
      logActivity(req.user.id, 'restore_backup', `تم استعادة النسخة الاحتياطية: ${file.originalname}`);

      // إرجاع رسالة النجاح
      res.json({
        message: 'تم استعادة النسخة الاحتياطية بنجاح! ✅',
        details: 'تم استبدال جميع البيانات بنجاح. يمكنك الآن تصفح النظام لرؤية البيانات المستعادة.'
      });

    } catch (replaceErr) {
      console.error('❌ خطأ في استبدال الملفات:', replaceErr);

      // محاولة الاستعادة من النسخة الاحتياطية
      console.log('🔄 محاولة الاستعادة من النسخة الاحتياطية...');

      try {
        // استعادة قاعدة البيانات إذا كانت النسخة الاحتياطية موجودة
        if (fs.existsSync(backupDbPath)) {
          console.log(`📄 استعادة قاعدة البيانات من النسخة الاحتياطية: ${backupDbPath} -> ${currentDbPath}`);
          if (fs.existsSync(currentDbPath)) {
            fs.unlinkSync(currentDbPath);
          }
          fs.copySync(backupDbPath, currentDbPath);
          fs.unlinkSync(backupDbPath);
        }

        // استعادة بيانات الموظفين إذا كانت النسخة الاحتياطية موجودة
        if (fs.existsSync(backupEmployeeDataDir)) {
          console.log(`📁 استعادة بيانات الموظفين من النسخة الاحتياطية: ${backupEmployeeDataDir} -> ${currentEmployeeDataDir}`);
          if (fs.existsSync(currentEmployeeDataDir)) {
            fs.removeSync(currentEmployeeDataDir);
          }
          fs.copySync(backupEmployeeDataDir, currentEmployeeDataDir);
          fs.removeSync(backupEmployeeDataDir);
        }

        console.log('✅ تم استعادة البيانات الأصلية بنجاح');
      } catch (restoreErr) {
        console.error('❌ فشل في استعادة البيانات الأصلية:', restoreErr);
      }

      throw new Error(`فشل في استبدال الملفات: ${replaceErr.message}`);
    }

  } catch (err) {
    console.error('❌ خطأ في عملية استعادة النسخة الاحتياطية:', err);

    // تنظيف أي ملفات مؤقتة
    try {
      if (fs.existsSync(tempDir)) {
        console.log(`🧹 إزالة المجلد المؤقت: ${tempDir}`);
        fs.removeSync(tempDir);
      }

      // تنظيف أي ملفات نسخ احتياطية
      const backupDbPath = path.join(dataDir, 'archive.db.bak');
      const backupEmployeeDataDir = path.join(dataDir, 'employee_database.bak');

      if (fs.existsSync(backupDbPath)) {
        console.log(`🧹 إزالة النسخة الاحتياطية لقاعدة البيانات: ${backupDbPath}`);
        fs.unlinkSync(backupDbPath);
      }

      if (fs.existsSync(backupEmployeeDataDir)) {
        console.log(`🧹 إزالة النسخة الاحتياطية لبيانات الموظفين: ${backupEmployeeDataDir}`);
        fs.removeSync(backupEmployeeDataDir);
      }
    } catch (cleanupErr) {
      console.error('❌ خطأ أثناء التنظيف:', cleanupErr);
    }

    res.status(500).json({
      message: 'حدث خطأ أثناء استعادة النسخة الاحتياطية ❌',
      error: err.message,
      details: 'يرجى التحقق من صحة ملف النسخة الاحتياطية والمحاولة مرة أخرى.'
    });
  }
});

// Validate backup structure (admin only)
router.post('/validate', authenticateToken, isAdmin, upload.single('backup'), (req, res) => {
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'No backup file uploaded' });
  }

  try {
    console.log(`🔍 Validating backup structure for file: ${file.originalname}`);
    console.log(`📁 File path: ${file.path}`);
    console.log(`📏 File size: ${file.size} bytes`);

    // Create a temporary directory for extraction
    const tempDir = path.join(dataDir, 'temp-validate');
    console.log(`📂 Temp directory: ${tempDir}`);

    // Ensure temp directory is clean
    if (fs.existsSync(tempDir)) {
      console.log('🧹 Cleaning existing temp directory...');
      fs.removeSync(tempDir);
    }
    fs.ensureDirSync(tempDir);
    console.log('✅ Temp directory created');

    // Extract the backup
    const { execSync } = require('child_process');
    console.log('📦 Starting backup extraction...');

    try {
      execSync(`powershell -command "Expand-Archive -Path '${file.path}' -DestinationPath '${tempDir}' -Force"`, {
        stdio: 'pipe'
      });
      console.log('✅ Backup extraction completed');

      // Immediately check what was extracted
      console.log('🔍 Immediate post-extraction check:');
      const immediateEntries = fs.readdirSync(tempDir, { withFileTypes: true });
      console.log('📋 Root level contents:', immediateEntries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

    } catch (extractError) {
      console.error('❌ Extraction failed:', extractError.message);
      throw new Error(`فشل في استخراج النسخة الاحتياطية: ${extractError.message}`);
    }

    // Function to find files recursively with enhanced search
    const findFileOrDir = (dir, nameToFind, isDir = false, depth = 0) => {
      if (depth > 5) return null; // Prevent infinite recursion

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        console.log(`Searching in ${dir} (depth: ${depth}):`, entries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          // Exact match first
          if (entry.name === nameToFind) {
            if ((isDir && entry.isDirectory()) || (!isDir && entry.isFile())) {
              console.log(`✅ Found exact match: ${fullPath}`);
              return fullPath;
            }
          }

          // Enhanced search for database files
          if (!isDir && nameToFind === 'archive.db' && entry.isFile()) {
            console.log(`🔍 Checking file: ${entry.name} for database match`);
            if (entry.name.endsWith('.db') ||
                entry.name.toLowerCase().includes('archive') ||
                entry.name.toLowerCase().includes('database') ||
                entry.name.toLowerCase().includes('db')) {
              console.log(`✅ Found database file variant: ${fullPath}`);
              return fullPath;
            }
          }

          // Enhanced search for employee directory
          if (isDir && nameToFind === 'employee_database' && entry.isDirectory()) {
            console.log(`🔍 Checking directory: ${entry.name} for employee match`);
            const lowerName = entry.name.toLowerCase();
            if (lowerName.includes('employee') ||
                lowerName.includes('data') ||
                lowerName === 'employees' ||
                lowerName === 'employee_data' ||
                lowerName === 'emp_data' ||
                lowerName === 'uploads' ||
                lowerName === 'files') {
              console.log(`✅ Found employee directory variant: ${fullPath}`);
              return fullPath;
            }
          }

          // Search in subdirectories
          if (entry.isDirectory()) {
            const found = findFileOrDir(fullPath, nameToFind, isDir, depth + 1);
            if (found) return found;
          }
        }
      } catch (err) {
        console.error(`Error reading directory ${dir}:`, err.message);
      }

      return null;
    };

    // Check for required files with detailed logging
    console.log('\n🔍 Starting search for required files...');
    console.log('🎯 Looking for database file (archive.db)...');

    // First, let's see what's actually in the root directory
    console.log('\n📋 Root directory contents:');
    const rootEntries = fs.readdirSync(tempDir, { withFileTypes: true });
    rootEntries.forEach(entry => {
      const fullPath = path.join(tempDir, entry.name);
      if (entry.isDirectory()) {
        console.log(`📁 ${entry.name}/`);
      } else {
        const stats = fs.statSync(fullPath);
        console.log(`📄 ${entry.name} (${stats.size} bytes)`);
      }
    });

    const dbPath = findFileOrDir(tempDir, 'archive.db', false);
    console.log(`📄 Database search result: ${dbPath || 'NOT FOUND'}`);

    console.log('🎯 Looking for employee directory (employee_database)...');
    const employeeDir = findFileOrDir(tempDir, 'employee_database', true);
    console.log(`📁 Employee directory search result: ${employeeDir || 'NOT FOUND'}`);

    // If not found, let's do a comprehensive scan
    if (!dbPath || !employeeDir) {
      console.log('\n🔍 Comprehensive scan of all files and directories:');

      const comprehensiveScan = (dir, prefix = '', depth = 0) => {
        if (depth > 10) return { files: [], dirs: [] };

        const result = { files: [], dirs: [] };

        try {
          const entries = fs.readdirSync(dir, { withFileTypes: true });

          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            const relativePath = prefix + entry.name;

            if (entry.isDirectory()) {
              console.log(`📁 ${relativePath}/`);
              result.dirs.push({ name: entry.name, path: relativePath, fullPath });

              if (depth < 3) {
                const subResult = comprehensiveScan(fullPath, relativePath + '/', depth + 1);
                result.files.push(...subResult.files);
                result.dirs.push(...subResult.dirs);
              }
            } else {
              const stats = fs.statSync(fullPath);
              console.log(`📄 ${relativePath} (${stats.size} bytes)`);
              result.files.push({ name: entry.name, path: relativePath, fullPath, size: stats.size });
            }
          }
        } catch (err) {
          console.error(`❌ Error scanning ${dir}:`, err.message);
        }

        return result;
      };

      const scanResult = comprehensiveScan(tempDir);

      console.log('\n📊 Scan Summary:');
      console.log(`📄 Total files found: ${scanResult.files.length}`);
      console.log(`📁 Total directories found: ${scanResult.dirs.length}`);

      // Look for potential database files
      const potentialDbFiles = scanResult.files.filter(f =>
        f.name.endsWith('.db') ||
        f.name.toLowerCase().includes('archive') ||
        f.name.toLowerCase().includes('database') ||
        f.name.toLowerCase().includes('db')
      );

      console.log(`🎯 Potential database files: ${potentialDbFiles.length}`);
      potentialDbFiles.forEach(f => console.log(`   📄 ${f.path} (${f.size} bytes)`));

      // Look for potential employee directories
      const potentialEmployeeDirs = scanResult.dirs.filter(d => {
        const lowerName = d.name.toLowerCase();
        return lowerName.includes('employee') ||
               lowerName.includes('data') ||
               lowerName === 'employees' ||
               lowerName === 'uploads' ||
               lowerName === 'files';
      });

      console.log(`🎯 Potential employee directories: ${potentialEmployeeDirs.length}`);
      potentialEmployeeDirs.forEach(d => console.log(`   📁 ${d.path}/`));
    }

    // Enhanced content listing with analysis
    const listContents = (dir, prefix = '', depth = 0) => {
      if (depth > 5) return []; // Prevent too deep recursion

      const entries = fs.readdirSync(dir, { withFileTypes: true });
      const result = [];

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          const dirInfo = {
            type: 'directory',
            name: entry.name,
            path: prefix + entry.name,
            isPotentialEmployeeDir: false
          };

          // Check if this could be employee directory
          const lowerName = entry.name.toLowerCase();
          if (lowerName.includes('employee') || lowerName.includes('data') ||
              lowerName === 'employees' || lowerName === 'uploads' || lowerName === 'files') {
            dirInfo.isPotentialEmployeeDir = true;
          }

          result.push(dirInfo);

          if (depth < 3) {
            result.push(...listContents(fullPath, prefix + entry.name + '/', depth + 1));
          }
        } else {
          const stats = fs.statSync(fullPath);
          const fileInfo = {
            type: 'file',
            name: entry.name,
            path: prefix + entry.name,
            size: stats.size,
            isPotentialDbFile: false
          };

          // Check if this could be database file
          if (entry.name.endsWith('.db') ||
              entry.name.toLowerCase().includes('archive') ||
              entry.name.toLowerCase().includes('database')) {
            fileInfo.isPotentialDbFile = true;
          }

          result.push(fileInfo);
        }
      }
      return result;
    };

    const contents = listContents(tempDir);

    // Clean up
    fs.removeSync(tempDir);

    // Check for potential files
    const potentialDbFiles = contents.filter(item => item.type === 'file' && item.isPotentialDbFile);
    const potentialEmployeeDirs = contents.filter(item => item.type === 'directory' && item.isPotentialEmployeeDir);

    let detailedMessage = '';
    if (dbPath && employeeDir) {
      detailedMessage = 'النسخة الاحتياطية صالحة وتحتوي على جميع الملفات المطلوبة';
    } else {
      const missing = [];
      if (!dbPath) missing.push('ملف قاعدة البيانات');
      if (!employeeDir) missing.push('مجلد بيانات الموظفين');

      detailedMessage = `النسخة الاحتياطية غير صالحة - ملفات مفقودة: ${missing.join(' و ')}`;

      // Add suggestions if potential files found
      if (potentialDbFiles.length > 0 && !dbPath) {
        detailedMessage += `\n\nملفات محتملة لقاعدة البيانات: ${potentialDbFiles.map(f => f.name).join(', ')}`;
      }
      if (potentialEmployeeDirs.length > 0 && !employeeDir) {
        detailedMessage += `\n\nمجلدات محتملة لبيانات الموظفين: ${potentialEmployeeDirs.map(d => d.name).join(', ')}`;
      }
    }

    // Return validation result
    res.json({
      valid: !!(dbPath && employeeDir),
      database: {
        found: !!dbPath,
        path: dbPath ? path.relative(tempDir, dbPath) : null,
        potentialFiles: potentialDbFiles.map(f => ({ name: f.name, path: f.path }))
      },
      employeeData: {
        found: !!employeeDir,
        path: employeeDir ? path.relative(tempDir, employeeDir) : null,
        potentialDirs: potentialEmployeeDirs.map(d => ({ name: d.name, path: d.path }))
      },
      contents: contents,
      message: detailedMessage,
      suggestions: {
        hasDbSuggestions: potentialDbFiles.length > 0 && !dbPath,
        hasEmployeeSuggestions: potentialEmployeeDirs.length > 0 && !employeeDir
      }
    });

  } catch (err) {
    console.error('Error validating backup:', err);

    // Clean up on error
    const tempDir = path.join(dataDir, 'temp-validate');
    if (fs.existsSync(tempDir)) {
      fs.removeSync(tempDir);
    }

    res.status(500).json({
      message: 'خطأ في فحص النسخة الاحتياطية: ' + err.message,
      valid: false
    });
  }
});

// Delete backup (admin only)
router.delete('/:filename', authenticateToken, isAdmin, (req, res) => {
  const { filename } = req.params;
  const backupPath = path.join(backupDir, filename);

  try {
    // Check if backup file exists
    if (!fs.existsSync(backupPath) || !fs.statSync(backupPath).isFile()) {
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Delete backup file
    fs.unlinkSync(backupPath);

    // Log activity
    logActivity(req.user.id, 'delete_backup', `Deleted backup: ${filename}`);

    res.json({ message: `Backup ${filename} deleted successfully` });
  } catch (err) {
    console.error(`Error deleting backup ${filename}:`, err);
    res.status(500).json({ message: `Error deleting backup ${filename}` });
  }
});

module.exports = router;
