const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const archiver = require('archiver');
const multer = require('multer');
const { authenticateToken, isAdmin } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

const router = express.Router();

// Base directory for data
const dataDir = path.join(__dirname, '../data');
const backupDir = path.join(dataDir, 'backups');

// Ensure backup directory exists
fs.ensureDirSync(backupDir);

// Configure multer for backup uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, backupDir);
  },
  filename: (req, file, cb) => {
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 1024 * 1024 * 100 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    // Accept only zip files
    if (file.mimetype === 'application/zip' || file.originalname.endsWith('.zip')) {
      cb(null, true);
    } else {
      cb(new Error('Only ZIP files are allowed'));
    }
  }
});

// Create backup (admin only)
router.post('/create', authenticateToken, isAdmin, (req, res) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `backup-${timestamp}.zip`;
  const backupPath = path.join(backupDir, backupFileName);

  try {
    console.log(`Creating backup: ${backupFileName}`);

    // Create a file to stream archive data to
    const output = fs.createWriteStream(backupPath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Listen for all archive data to be written
    output.on('close', () => {
      console.log(`Backup created successfully: ${backupFileName} (${archive.pointer()} bytes)`);

      // Log activity
      logActivity(req.user.id, 'create_backup', `Created backup: ${backupFileName} (${archive.pointer()} bytes)`);

      res.json({
        message: 'Backup created successfully',
        backup: {
          name: backupFileName,
          path: backupPath,
          size: archive.pointer(),
          createdAt: new Date()
        }
      });
    });

    // Handle errors
    archive.on('error', (err) => {
      console.error(`Error creating backup: ${err.message}`);
      throw err;
    });

    // Pipe archive data to the file
    archive.pipe(output);

    // Add the database file
    const dbPath = path.join(dataDir, 'archive.db');
    if (fs.existsSync(dbPath)) {
      console.log(`Adding database file to backup: ${dbPath}`);
      archive.file(dbPath, { name: 'archive.db' });
    } else {
      console.error(`Database file not found: ${dbPath}`);
    }

    // Add the employee data directory
    const employeeDataDir = path.join(dataDir, 'employee_database');
    if (fs.existsSync(employeeDataDir)) {
      console.log(`Adding employee data directory to backup: ${employeeDataDir}`);
      archive.directory(employeeDataDir, 'employee_database');
    } else {
      console.error(`Employee data directory not found: ${employeeDataDir}`);
    }

    // Log the structure of the backup
    console.log('Backup structure:');
    console.log('- archive.db');
    console.log('- employee_database/');
    console.log('  - [employee folders]');

    // Finalize the archive
    archive.finalize();
  } catch (err) {
    console.error('Error creating backup:', err);
    res.status(500).json({ message: 'Error creating backup' });
  }
});

// Create and download backup (admin only)
router.get('/download', authenticateToken, isAdmin, (req, res) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFileName = `backup-${timestamp}.zip`;

  try {
    console.log(`Creating backup for direct download: ${backupFileName}`);

    // Create a zip archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Set the response headers for file download
    res.attachment(backupFileName);
    res.setHeader('Content-Type', 'application/zip');

    // Pipe the archive to the response
    archive.pipe(res);

    // Handle errors
    archive.on('error', (err) => {
      console.error(`Error creating backup for download: ${err.message}`);
      // إنهاء الاستجابة بحالة خطأ
      if (!res.headersSent) {
        res.status(500).json({ message: `Error creating backup: ${err.message}` });
      } else {
        res.end();
      }
    });

    // Add the database file
    const dbPath = path.join(dataDir, 'archive.db');
    if (fs.existsSync(dbPath)) {
      console.log(`Adding database file to download backup: ${dbPath}`);
      archive.file(dbPath, { name: 'archive.db' });
    } else {
      console.error(`Database file not found: ${dbPath}`);
      throw new Error('Database file not found');
    }

    // Add the employee data directory
    const employeeDataDir = path.join(dataDir, 'employee_database');
    if (fs.existsSync(employeeDataDir)) {
      console.log(`Adding employee data directory to download backup: ${employeeDataDir}`);
      archive.directory(employeeDataDir, 'employee_database');
    } else {
      console.error(`Employee data directory not found: ${employeeDataDir}`);
      throw new Error('Employee data directory not found');
    }

    // Log activity
    logActivity(req.user.id, 'download_backup', `Created and downloaded backup: ${backupFileName}`);

    // Listen for archive close event
    archive.on('end', () => {
      console.log(`Backup archive finalized: ${backupFileName}`);
    });

    // Finalize the archive
    archive.finalize();

    console.log('Archive finalization started');

  } catch (err) {
    console.error('Error creating backup for download:', err);
    res.status(500).json({ message: `Error creating backup for download: ${err.message}` });
  }
});

// Get all backups (admin only)
router.get('/', authenticateToken, isAdmin, (req, res) => {
  try {
    // Read all backup files
    const backups = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.zip'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);

        return {
          name: file,
          path: filePath,
          size: stats.size,
          createdAt: stats.birthtime
        };
      })
      .sort((a, b) => b.createdAt - a.createdAt); // Sort by creation date (newest first)

    res.json(backups);
  } catch (err) {
    console.error('Error reading backups:', err);
    res.status(500).json({ message: 'Error reading backups' });
  }
});

// Download backup (admin only)
router.get('/:filename', authenticateToken, isAdmin, (req, res) => {
  const { filename } = req.params;
  const backupPath = path.join(backupDir, filename);

  try {
    console.log(`Attempting to download backup: ${filename}`);
    console.log(`Full backup path: ${backupPath}`);
    console.log(`Current directory: ${__dirname}`);
    console.log(`Backup directory: ${backupDir}`);

    // Check if backup directory exists
    if (!fs.existsSync(backupDir)) {
      console.error(`Backup directory not found: ${backupDir}`);
      fs.ensureDirSync(backupDir);
      console.log(`Created backup directory: ${backupDir}`);
    }

    // Check if backup file exists
    if (!fs.existsSync(backupPath)) {
      console.error(`Backup file not found: ${backupPath}`);
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Check if it's a file
    const stats = fs.statSync(backupPath);
    if (!stats.isFile()) {
      console.error(`Path exists but is not a file: ${backupPath}`);
      return res.status(404).json({ message: 'Backup is not a valid file' });
    }

    console.log(`Backup file found: ${backupPath}, size: ${stats.size} bytes`);

    // Log activity
    logActivity(req.user.id, 'download_backup', `Downloaded backup: ${filename}`);

    // Set appropriate headers
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', stats.size);

    // Send file directly
    console.log(`Sending file: ${backupPath}`);
    fs.createReadStream(backupPath)
      .on('error', (err) => {
        console.error(`Error reading file: ${err.message}`);
        if (!res.headersSent) {
          res.status(500).send(`Error downloading backup: ${err.message}`);
        } else {
          res.end();
        }
      })
      .pipe(res)
      .on('finish', () => {
        console.log(`File download completed successfully: ${filename}`);
      });

  } catch (err) {
    console.error(`Error downloading backup ${filename}:`, err);
    res.status(500).json({ message: `Error downloading backup ${filename}: ${err.message}` });
  }
});

// Restore backup (admin only) - Simplified Direct Method
router.post('/restore', authenticateToken, isAdmin, upload.single('backup'), (req, res) => {
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'No backup file uploaded' });
  }

  try {
    console.log(`Starting simplified backup restore process for file: ${file.originalname}`);

    // Create a temporary directory for extraction
    const tempDir = path.join(dataDir, 'temp-restore');
    console.log(`Creating temporary directory: ${tempDir}`);

    // Ensure temp directory is clean
    if (fs.existsSync(tempDir)) {
      console.log('Removing existing temporary directory');
      fs.removeSync(tempDir);
    }

    fs.ensureDirSync(tempDir);
    console.log('Temporary directory created successfully');

    // Use a simpler approach with child_process
    const { execSync } = require('child_process');

    try {
      console.log(`Extracting backup file using system unzip: ${file.path} -> ${tempDir}`);

      // Use system unzip command which is more reliable
      execSync(`powershell -command "Expand-Archive -Path '${file.path}' -DestinationPath '${tempDir}' -Force"`, {
        stdio: 'inherit'
      });

      console.log('Extraction completed successfully');

      // List all files in the extracted directory to debug
      console.log('Listing all extracted files:');
      const listAllFiles = (dir, basePath = '') => {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          const relativePath = path.join(basePath, entry.name);
          if (entry.isDirectory()) {
            console.log(`Directory: ${relativePath}/`);
            listAllFiles(fullPath, relativePath);
          } else {
            console.log(`File: ${relativePath} (${fs.statSync(fullPath).size} bytes)`);
          }
        }
      };

      try {
        listAllFiles(tempDir);
      } catch (listErr) {
        console.error('Error listing files:', listErr);
      }

      // Try to find the database file and employee directory at any level
      console.log('Searching for database file and employee directory at any level...');

      // Function to find a file or directory recursively with enhanced search
      const findFileOrDir = (dir, nameToFind, isDir = false, depth = 0) => {
        if (depth > 5) return null; // Prevent infinite recursion

        try {
          const entries = fs.readdirSync(dir, { withFileTypes: true });
          console.log(`🔍 Searching in ${dir} (depth: ${depth}):`, entries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);

            // Exact match first
            if (entry.name === nameToFind && entry.isDirectory() === isDir) {
              console.log(`✅ Found exact match: ${fullPath}`);
              return fullPath;
            }

            // Enhanced search for database files
            if (!isDir && nameToFind === 'archive.db') {
              if (entry.name.endsWith('.db') ||
                  entry.name.toLowerCase().includes('archive') ||
                  entry.name.toLowerCase().includes('database') ||
                  entry.name.toLowerCase().includes('db')) {
                console.log(`✅ Found database file variant: ${entry.name} at ${fullPath}`);
                return fullPath;
              }
            }

            // Enhanced search for employee directory
            if (isDir && nameToFind === 'employee_database') {
              const lowerName = entry.name.toLowerCase();
              if (lowerName.includes('employee') ||
                  lowerName.includes('data') ||
                  lowerName === 'employees' ||
                  lowerName === 'employee_data' ||
                  lowerName === 'emp_data' ||
                  lowerName === 'uploads' ||
                  lowerName === 'files') {
                console.log(`✅ Found employee directory variant: ${entry.name} at ${fullPath}`);
                return fullPath;
              }
            }

            // Search in subdirectories
            if (entry.isDirectory()) {
              const found = findFileOrDir(fullPath, nameToFind, isDir, depth + 1);
              if (found) return found;
            }
          }
        } catch (err) {
          console.error(`❌ Error reading directory ${dir}:`, err.message);
        }

        return null;
      };

      // Try to find the database file
      let dbPath = findFileOrDir(tempDir, 'archive.db', false);
      if (!dbPath) {
        // If not found directly, look for data directory
        const dataDir = findFileOrDir(tempDir, 'data', true);
        if (dataDir) {
          dbPath = path.join(dataDir, 'archive.db');
          if (!fs.existsSync(dbPath)) {
            dbPath = null;
          }
        }
      }

      // Try to find the employee directory
      let employeeDataDir = findFileOrDir(tempDir, 'employee_database', true);
      if (!employeeDataDir) {
        // If not found directly, look for data directory
        const dataDir = findFileOrDir(tempDir, 'data', true);
        if (dataDir) {
          employeeDataDir = path.join(dataDir, 'employee_database');
          if (!fs.existsSync(employeeDataDir)) {
            employeeDataDir = null;
          }
        }
      }

      console.log(`Database file found: ${dbPath || 'Not found'}`);
      console.log(`Employee data directory found: ${employeeDataDir || 'Not found'}`);

      // Create detailed error message
      let errorDetails = [];
      if (!dbPath) {
        errorDetails.push('ملف قاعدة البيانات (archive.db)');
      }
      if (!employeeDataDir) {
        errorDetails.push('مجلد بيانات الموظفين (employee_database)');
      }

      if (!dbPath || !employeeDataDir) {
        const missingItems = errorDetails.join(' و ');
        console.log('Missing items in backup:', errorDetails);

        // List what was actually found with detailed analysis
        console.log('🔍 Detailed analysis of backup contents:');
        try {
          const analyzeContents = (dir, prefix = '', depth = 0) => {
            if (depth > 10) return; // Prevent too deep recursion

            const entries = fs.readdirSync(dir, { withFileTypes: true });
            for (const entry of entries) {
              const fullPath = path.join(dir, entry.name);

              if (entry.isDirectory()) {
                console.log(`${prefix}📁 ${entry.name}/`);

                // Check if this could be employee directory
                const lowerName = entry.name.toLowerCase();
                if (lowerName.includes('employee') || lowerName.includes('data') ||
                    lowerName === 'employees' || lowerName === 'uploads' || lowerName === 'files') {
                  console.log(`${prefix}   ⭐ Potential employee directory!`);
                }

                if (depth < 3) { // Limit depth but go deeper than before
                  analyzeContents(fullPath, prefix + '  ', depth + 1);
                }
              } else {
                const stats = fs.statSync(fullPath);
                console.log(`${prefix}📄 ${entry.name} (${stats.size} bytes)`);

                // Check if this could be database file
                if (entry.name.endsWith('.db') ||
                    entry.name.toLowerCase().includes('archive') ||
                    entry.name.toLowerCase().includes('database')) {
                  console.log(`${prefix}   ⭐ Potential database file!`);
                }
              }
            }
          };

          analyzeContents(tempDir);

          // Additional search for any .db files
          console.log('\n🔍 Searching for ANY .db files:');
          const findAllDbFiles = (dir, depth = 0) => {
            if (depth > 10) return;

            const entries = fs.readdirSync(dir, { withFileTypes: true });
            for (const entry of entries) {
              const fullPath = path.join(dir, entry.name);

              if (entry.isFile() && entry.name.endsWith('.db')) {
                const stats = fs.statSync(fullPath);
                console.log(`   📄 Found .db file: ${fullPath} (${stats.size} bytes)`);
              } else if (entry.isDirectory() && depth < 5) {
                findAllDbFiles(fullPath, depth + 1);
              }
            }
          };

          findAllDbFiles(tempDir);

        } catch (listErr) {
          console.error('❌ Error analyzing backup contents:', listErr);
        }

        throw new Error(`بنية ملف النسخة الاحتياطية غير صالحة - لم يتم العثور على: ${missingItems}. تأكد من أن النسخة الاحتياطية تم إنشاؤها بواسطة هذا النظام.`);
      }

      console.log('Extracted files validation successful');

      // Create a backup of current data
      const backupDbPath = path.join(dataDir, 'archive.db.bak');
      const backupEmployeeDataDir = path.join(dataDir, 'employee_database.bak');

      console.log('Creating backup of current data');

      // Backup current database if it exists
      const currentDbPath = path.join(dataDir, 'archive.db');
      if (fs.existsSync(currentDbPath)) {
        console.log(`Backing up current database: ${currentDbPath} -> ${backupDbPath}`);
        fs.copySync(currentDbPath, backupDbPath);
      }

      // Backup current employee data if it exists
      const currentEmployeeDataDir = path.join(dataDir, 'employee_database');
      if (fs.existsSync(currentEmployeeDataDir)) {
        console.log(`Backing up current employee data: ${currentEmployeeDataDir} -> ${backupEmployeeDataDir}`);
        fs.copySync(currentEmployeeDataDir, backupEmployeeDataDir);
      }

      console.log('Backup of current data completed');

      // Now try to replace the files
      try {
        // Remove current data
        if (fs.existsSync(currentDbPath)) {
          console.log(`Removing current database file: ${currentDbPath}`);
          fs.unlinkSync(currentDbPath);
        }

        if (fs.existsSync(currentEmployeeDataDir)) {
          console.log(`Removing current employee data directory: ${currentEmployeeDataDir}`);
          fs.removeSync(currentEmployeeDataDir);
        }

        // Copy new data
        console.log(`Copying new database file: ${dbPath} -> ${currentDbPath}`);
        fs.copySync(dbPath, currentDbPath);

        console.log(`Copying new employee data directory: ${employeeDataDir} -> ${currentEmployeeDataDir}`);
        fs.copySync(employeeDataDir, currentEmployeeDataDir);

        console.log('Data replacement completed successfully');

        // Clean up
        console.log('Cleaning up temporary files');
        fs.removeSync(tempDir);

        // Remove backups if everything went well
        if (fs.existsSync(backupDbPath)) {
          console.log(`Removing database backup: ${backupDbPath}`);
          fs.unlinkSync(backupDbPath);
        }

        if (fs.existsSync(backupEmployeeDataDir)) {
          console.log(`Removing employee data backup: ${backupEmployeeDataDir}`);
          fs.removeSync(backupEmployeeDataDir);
        }

        // Log activity
        logActivity(req.user.id, 'restore_backup', `Restored backup: ${file.originalname}`);

        // Return success
        res.json({ message: 'تم استعادة النسخة الاحتياطية بنجاح. يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.' });

      } catch (replaceErr) {
        console.error('Error replacing files:', replaceErr);

        // Try to restore from backup
        console.log('Attempting to restore from backup');

        // Restore database if backup exists
        if (fs.existsSync(backupDbPath)) {
          console.log(`Restoring database from backup: ${backupDbPath} -> ${currentDbPath}`);
          if (fs.existsSync(currentDbPath)) {
            fs.unlinkSync(currentDbPath);
          }
          fs.copySync(backupDbPath, currentDbPath);
          fs.unlinkSync(backupDbPath);
        }

        // Restore employee data if backup exists
        if (fs.existsSync(backupEmployeeDataDir)) {
          console.log(`Restoring employee data from backup: ${backupEmployeeDataDir} -> ${currentEmployeeDataDir}`);
          if (fs.existsSync(currentEmployeeDataDir)) {
            fs.removeSync(currentEmployeeDataDir);
          }
          fs.copySync(backupEmployeeDataDir, currentEmployeeDataDir);
          fs.removeSync(backupEmployeeDataDir);
        }

        throw replaceErr;
      }

    } catch (extractErr) {
      console.error('Error during extraction or file replacement:', extractErr);

      // Clean up
      if (fs.existsSync(tempDir)) {
        console.log(`Removing temporary directory: ${tempDir}`);
        fs.removeSync(tempDir);
      }

      throw new Error('فشل في استخراج أو استبدال ملفات النسخة الاحتياطية: ' + extractErr.message);
    }

  } catch (err) {
    console.error('Error in backup restore process:', err);

    // Clean up any temporary files
    try {
      const tempDir = path.join(dataDir, 'temp-restore');
      if (fs.existsSync(tempDir)) {
        fs.removeSync(tempDir);
      }

      // Also clean up any backup files
      const backupDbPath = path.join(dataDir, 'archive.db.bak');
      const backupEmployeeDataDir = path.join(dataDir, 'employee_database.bak');

      if (fs.existsSync(backupDbPath)) {
        fs.unlinkSync(backupDbPath);
      }

      if (fs.existsSync(backupEmployeeDataDir)) {
        fs.removeSync(backupEmployeeDataDir);
      }
    } catch (cleanupErr) {
      console.error('Error during cleanup:', cleanupErr);
    }

    res.status(500).json({ message: 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + err.message });
  }
});

// Validate backup structure (admin only)
router.post('/validate', authenticateToken, isAdmin, upload.single('backup'), (req, res) => {
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'No backup file uploaded' });
  }

  try {
    console.log(`🔍 Validating backup structure for file: ${file.originalname}`);
    console.log(`📁 File path: ${file.path}`);
    console.log(`📏 File size: ${file.size} bytes`);

    // Create a temporary directory for extraction
    const tempDir = path.join(dataDir, 'temp-validate');
    console.log(`📂 Temp directory: ${tempDir}`);

    // Ensure temp directory is clean
    if (fs.existsSync(tempDir)) {
      console.log('🧹 Cleaning existing temp directory...');
      fs.removeSync(tempDir);
    }
    fs.ensureDirSync(tempDir);
    console.log('✅ Temp directory created');

    // Extract the backup
    const { execSync } = require('child_process');
    console.log('📦 Starting backup extraction...');

    try {
      execSync(`powershell -command "Expand-Archive -Path '${file.path}' -DestinationPath '${tempDir}' -Force"`, {
        stdio: 'pipe'
      });
      console.log('✅ Backup extraction completed');

      // Immediately check what was extracted
      console.log('🔍 Immediate post-extraction check:');
      const immediateEntries = fs.readdirSync(tempDir, { withFileTypes: true });
      console.log('📋 Root level contents:', immediateEntries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

    } catch (extractError) {
      console.error('❌ Extraction failed:', extractError.message);
      throw new Error(`فشل في استخراج النسخة الاحتياطية: ${extractError.message}`);
    }

    // Function to find files recursively with enhanced search
    const findFileOrDir = (dir, nameToFind, isDir = false, depth = 0) => {
      if (depth > 5) return null; // Prevent infinite recursion

      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        console.log(`Searching in ${dir} (depth: ${depth}):`, entries.map(e => `${e.isDirectory() ? '📁' : '📄'} ${e.name}`));

        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);

          // Exact match first
          if (entry.name === nameToFind) {
            if ((isDir && entry.isDirectory()) || (!isDir && entry.isFile())) {
              console.log(`✅ Found exact match: ${fullPath}`);
              return fullPath;
            }
          }

          // Enhanced search for database files
          if (!isDir && nameToFind === 'archive.db' && entry.isFile()) {
            console.log(`🔍 Checking file: ${entry.name} for database match`);
            if (entry.name.endsWith('.db') ||
                entry.name.toLowerCase().includes('archive') ||
                entry.name.toLowerCase().includes('database') ||
                entry.name.toLowerCase().includes('db')) {
              console.log(`✅ Found database file variant: ${fullPath}`);
              return fullPath;
            }
          }

          // Enhanced search for employee directory
          if (isDir && nameToFind === 'employee_database' && entry.isDirectory()) {
            console.log(`🔍 Checking directory: ${entry.name} for employee match`);
            const lowerName = entry.name.toLowerCase();
            if (lowerName.includes('employee') ||
                lowerName.includes('data') ||
                lowerName === 'employees' ||
                lowerName === 'employee_data' ||
                lowerName === 'emp_data' ||
                lowerName === 'uploads' ||
                lowerName === 'files') {
              console.log(`✅ Found employee directory variant: ${fullPath}`);
              return fullPath;
            }
          }

          // Search in subdirectories
          if (entry.isDirectory()) {
            const found = findFileOrDir(fullPath, nameToFind, isDir, depth + 1);
            if (found) return found;
          }
        }
      } catch (err) {
        console.error(`Error reading directory ${dir}:`, err.message);
      }

      return null;
    };

    // Check for required files with detailed logging
    console.log('\n🔍 Starting search for required files...');
    console.log('🎯 Looking for database file (archive.db)...');

    // First, let's see what's actually in the root directory
    console.log('\n📋 Root directory contents:');
    const rootEntries = fs.readdirSync(tempDir, { withFileTypes: true });
    rootEntries.forEach(entry => {
      const fullPath = path.join(tempDir, entry.name);
      if (entry.isDirectory()) {
        console.log(`📁 ${entry.name}/`);
      } else {
        const stats = fs.statSync(fullPath);
        console.log(`📄 ${entry.name} (${stats.size} bytes)`);
      }
    });

    const dbPath = findFileOrDir(tempDir, 'archive.db', false);
    console.log(`📄 Database search result: ${dbPath || 'NOT FOUND'}`);

    console.log('🎯 Looking for employee directory (employee_database)...');
    const employeeDir = findFileOrDir(tempDir, 'employee_database', true);
    console.log(`📁 Employee directory search result: ${employeeDir || 'NOT FOUND'}`);

    // If not found, let's do a comprehensive scan
    if (!dbPath || !employeeDir) {
      console.log('\n🔍 Comprehensive scan of all files and directories:');

      const comprehensiveScan = (dir, prefix = '', depth = 0) => {
        if (depth > 10) return { files: [], dirs: [] };

        const result = { files: [], dirs: [] };

        try {
          const entries = fs.readdirSync(dir, { withFileTypes: true });

          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            const relativePath = prefix + entry.name;

            if (entry.isDirectory()) {
              console.log(`📁 ${relativePath}/`);
              result.dirs.push({ name: entry.name, path: relativePath, fullPath });

              if (depth < 3) {
                const subResult = comprehensiveScan(fullPath, relativePath + '/', depth + 1);
                result.files.push(...subResult.files);
                result.dirs.push(...subResult.dirs);
              }
            } else {
              const stats = fs.statSync(fullPath);
              console.log(`📄 ${relativePath} (${stats.size} bytes)`);
              result.files.push({ name: entry.name, path: relativePath, fullPath, size: stats.size });
            }
          }
        } catch (err) {
          console.error(`❌ Error scanning ${dir}:`, err.message);
        }

        return result;
      };

      const scanResult = comprehensiveScan(tempDir);

      console.log('\n📊 Scan Summary:');
      console.log(`📄 Total files found: ${scanResult.files.length}`);
      console.log(`📁 Total directories found: ${scanResult.dirs.length}`);

      // Look for potential database files
      const potentialDbFiles = scanResult.files.filter(f =>
        f.name.endsWith('.db') ||
        f.name.toLowerCase().includes('archive') ||
        f.name.toLowerCase().includes('database') ||
        f.name.toLowerCase().includes('db')
      );

      console.log(`🎯 Potential database files: ${potentialDbFiles.length}`);
      potentialDbFiles.forEach(f => console.log(`   📄 ${f.path} (${f.size} bytes)`));

      // Look for potential employee directories
      const potentialEmployeeDirs = scanResult.dirs.filter(d => {
        const lowerName = d.name.toLowerCase();
        return lowerName.includes('employee') ||
               lowerName.includes('data') ||
               lowerName === 'employees' ||
               lowerName === 'uploads' ||
               lowerName === 'files';
      });

      console.log(`🎯 Potential employee directories: ${potentialEmployeeDirs.length}`);
      potentialEmployeeDirs.forEach(d => console.log(`   📁 ${d.path}/`));
    }

    // Enhanced content listing with analysis
    const listContents = (dir, prefix = '', depth = 0) => {
      if (depth > 5) return []; // Prevent too deep recursion

      const entries = fs.readdirSync(dir, { withFileTypes: true });
      const result = [];

      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        if (entry.isDirectory()) {
          const dirInfo = {
            type: 'directory',
            name: entry.name,
            path: prefix + entry.name,
            isPotentialEmployeeDir: false
          };

          // Check if this could be employee directory
          const lowerName = entry.name.toLowerCase();
          if (lowerName.includes('employee') || lowerName.includes('data') ||
              lowerName === 'employees' || lowerName === 'uploads' || lowerName === 'files') {
            dirInfo.isPotentialEmployeeDir = true;
          }

          result.push(dirInfo);

          if (depth < 3) {
            result.push(...listContents(fullPath, prefix + entry.name + '/', depth + 1));
          }
        } else {
          const stats = fs.statSync(fullPath);
          const fileInfo = {
            type: 'file',
            name: entry.name,
            path: prefix + entry.name,
            size: stats.size,
            isPotentialDbFile: false
          };

          // Check if this could be database file
          if (entry.name.endsWith('.db') ||
              entry.name.toLowerCase().includes('archive') ||
              entry.name.toLowerCase().includes('database')) {
            fileInfo.isPotentialDbFile = true;
          }

          result.push(fileInfo);
        }
      }
      return result;
    };

    const contents = listContents(tempDir);

    // Clean up
    fs.removeSync(tempDir);

    // Check for potential files
    const potentialDbFiles = contents.filter(item => item.type === 'file' && item.isPotentialDbFile);
    const potentialEmployeeDirs = contents.filter(item => item.type === 'directory' && item.isPotentialEmployeeDir);

    let detailedMessage = '';
    if (dbPath && employeeDir) {
      detailedMessage = 'النسخة الاحتياطية صالحة وتحتوي على جميع الملفات المطلوبة';
    } else {
      const missing = [];
      if (!dbPath) missing.push('ملف قاعدة البيانات');
      if (!employeeDir) missing.push('مجلد بيانات الموظفين');

      detailedMessage = `النسخة الاحتياطية غير صالحة - ملفات مفقودة: ${missing.join(' و ')}`;

      // Add suggestions if potential files found
      if (potentialDbFiles.length > 0 && !dbPath) {
        detailedMessage += `\n\nملفات محتملة لقاعدة البيانات: ${potentialDbFiles.map(f => f.name).join(', ')}`;
      }
      if (potentialEmployeeDirs.length > 0 && !employeeDir) {
        detailedMessage += `\n\nمجلدات محتملة لبيانات الموظفين: ${potentialEmployeeDirs.map(d => d.name).join(', ')}`;
      }
    }

    // Return validation result
    res.json({
      valid: !!(dbPath && employeeDir),
      database: {
        found: !!dbPath,
        path: dbPath ? path.relative(tempDir, dbPath) : null,
        potentialFiles: potentialDbFiles.map(f => ({ name: f.name, path: f.path }))
      },
      employeeData: {
        found: !!employeeDir,
        path: employeeDir ? path.relative(tempDir, employeeDir) : null,
        potentialDirs: potentialEmployeeDirs.map(d => ({ name: d.name, path: d.path }))
      },
      contents: contents,
      message: detailedMessage,
      suggestions: {
        hasDbSuggestions: potentialDbFiles.length > 0 && !dbPath,
        hasEmployeeSuggestions: potentialEmployeeDirs.length > 0 && !employeeDir
      }
    });

  } catch (err) {
    console.error('Error validating backup:', err);

    // Clean up on error
    const tempDir = path.join(dataDir, 'temp-validate');
    if (fs.existsSync(tempDir)) {
      fs.removeSync(tempDir);
    }

    res.status(500).json({
      message: 'خطأ في فحص النسخة الاحتياطية: ' + err.message,
      valid: false
    });
  }
});

// Delete backup (admin only)
router.delete('/:filename', authenticateToken, isAdmin, (req, res) => {
  const { filename } = req.params;
  const backupPath = path.join(backupDir, filename);

  try {
    // Check if backup file exists
    if (!fs.existsSync(backupPath) || !fs.statSync(backupPath).isFile()) {
      return res.status(404).json({ message: 'Backup not found' });
    }

    // Delete backup file
    fs.unlinkSync(backupPath);

    // Log activity
    logActivity(req.user.id, 'delete_backup', `Deleted backup: ${filename}`);

    res.json({ message: `Backup ${filename} deleted successfully` });
  } catch (err) {
    console.error(`Error deleting backup ${filename}:`, err);
    res.status(500).json({ message: `Error deleting backup ${filename}` });
  }
});

module.exports = router;
