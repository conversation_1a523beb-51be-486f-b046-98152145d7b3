const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { db } = require('../db/database');
const { logActivity } = require('../middleware/logger');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';

// Login route
router.post('/login', (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({ message: 'Username and password are required' });
  }

  db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    bcrypt.compare(password, user.password, (err, isMatch) => {
      if (err) {
        console.error('Password comparison error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      if (!isMatch) {
        return res.status(401).json({ message: 'Invalid credentials' });
      }

      // Create JWT token
      const token = jwt.sign(
        { id: user.id, username: user.username, role: user.role },
        JWT_SECRET,
        { expiresIn: '24h' }
      );

      // Log the login activity
      logActivity(user.id, 'login', 'User logged in');

      res.json({
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      });
    });
  });
});

// Register route (admin only)
router.post('/register', (req, res) => {
  const { username, password, role } = req.body;
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ message: 'Authorization required' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, JWT_SECRET);

    // Only admin can register new users
    if (decoded.role !== 'admin') {
      return res.status(403).json({ message: 'Admin privileges required' });
    }

    if (!username || !password || !role) {
      return res.status(400).json({ message: 'Username, password, and role are required' });
    }

    // Check if username already exists
    db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      if (user) {
        return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
      }

      // Hash password
      bcrypt.hash(password, 10, (err, hash) => {
        if (err) {
          console.error('Password hashing error:', err);
          return res.status(500).json({ message: 'Internal server error' });
        }

        // Insert new user
        db.run(
          'INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
          [username, hash, role],
          function(err) {
            if (err) {
              console.error('Database error:', err);
              return res.status(500).json({ message: 'Internal server error' });
            }

            // Log the user creation
            logActivity(decoded.id, 'user_create', `Created user: ${username} with role: ${role}`);

            res.status(201).json({
              message: 'User created successfully',
              userId: this.lastID
            });
          }
        );
      });
    });
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({ message: 'Invalid token' });
  }
});

// Verify token route
router.get('/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    return res.status(401).json({ message: 'Authorization required' });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    res.json({
      user: {
        id: decoded.id,
        username: decoded.username,
        role: decoded.role
      }
    });
  } catch (err) {
    console.error('Token verification error:', err);
    return res.status(401).json({ message: 'Invalid token' });
  }
});

// Change password route
router.post('/change-password', authenticateToken, (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({ message: 'كلمة المرور الحالية والجديدة مطلوبة' });
  }

  // Validate new password
  if (newPassword.length < 6) {
    return res.status(400).json({ message: 'كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل' });
  }

  // Get user from database
  db.get('SELECT * FROM users WHERE id = ?', [userId], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'حدث خطأ في قاعدة البيانات' });
    }

    if (!user) {
      return res.status(404).json({ message: 'المستخدم غير موجود' });
    }

    // Verify current password
    bcrypt.compare(currentPassword, user.password, (err, isMatch) => {
      if (err) {
        console.error('Password comparison error:', err);
        return res.status(500).json({ message: 'حدث خطأ أثناء التحقق من كلمة المرور' });
      }

      if (!isMatch) {
        return res.status(401).json({ message: 'كلمة المرور الحالية غير صحيحة' });
      }

      // Hash new password
      bcrypt.hash(newPassword, 10, (err, hash) => {
        if (err) {
          console.error('Password hashing error:', err);
          return res.status(500).json({ message: 'حدث خطأ أثناء تشفير كلمة المرور' });
        }

        // Update password in database
        db.run(
          'UPDATE users SET password = ? WHERE id = ?',
          [hash, userId],
          (err) => {
            if (err) {
              console.error('Database error:', err);
              return res.status(500).json({ message: 'حدث خطأ أثناء تحديث كلمة المرور' });
            }

            // Log the password change
            logActivity(userId, 'change_password', 'Changed password');

            res.json({ message: 'تم تغيير كلمة المرور بنجاح' });
          }
        );
      });
    });
  });
});

module.exports = router;
