# نظام أرشفة ملفات الموظفين

نظام شامل لإدارة وأرشفة ملفات الموظفين مع واجهة عربية سهلة الاستخدام.

## 🚀 البدء السريع

### Windows (الطريقة الأسهل)
1. شغ<PERSON> ملف `setup.bat` كمدير (للإعداد الأولي)
2. شغل ملف `start.bat` لبدء التطبيق
3. افتح المتصفح واذهب إلى `http://localhost:5173`

### بيانات تسجيل الدخول الافتراضية
- **المدير**: `admin` / `admin123`
- **المستخدم**: `user` / `user123`

## 📚 الدليل الشامل

للحصول على دليل مفصل وشامل لجميع ميزات النظام، يرجى مراجعة:

**[📖 الدليل الشامل للمستخدم](COMPLETE_USER_GUIDE.md)**

يتضمن الدليل الشامل:
- ✅ التثبيت والإعداد التفصيلي
- ✅ دليل الاستخدام الكامل
- ✅ إدارة النسخ الاحتياطية
- ✅ استكشاف الأخطاء وإصلاحها
- ✅ الأسئلة الشائعة
- ✅ الدعم الفني

## ⚡ الميزات الرئيسية

- 📁 **إدارة ملفات الموظفين** - تنظيم وأرشفة شاملة
- 🔍 **البحث المتقدم** - في الملفات والموظفين
- 👥 **إدارة المستخدمين** - نظام صلاحيات متقدم
- 💾 **النسخ الاحتياطية** - إنشاء واستعادة آمنة
- 📱 **واجهة متجاوبة** - تعمل على جميع الأجهزة
- 🔒 **أمان متقدم** - حماية البيانات والملفات
- 🌐 **دعم عربي كامل** - واجهة باللغة العربية

## 🛠️ التقنيات المستخدمة

### الخادم الخلفي
- Node.js + Express.js
- SQLite Database
- JWT Authentication
- Multer File Upload

### الواجهة الأمامية
- React + Vite
- Tailwind CSS
- React Router
- Axios

## 📞 الدعم

للحصول على المساعدة:
1. راجع [الدليل الشامل](COMPLETE_USER_GUIDE.md)
2. تحقق من قسم استكشاف الأخطاء
3. راجع ملفات السجل في وحدة التحكم
4. تواصل مع فريق الدعم

## 🔄 التحديثات

للحصول على آخر التحديثات:
```bash
git pull origin main
npm install
```

---

**📋 للحصول على التعليمات الكاملة والمفصلة، يرجى مراجعة [الدليل الشامل للمستخدم](COMPLETE_USER_GUIDE.md)**

---

تم إنشاء هذا النظام بواسطة: حامد الفهد
http://www.fb.com/prog.hamid
