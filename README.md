# نظام أرشفة ملفات الموظفين

نظام متكامل لإدارة وأرشفة ملفات الموظفين بواجهة عربية سهلة الاستخدام.

## المميزات

- تصفح واستعراض ملفات الموظفين
- البحث عن موظف أو ملف
- رفع ملفات PDF للموظفين
- إدارة المستخدمين والصلاحيات
- سجل النشاط
- النسخ الاحتياطي والاستعادة

## متطلبات النظام

- نظام تشغيل Windows 10 أو أحدث
- مساحة قرص صلب لا تقل عن 500 ميجابايت
- ذاكرة وصول عشوائي (RAM) لا تقل عن 4 جيجابايت

## طريقة نقل النظام إلى حاسوب آخر

### الطريقة السريعة (موصى بها)

1. ان<PERSON><PERSON> مجلد `employee-archive-system` بالكامل إلى الحاسوب الجديد
2. قم بتشغيل ملف `setup.bat` بالنقر المزدوج عليه
3. انتظر حتى تكتمل عملية التثبيت وتشغيل النظام تلقائياً

### الطريقة اليدوية

إذا واجهتك مشكلة مع الطريقة السريعة، يمكنك اتباع الخطوات التالية:

1. تأكد من تثبيت Node.js على الحاسوب الجديد:
   - قم بتحميل وتثبيت Node.js من الموقع الرسمي: https://nodejs.org/
   - اختر الإصدار LTS (دعم طويل الأمد)

2. انسخ مجلد `employee-archive-system` بالكامل إلى الحاسوب الجديد

3. افتح موجه الأوامر (Command Prompt) بصلاحيات المسؤول:
   - اضغط على زر Windows + X
   - اختر "موجه الأوامر (مسؤول)" أو "Windows PowerShell (مسؤول)"

4. انتقل إلى مجلد النظام:
   ```
   cd مسار\إلى\employee-archive-system
   ```

5. قم بتثبيت اعتماديات الخادم الخلفي:
   ```
   cd backend
   npm install
   cd ..
   ```

6. قم بتثبيت اعتماديات الواجهة الأمامية:
   ```
   cd frontend
   npm install
   cd ..
   ```

7. قم بتشغيل النظام:
   ```
   start.bat
   ```

## طريقة تشغيل النظام

بعد اكتمال عملية التثبيت، يمكنك تشغيل النظام بإحدى الطرق التالية:

1. النقر المزدوج على ملف `start.bat` في مجلد النظام
2. إنشاء اختصار لملف `start.bat` على سطح المكتب والنقر عليه

## بيانات الدخول الافتراضية

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## النسخ الاحتياطي للبيانات

للحفاظ على بيانات النظام، يُنصح بعمل نسخة احتياطية بشكل دوري:

1. من داخل النظام، انتقل إلى صفحة "النسخ الاحتياطي"
2. انقر على زر "إنشاء نسخة احتياطية جديدة"
3. احفظ ملف النسخة الاحتياطية في مكان آمن

## استعادة البيانات

لاستعادة البيانات من نسخة احتياطية:

1. من داخل النظام، انتقل إلى صفحة "النسخ الاحتياطي"
2. انقر على زر "استعادة من نسخة احتياطية"
3. اختر ملف النسخة الاحتياطية المراد استعادته

## حل المشكلات الشائعة

### النظام لا يعمل بعد التثبيت

1. تأكد من تثبيت Node.js بشكل صحيح
2. تأكد من وجود اتصال بالإنترنت أثناء عملية التثبيت الأولى
3. جرب تشغيل ملف `setup.bat` مرة أخرى بصلاحيات المسؤول

### رسالة خطأ "لا يمكن الاتصال بالخادم"

1. تأكد من تشغيل الخادم الخلفي
2. أعد تشغيل النظام بالكامل باستخدام ملف `start.bat`

### مشكلة في تسجيل الدخول

1. استخدم بيانات الدخول الافتراضية:
   - اسم المستخدم: admin
   - كلمة المرور: admin123

### ظهور رموز غريبة في نافذة موجه الأوامر (CMD)

1. هذه مشكلة طبيعية تتعلق بعدم دعم موجه الأوامر للغة العربية بشكل صحيح
2. لا تقلق، فهذا لا يؤثر على عمل النظام
3. واجهة المستخدم الرسومية في المتصفح ستظهر باللغة العربية بشكل صحيح
4. تم تعديل ملفات البرنامج لاستخدام اللغة الإنجليزية في واجهة سطر الأوامر لتجنب هذه المشكلة

## الصلاحيات

النظام يدعم ثلاثة أنواع من الصلاحيات:

1. **مدير (admin)**: يملك كافة الصلاحيات في النظام
2. **محرر (editor)**: يستطيع عرض وإضافة وتعديل الملفات
3. **مشاهد (viewer)**: يستطيع فقط عرض الملفات

---

تم إنشاء هذا النظام بواسطة: حامد الفهد
http://www.fb.com/prog.hamid
