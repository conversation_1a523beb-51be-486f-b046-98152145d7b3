import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FiUsers, FiFile, FiActivity, FiSearch } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    employeeCount: 0,
    fileCount: 0,
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState({ files: [], employees: [] });
  const [searching, setSearching] = useState(false);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Fetch employee count
        const employeesResponse = await axios.get('/api/employees');
        const employeeCount = employeesResponse.data.length;

        // Fetch file count (from all employees)
        let fileCount = 0;
        for (const employee of employeesResponse.data) {
          const filesResponse = await axios.get(`/api/files/employee/${employee.name}`);
          fileCount += filesResponse.data.length;
        }

        // Fetch recent activity
        let recentActivity = [];
        if (user.role === 'admin') {
          const logsResponse = await axios.get('/api/logs?limit=5');
          recentActivity = logsResponse.data;
        } else {
          const logsResponse = await axios.get('/api/logs/user/me?limit=5');
          recentActivity = logsResponse.data;
        }

        setStats({
          employeeCount,
          fileCount,
          recentActivity
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [user.role]);

  // Effect for auto search when typing
  useEffect(() => {
    // Debounce function to prevent too many API calls
    const debounceTimeout = setTimeout(() => {
      if (searchQuery.trim()) {
        performSearch();
      } else {
        // Clear results if search query is empty
        setSearchResults({ files: [], employees: [] });
      }
    }, 300); // Wait 300ms after typing stops before searching

    return () => clearTimeout(debounceTimeout); // Cleanup timeout
  }, [searchQuery]);

  // Function to perform search - used by both auto-search and manual search
  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearching(true);

      // Search for files
      const filesResponse = await axios.get(`/api/files/search?query=${searchQuery}`);

      // Search for employees (by checking if any employee name contains the search query)
      const employeesResponse = await axios.get('/api/employees');
      const matchingEmployees = employeesResponse.data.filter(
        employee => employee.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

      // Combine results
      setSearchResults({
        files: filesResponse.data,
        employees: matchingEmployees
      });
    } catch (error) {
      console.error('Error searching:', error);
    } finally {
      setSearching(false);
    }
  };

  // Handle manual search form submission
  const handleSearch = async (e) => {
    e.preventDefault();
    performSearch();
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionText = (action) => {
    switch (action) {
      case 'login':
        return 'تسجيل دخول';
      case 'view_employee':
        return 'عرض موظف';
      case 'view_file':
        return 'عرض ملف';
      case 'upload_file':
        return 'رفع ملف';
      case 'delete_file':
        return 'حذف ملف';
      case 'create_employee':
        return 'إنشاء موظف';
      case 'delete_employee':
        return 'حذف موظف';
      case 'create_user':
        return 'إنشاء مستخدم';
      case 'update_user':
        return 'تحديث مستخدم';
      case 'delete_user':
        return 'حذف مستخدم';
      case 'create_backup':
        return 'إنشاء نسخة احتياطية';
      case 'restore_backup':
        return 'استعادة نسخة احتياطية';
      default:
        return action;
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold text-gray-800 mb-6">لوحة التحكم</h1>

      {/* Search */}
      <div className="card mb-6">
        <h2 className="text-lg font-semibold text-gray-700 mb-4">البحث السريع</h2>
        <div className="relative flex-1">
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <FiSearch className="text-gray-400" />
          </div>
          <input
            type="text"
            className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all"
            placeholder="ابحث عن موظف أو ملف..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            autoFocus
          />
          {searching && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <div className="animate-spin h-4 w-4 border-2 border-primary-500 rounded-full border-t-transparent"></div>
            </div>
          )}
          {searchQuery && !searching && (
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <button
                onClick={() => setSearchQuery('')}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
                aria-label="مسح البحث"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Search Results */}
        {searchQuery.trim() && (
          <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden">
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-md font-semibold text-gray-700">نتائج البحث</h3>
                {(searchResults.employees.length > 0 || searchResults.files.length > 0) && (
                  <span className="text-xs bg-primary-100 text-primary-800 py-1 px-2 rounded-full">
                    {searchResults.employees.length + searchResults.files.length} نتيجة
                  </span>
                )}
              </div>
            </div>

            <div className="p-4">
              {searchResults.employees.length === 0 && searchResults.files.length === 0 ? (
                searching ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <div className="animate-spin h-8 w-8 border-3 border-primary-500 rounded-full border-t-transparent mb-4"></div>
                    <p className="text-gray-500">جاري البحث...</p>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <div className="bg-gray-100 p-3 rounded-full mb-3">
                      <FiSearch className="w-6 h-6 text-gray-400" />
                    </div>
                    <p className="text-gray-500 mb-1">لا توجد نتائج مطابقة</p>
                    <p className="text-gray-400 text-sm">حاول استخدام كلمات بحث مختلفة</p>
                  </div>
                )
              ) : (
                <div className="space-y-6">
                  {searchResults.employees.length > 0 && (
                    <div>
                      <div className="flex items-center mb-3">
                        <div className="p-2 rounded-full bg-blue-100 text-blue-600 ml-2">
                          <FiUsers className="w-4 h-4" />
                        </div>
                        <h4 className="text-sm font-medium text-gray-700">الموظفين ({searchResults.employees.length})</h4>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {searchResults.employees.map((employee) => (
                          <Link
                            key={employee.name}
                            to={`/employees/${employee.name}`}
                            className="flex items-center p-3 rounded-lg border border-gray-100 hover:bg-gray-50 transition-colors"
                          >
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 ml-3">
                              {employee.name.charAt(0)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-800">{employee.name}</div>
                              <div className="text-xs text-gray-500">عرض ملفات الموظف</div>
                            </div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {searchResults.files.length > 0 && (
                    <div>
                      <div className="flex items-center mb-3">
                        <div className="p-2 rounded-full bg-green-100 text-green-600 ml-2">
                          <FiFile className="w-4 h-4" />
                        </div>
                        <h4 className="text-sm font-medium text-gray-700">الملفات ({searchResults.files.length})</h4>
                      </div>
                      <div className="bg-gray-50 rounded-lg border border-gray-100">
                        {searchResults.files.map((file, index) => (
                          <Link
                            key={`${file.employee}-${file.name}`}
                            to={`/employees/${file.employee}?file=${encodeURIComponent(file.name)}`}
                            className={`flex items-center p-3 hover:bg-gray-100 transition-colors ${
                              index !== searchResults.files.length - 1 ? 'border-b border-gray-100' : ''
                            }`}
                          >
                            <div className="p-2 rounded bg-white border border-gray-200 ml-3">
                              <FiFile className="w-4 h-4 text-gray-500" />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-800">{file.name}</div>
                              <div className="text-xs text-gray-500">الموظف: {file.employee}</div>
                            </div>
                            <div className="text-primary-600 text-sm">عرض</div>
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="card">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-500">
              <FiUsers className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <h2 className="text-lg font-semibold text-gray-700">الموظفين</h2>
              {loading ? (
                <p className="text-2xl font-bold text-gray-800">...</p>
              ) : (
                <p className="text-2xl font-bold text-gray-800">{stats.employeeCount}</p>
              )}
            </div>
          </div>
          <div className="mt-4">
            <Link to="/employees" className="text-sm text-primary-600 hover:underline">
              عرض جميع الموظفين
            </Link>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-500">
              <FiFile className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <h2 className="text-lg font-semibold text-gray-700">الملفات</h2>
              {loading ? (
                <p className="text-2xl font-bold text-gray-800">...</p>
              ) : (
                <p className="text-2xl font-bold text-gray-800">{stats.fileCount}</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-700">النشاط الأخير</h2>
          {user.role === 'admin' && (
            <Link to="/logs" className="text-sm text-primary-600 hover:underline">
              عرض الكل
            </Link>
          )}
        </div>

        {loading ? (
          <p className="text-gray-500">جاري التحميل...</p>
        ) : stats.recentActivity.length === 0 ? (
          <p className="text-gray-500">لا يوجد نشاط حديث</p>
        ) : (
          <ul className="space-y-3">
            {stats.recentActivity.map((activity) => (
              <li key={activity.id} className="flex items-start">
                <div className="p-2 rounded-full bg-gray-100 text-gray-500 mt-1">
                  <FiActivity className="w-4 h-4" />
                </div>
                <div className="mr-3">
                  <p className="text-sm text-gray-800">
                    {activity.user_username && (
                      <span className="font-medium">{activity.user_username}: </span>
                    )}
                    <span>{getActionText(activity.action)}</span>
                    {activity.details && <span> - {activity.details}</span>}
                  </p>
                  <p className="text-xs text-gray-500">{formatDate(activity.timestamp)}</p>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default Dashboard;
