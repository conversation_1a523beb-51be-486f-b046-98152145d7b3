import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { FiArrowRight, FiPrinter, FiZoomIn, FiZoomOut, FiRotateCw, FiDownload, FiCheckCircle, FiX } from 'react-icons/fi';
import * as pdfjsLib from 'pdfjs-dist';
import { useAuth } from '../contexts/AuthContext';

// Set the PDF.js worker source
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;

const FileViewer = () => {
  const { employee, filename } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const canvasRef = useRef(null);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [pageNum, setPageNum] = useState(1);
  const [pageCount, setPageCount] = useState(0);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [downloadSuccessMessage, setDownloadSuccessMessage] = useState('');
  const [showDownloadSuccessMessage, setShowDownloadSuccessMessage] = useState(false);

  useEffect(() => {
    // إضافة تأخير قصير قبل تحميل الملف لضمان تهيئة المكونات بشكل صحيح
    const timer = setTimeout(() => {
      console.log('Loading PDF for:', employee, filename);
      loadPdf();
    }, 300); // زيادة التأخير لضمان تحميل المكونات بشكل صحيح

    return () => clearTimeout(timer);
  }, [employee, filename]);

  // إضافة تأثير إضافي لإعادة تحميل الملف عند تحديث الصفحة
  useEffect(() => {
    // إضافة مستمع لحدث popstate (يحدث عند استخدام أزرار التنقل في المتصفح)
    const handlePopState = () => {
      console.log('Navigation event detected, reloading PDF');
      loadPdf();
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, []);

  useEffect(() => {
    if (pdfDoc) {
      renderPage();
    }
  }, [pdfDoc, pageNum, scale, rotation]);

  // إزالة رسالة نجاح التنزيل بعد 5 ثوانٍ
  useEffect(() => {
    if (showDownloadSuccessMessage) {
      const timer = setTimeout(() => {
        setShowDownloadSuccessMessage(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [showDownloadSuccessMessage]);

  const loadPdf = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`Attempting to load file: /api/files/employee/${employee}/${filename}`);

      // الحصول على رمز المصادقة من التخزين المحلي أو sessionStorage
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        console.error('No authentication token found');
        setError('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
        setLoading(false);
        return;
      }

      // تعيين رأس المصادقة لـ axios
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      // Get the PDF file with authentication
      const response = await axios.get(`/api/files/employee/${employee}/${filename}`, {
        responseType: 'arraybuffer',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        }
      });

      console.log('File fetched successfully, content length:', response.data.byteLength);

      // التحقق من أن البيانات المستلمة صالحة
      if (!response.data || response.data.byteLength === 0) {
        console.error('Received empty file data');
        setError('تم استلام بيانات ملف فارغة');
        setLoading(false);
        return;
      }

      // Load the PDF with error handling
      try {
        const loadingTask = pdfjsLib.getDocument({
          data: response.data,
          cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
          cMapPacked: true,
          standardFontDataUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/standard_fonts/'
        });

        console.log('PDF loading task created');

        const pdf = await loadingTask.promise;
        console.log('PDF loaded successfully, pages:', pdf.numPages);

        setPdfDoc(pdf);
        setPageCount(pdf.numPages);
        setPageNum(1);
      } catch (pdfErr) {
        console.error('Error parsing PDF:', pdfErr);
        setError('حدث خطأ أثناء معالجة ملف PDF. قد يكون الملف تالفًا أو بتنسيق غير صحيح.');
      }
    } catch (err) {
      console.error('Error loading file:', err);
      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response headers:', err.response.headers);

        if (err.response.status === 401) {
          setError('انتهت صلاحية الجلسة. الرجاء تسجيل الدخول مرة أخرى.');
        } else if (err.response.status === 404) {
          setError('لم يتم العثور على الملف. قد يكون تم حذفه أو نقله.');
        } else {
          setError(`حدث خطأ أثناء تحميل الملف: ${err.response.status}`);
        }
      } else {
        setError('حدث خطأ أثناء تحميل الملف. تحقق من اتصالك بالإنترنت وحاول مرة أخرى.');
      }
    } finally {
      setLoading(false);
    }
  };

  const renderPage = async () => {
    if (!pdfDoc) return;

    try {
      // Get the page
      const page = await pdfDoc.getPage(pageNum);

      // Set viewport with scale and rotation
      const viewport = page.getViewport({ scale, rotation: rotation * 90 });

      // Prepare canvas
      const canvas = canvasRef.current;
      if (!canvas) return;

      const context = canvas.getContext('2d', { alpha: false });
      canvas.height = viewport.height;
      canvas.width = viewport.width;

      // Clear canvas
      context.fillStyle = 'rgb(255, 255, 255)';
      context.fillRect(0, 0, canvas.width, canvas.height);

      // Render PDF page into canvas context
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
        enableWebGL: true,
        renderInteractiveForms: true
      };

      try {
        await page.render(renderContext).promise;
      } catch (renderError) {
        console.error('Error during render:', renderError);

        // Try again with simpler options
        const simpleRenderContext = {
          canvasContext: context,
          viewport: viewport
        };

        await page.render(simpleRenderContext).promise;
      }
    } catch (err) {
      console.error('Error rendering page:', err);
      setError('حدث خطأ أثناء عرض الصفحة');
    }
  };

  const prevPage = () => {
    if (pageNum <= 1) return;
    setPageNum(pageNum - 1);
  };

  const nextPage = () => {
    if (pageNum >= pageCount) return;
    setPageNum(pageNum + 1);
  };

  const zoomIn = () => {
    setScale(scale + 0.25);
  };

  const zoomOut = () => {
    if (scale <= 0.5) return;
    setScale(scale - 0.25);
  };

  const rotate = () => {
    setRotation((rotation + 1) % 4);
  };

  const handlePrint = () => {
    // الحصول على رمز المصادقة من التخزين المحلي أو sessionStorage
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!token) {
      alert('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
      return;
    }

    // Create a blob URL with the file content
    const printWindow = window.open('', '_blank');

    if (printWindow) {
      printWindow.document.write('<html><body><h1>جاري تحميل المستند...</h1></body></html>');

      // Fetch the file with authorization header
      fetch(`/api/files/employee/${employee}/${filename}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('فشل في تحميل الملف');
        }
        return response.blob();
      })
      .then(blob => {
        // Create object URL
        const objectUrl = URL.createObjectURL(blob);

        // Redirect to the object URL
        printWindow.location.href = objectUrl;
      })
      .catch(error => {
        printWindow.document.write(`<html><body><h1>خطأ: ${error.message}</h1></body></html>`);
      });
    }
  };

  const handleDownloadFile = () => {
    // الحصول على رمز المصادقة من التخزين المحلي أو sessionStorage
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!token) {
      alert('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
      return;
    }

    // Fetch the file with authorization header
    fetch(`/api/files/employee/${employee}/${filename}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('فشل في تحميل الملف');
      }
      return response.blob();
    })
    .then(blob => {
      // Create object URL
      const objectUrl = URL.createObjectURL(blob);

      // Create a temporary link element
      const a = document.createElement('a');
      a.href = objectUrl;
      a.download = filename; // Set the download attribute to the filename
      document.body.appendChild(a);

      // Trigger the download
      a.click();

      // Clean up
      URL.revokeObjectURL(objectUrl);
      document.body.removeChild(a);

      // Show success message
      setDownloadSuccessMessage(`تم تنزيل الملف "${filename}" بنجاح`);
      setShowDownloadSuccessMessage(true);

      // Log activity
      axios.post('/api/logs', {
        action: 'download_file',
        details: `تم تنزيل الملف: ${filename} للموظف: ${employee}`
      }).catch(err => console.error('Error logging activity:', err));
    })
    .catch(error => {
      console.error('Error downloading file:', error);
      alert('حدث خطأ أثناء تنزيل الملف');
    });
  };

  return (
    <div className="container mx-auto px-0 py-0">
            <div className="flex items-center mb-6">
            <button
                  onClick={() => navigate(`/employees/${employee}`)}
                  className="flex items-center text-gray-600 hover:text-gray-900 transform hover:scale-110"
                >
                  <FiArrowRight className="ml-1" />
                  <span>العودةالى قائمة الموظف</span>
                </button>
            </div>
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center mb-2">
                <h1 className="text-3xl font-bold text-gray-800" dir="auto">{filename}</h1>
              </div>
              <p className="text-gray-600 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                ملف للموظف: {employee}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Download Success Message - تم نقلها تحت بطاقة اسم الملف */}
      {showDownloadSuccessMessage && (
        <div className="mb-4 p-3 rounded-lg flex items-center animate-fade-in-out bg-green-50 border border-green-200 text-green-700">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 text-green-500" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <span dir="auto">{downloadSuccessMessage}</span>
        </div>
      )}

      {/* PDF Controls */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-gray-800">أدوات التحكم</h2>
          </div>
        </div>
        <div className="p-4">
          <div className="flex flex-wrap items-center justify-between">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                onClick={prevPage}
                disabled={pageNum <= 1 || loading}
                className="btn btn-secondary flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                الصفحة السابقة
              </button>

              <span className="text-gray-700 bg-gray-100 px-4 py-2 rounded-md">
                صفحة <span className="font-medium">{pageNum}</span> من <span className="font-medium">{pageCount}</span>
              </span>

              <button
                onClick={nextPage}
                disabled={pageNum >= pageCount || loading}
                className="btn btn-secondary flex items-center"
              >
                الصفحة التالية
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>

            <div className="flex items-center space-x-2 space-x-reverse mt-4 sm:mt-0">
              <button
                onClick={zoomIn}
                disabled={loading}
                className="p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                title="تكبير"
              >
                <FiZoomIn className="w-5 h-5" />
              </button>

              <button
                onClick={zoomOut}
                disabled={loading || scale <= 0.5}
                className="p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                title="تصغير"
              >
                <FiZoomOut className="w-5 h-5" />
              </button>

              <button
                onClick={rotate}
                disabled={loading}
                className="p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                title="تدوير"
              >
                <FiRotateCw className="w-5 h-5" />
              </button>

              <button
                onClick={handleDownloadFile}
                disabled={loading}
                className="p-2 rounded-full bg-green-50 text-green-600 hover:bg-green-100 transition-colors"
                title="تنزيل"
              >
                <FiDownload className="w-5 h-5" />
              </button>

              <button
                onClick={handlePrint}
                disabled={loading}
                className="p-2 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                title="طباعة"
              >
                <FiPrinter className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="card relative overflow-hidden transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h2 className="text-lg font-semibold text-gray-800">عرض الملف</h2>
          </div>
        </div>
        <div className="p-4 bg-gray-800 rounded-b-lg">
          {loading ? (
            <div className="text-center py-12 text-white">
              <div className="animate-spin h-12 w-12 border-4 border-primary-500 rounded-full border-t-transparent mb-4 mx-auto"></div>
              <p className="text-gray-300 font-medium">جاري تحميل الملف...</p>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="bg-red-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-red-400 font-medium text-lg mb-4">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="btn btn-primary inline-flex items-center px-4 py-2"
              >
                <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                إعادة المحاولة
              </button>
            </div>
          ) : (
            <div className="flex justify-center">
              <canvas ref={canvasRef} className="max-w-full shadow-lg" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileViewer;
