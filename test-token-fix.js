// اختبار سريع للتحقق من إصلاح مشكلة الـ token

// هذا الملف يمكن تشغيله في console المتصفح لاختبار الـ token

console.log('=== اختبار إصلاح مشكلة الـ token ===');

// 1. التحقق من وجود الـ token في localStorage أو sessionStorage
const tokenFromLocalStorage = localStorage.getItem('token');
const tokenFromSessionStorage = sessionStorage.getItem('token');
const token = tokenFromLocalStorage || tokenFromSessionStorage;

console.log('1. فحص الـ token:');
console.log('   - localStorage:', tokenFromLocalStorage ? 'موجود' : 'غير موجود');
console.log('   - sessionStorage:', tokenFromSessionStorage ? 'موجود' : 'غير موجود');
console.log('   - الـ token المستخدم:', token ? 'موجود' : 'غير موجود');

if (token) {
  // 2. فحص صحة الـ token
  try {
    const tokenParts = token.split('.');
    if (tokenParts.length === 3) {
      const payload = JSON.parse(atob(tokenParts[1]));
      console.log('2. محتوى الـ token:');
      console.log('   - User ID:', payload.id);
      console.log('   - Username:', payload.username);
      console.log('   - Role:', payload.role);
      console.log('   - Expires:', new Date(payload.exp * 1000));
      console.log('   - منتهي الصلاحية؟', Date.now() > payload.exp * 1000 ? 'نعم' : 'لا');
    } else {
      console.log('2. الـ token غير صحيح (تنسيق خاطئ)');
    }
  } catch (e) {
    console.log('2. خطأ في قراءة الـ token:', e.message);
  }

  // 3. اختبار API call
  console.log('3. اختبار API call...');
  fetch('/api/auth/verify', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  })
  .then(response => {
    console.log('   - Response status:', response.status);
    if (response.ok) {
      return response.json();
    } else {
      throw new Error(`HTTP ${response.status}`);
    }
  })
  .then(data => {
    console.log('   - API Response:', data);
    console.log('   - التوثيق ناجح ✅');
  })
  .catch(error => {
    console.log('   - API Error:', error.message);
    console.log('   - التوثيق فاشل ❌');
  });

} else {
  console.log('2. لا يوجد token - يرجى تسجيل الدخول');
}

// 4. فحص axios default headers
console.log('4. فحص axios headers:');
if (typeof axios !== 'undefined') {
  console.log('   - axios Authorization header:', axios.defaults.headers.common['Authorization'] || 'غير موجود');
} else {
  console.log('   - axios غير متوفر');
}

console.log('=== انتهاء الاختبار ===');

// دالة مساعدة لاختبار رفع الملفات
function testBulkUpload() {
  console.log('=== اختبار رفع الملفات ===');
  
  const token = localStorage.getItem('token') || sessionStorage.getItem('token');
  
  if (!token) {
    console.log('❌ لا يوجد token - يرجى تسجيل الدخول أولاً');
    return;
  }

  // إنشاء FormData تجريبي
  const formData = new FormData();
  
  // إضافة ملف تجريبي (نص فقط للاختبار)
  const testFile = new Blob(['هذا ملف اختبار'], { type: 'text/plain' });
  formData.append('files', testFile, 'test-file.txt');
  
  // إضافة موظف تجريبي
  formData.append('employees[0]', 'موظف تجريبي');
  formData.append('employeesJson', JSON.stringify(['موظف تجريبي']));

  console.log('إرسال طلب اختبار...');
  
  fetch('/api/direct-upload-multiple', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  })
  .then(response => {
    console.log('Response status:', response.status);
    return response.json();
  })
  .then(data => {
    console.log('Response data:', data);
    if (data.message && !data.message.includes('Invalid token')) {
      console.log('✅ اختبار رفع الملفات ناجح');
    } else {
      console.log('❌ اختبار رفع الملفات فاشل');
    }
  })
  .catch(error => {
    console.log('❌ خطأ في اختبار رفع الملفات:', error);
  });
}

// تصدير الدالة للاستخدام
window.testBulkUpload = testBulkUpload;

console.log('💡 لاختبار رفع الملفات، اكتب: testBulkUpload()');
