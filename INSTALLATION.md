# دليل التثبيت

هذا الدليل يشرح كيفية تثبيت وإعداد نظام أرشفة ملفات الموظفين على أنظمة التشغيل المختلفة.

## متطلبات النظام

- **Node.js**: الإصدار 14 أو أحدث
- **npm**: مدير حزم Node.js (يأتي مع تثبيت Node.js)
- **مساحة تخزين**: 100 ميجابايت على الأقل للتطبيق (بدون احتساب ملفات الموظفين)

## تثبيت Node.js

### Windows

1. قم بتنزيل أحدث إصدار من Node.js من [الموقع الرسمي](https://nodejs.org/)
2. قم بتشغيل ملف التثبيت واتبع التعليمات
3. تحقق من التثبيت بفتح موجه الأوامر (Command Prompt) وكتابة:
   ```
   node --version
   npm --version
   ```

### macOS

1. قم بتنزيل أحدث إصدار من Node.js من [الموقع الرسمي](https://nodejs.org/)
2. قم بتشغيل ملف التثبيت واتبع التعليمات
3. أو استخدم Homebrew:
   ```
   brew install node
   ```
4. تحقق من التثبيت بفتح Terminal وكتابة:
   ```
   node --version
   npm --version
   ```

### Linux (Ubuntu/Debian)

1. قم بتثبيت Node.js باستخدام apt:
   ```
   sudo apt update
   sudo apt install nodejs npm
   ```
2. أو استخدم NodeSource:
   ```
   curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
   sudo apt-get install -y nodejs
   ```
3. تحقق من التثبيت:
   ```
   node --version
   npm --version
   ```

## تثبيت التطبيق

### باستخدام سكربت التشغيل التلقائي

#### Windows

1. قم بفك ضغط ملفات المشروع
2. انقر نقرًا مزدوجًا على ملف `run.bat`
3. سيقوم السكربت بتثبيت الاعتماديات وتشغيل التطبيق تلقائيًا

#### macOS/Linux

1. قم بفك ضغط ملفات المشروع
2. افتح Terminal في مجلد المشروع
3. قم بتنفيذ الأمر التالي لجعل سكربت التشغيل قابل للتنفيذ:
   ```
   chmod +x run.sh
   ```
4. قم بتشغيل السكربت:
   ```
   ./run.sh
   ```

### التثبيت اليدوي

1. قم بفك ضغط ملفات المشروع
2. افتح موجه الأوامر أو Terminal في مجلد المشروع
3. قم بتثبيت الاعتماديات:
   ```
   npm run install-all
   ```
4. (اختياري) قم بإنشاء بيانات تجريبية:
   ```
   npm run init-data
   ```
5. قم بتشغيل التطبيق:
   ```
   npm run dev
   ```

## الوصول إلى التطبيق

بعد تشغيل التطبيق، يمكنك الوصول إليه من خلال:

- **الواجهة**: http://localhost:5173
- **الخادم**: http://localhost:5000

استخدم بيانات الدخول الافتراضية:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## استكشاف الأخطاء وإصلاحها

### مشكلة: فشل تثبيت الاعتماديات

- تأكد من اتصالك بالإنترنت
- تأكد من تثبيت Node.js بشكل صحيح
- حاول حذف مجلد `node_modules` وملف `package-lock.json` ثم أعد تثبيت الاعتماديات

### مشكلة: الخادم لا يعمل

- تأكد من أن المنفذ 5000 غير مستخدم من قبل تطبيق آخر
- تحقق من وجود أخطاء في سجل الخادم

### مشكلة: الواجهة لا تعمل

- تأكد من أن المنفذ 5173 غير مستخدم من قبل تطبيق آخر
- تحقق من وجود أخطاء في سجل الواجهة

### مشكلة: لا يمكن الوصول إلى الخادم من الواجهة

- تأكد من أن الخادم يعمل على المنفذ 5000
- تحقق من إعدادات الوكيل (proxy) في ملف `vite.config.js`
