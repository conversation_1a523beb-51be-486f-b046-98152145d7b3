import { useState } from 'react';
import { Outlet, useNavigate, Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Footer from '../components/Footer';
import {
  FiMenu,
  FiX,
  FiHome,
  FiUsers,
  FiFolder,
  FiActivity,
  FiDatabase,
  FiUser,
  FiLogOut
} from 'react-icons/fi';
import employeeFilesLogo from '../assets/employee-files-logo.png';

const MainLayout = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const menuItems = [
    {
      label: 'الرئيسية',
      icon: <FiHome className="ml-2" />,
      path: '/dashboard',
      roles: ['admin', 'editor', 'viewer']
    },
    {
      label: 'الموظفين',
      icon: <FiUsers className="ml-2" />,
      path: '/employees',
      roles: ['admin', 'editor', 'viewer']
    },
    {
      label: 'المستخدمين',
      icon: <FiUser className="ml-2" />,
      path: '/users',
      roles: ['admin']
    },
    {
      label: 'سجل النشاط',
      icon: <FiActivity className="ml-2" />,
      path: '/logs',
      roles: ['admin']
    },
    {
      label: 'النسخ الاحتياطي',
      icon: <FiDatabase className="ml-2" />,
      path: '/backup',
      roles: ['admin']
    },
    {
      label: 'الملف الشخصي',
      icon: <FiFolder className="ml-2" />,
      path: '/profile',
      roles: ['admin', 'editor', 'viewer']
    },
  ];

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar for mobile */}
      <div
        className={`fixed inset-0 z-20 transition-opacity bg-black bg-opacity-50 lg:hidden ${
          sidebarOpen ? 'opacity-100 ease-out duration-300' : 'opacity-0 ease-in duration-200 pointer-events-none'
        }`}
        onClick={toggleSidebar}
      />

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 right-0 z-30 w-72 transition duration-300 transform bg-white shadow-lg lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? 'translate-x-0 ease-out' : 'translate-x-full ease-in'
        }`}
      >
        <div className="bg-gradient-to-r from-primary-600 to-primary-300 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <img src={employeeFilesLogo} alt="نظام ارشفة ملفات الموظفين" className="w-16 h-16 ml-2" />
              <h2 className="text-xl font-bold text-white"><center>نظام ارشفة ملفات الموظفين</center></h2>
            </div>
            <button
              onClick={toggleSidebar}
              className="p-1 text-white rounded-md hover:bg-primary-500 focus:outline-none focus:ring lg:hidden"
            >
              <FiX className="w-6 h-6" />
            </button>
          </div>
        </div>

        <nav>
          <div className="px-6 py-5 bg-gradient-to-r from-primary-50 to-white border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center text-white font-bold shadow-md">
                {user?.username.charAt(0).toUpperCase()}
              </div>
              <div className="mr-3">
                <p className="text-base font-medium text-gray-800 px-2">{user?.username}</p>
                <div className="flex items-center mt-1">
                  <span className={`px-3 py-1 text-xs font-medium rounded-full flex items-center ${
                    user?.role === 'admin'
                      ? 'bg-purple-100 text-purple-800'
                      : user?.role === 'editor'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    <span className={`inline-block w-2 h-2 rounded-full ml-1 ${
                      user?.role === 'admin' ? 'bg-purple-600' : user?.role === 'editor' ? 'bg-blue-600' : 'bg-yellow-600'
                    }`}></span>
                    {user?.role === 'admin' ? 'مدير' : user?.role === 'editor' ? 'محرر' : 'مشاهد'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="py-4">
            <ul>
              {menuItems.map((item, index) => (
                item.roles.includes(user?.role) && (
                  <li key={index} className="px-3 py-1">
                    <button
                      onClick={() => {
                        navigate(item.path);
                        setSidebarOpen(false);
                      }}
                      className={`flex items-center w-full px-4 py-3 rounded-lg transition-all ${
                        location.pathname === item.path
                          ? 'bg-primary-100 text-primary-700 font-medium shadow-sm'
                          : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                      }`}
                    >
                      <div className={`p-2 rounded-md mr-2 ml-3 ${
                        location.pathname === item.path
                          ? 'bg-primary-200 text-primary-700'
                          : 'bg-gray-100 text-gray-500'
                      }`}>
                        {item.icon}
                      </div>
                      <span>{item.label}</span>
                    </button>
                  </li>
                )
              ))}
            </ul>

            <ul className="mt-6">
              <li className="px-3 py-1">
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full px-4 py-3 rounded-lg text-red-600 hover:bg-red-50 transition-all"
                >
                  <div className="p-2 rounded-md bg-red-100 text-red-500 mr-2 ml-3">
                    <FiLogOut />
                  </div>
                  <span>تسجيل الخروج</span>
                </button>
              </li>
            </ul>
          </div>
        </nav>
      </div>

      {/* Main content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        <header className="flex items-center justify-between px-6 py-3 bg-white border-b shadow-sm">
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="p-2 text-gray-500 rounded-md hover:bg-gray-100 hover:text-gray-700 focus:outline-none focus:ring lg:hidden"
            >
              <FiMenu className="w-6 h-6" />
            </button>

            <div className="hidden md:flex items-center mr-4">
              <div className="flex space-x-1 space-x-reverse">
                <Link
                  to="/dashboard"
                  className="px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-100 text-gray-700"
                >
                  الرئيسية
                </Link>
                <Link
                  to="/employees"
                  className="px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-100 text-gray-700"
                >
                  الموظفين
                </Link>
                {user?.role === 'admin' && (
                  <Link
                    to="/users"
                  className="px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-100 text-gray-700"
                  >
                    المستخدمين
                  </Link>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center">
            <div className="relative">
              <div className="flex items-center space-x-3 space-x-reverse bg-gray-50 px-3 py-1.5 rounded-full">
                <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-700 rounded-full flex items-center justify-center text-white font-bold shadow-sm">
                  {user?.username.charAt(0).toUpperCase()}
                </div>
                <span className="text-sm font-medium text-gray-700">مرحباً، {user?.username}</span>
              </div>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
          <Outlet />
        </main>

        <Footer />
      </div>
    </div>
  );
};

export default MainLayout;
