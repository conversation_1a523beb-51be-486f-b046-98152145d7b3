const { db } = require('../db/database');

// Log user activity
const logActivity = (userId, action, details) => {
  db.run(
    'INSERT INTO activity_logs (user_id, action, details) VALUES (?, ?, ?)',
    [userId, action, details],
    (err) => {
      if (err) {
        console.error('Error logging activity:', err);
      }
    }
  );
};

// Middleware to log API requests
const logRequest = (req, res, next) => {
  if (req.user) {
    const { method, originalUrl } = req;
    logActivity(req.user.id, 'api_request', `${method} ${originalUrl}`);
  }
  next();
};

module.exports = {
  logActivity,
  logRequest
};
