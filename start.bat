@echo off
title Employee Archive System
color 0A

REM Skip menu and go directly to start system
goto START_SYSTEM

:CHECK_NODE
echo Checking Node.js installation...
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Node.js is not installed. Please install Node.js and try again.
    echo You can download Node.js from https://nodejs.org/
    pause
    goto MENU
)
echo Node.js found:
node --version
goto :EOF

:CREATE_DIRS
echo Creating necessary directories...
mkdir backend\data 2>nul
mkdir "backend\data\employee_database" 2>nul
mkdir backend\data\backups 2>nul
mkdir backend\temp 2>nul

REM Check if old directory exists and migrate files if needed
if exist "backend\data\قاعدة_البيانات_المحلية" (
    echo Old data directory found. Migrating files...
    xcopy /E /I /Y "backend\data\قاعدة_البيانات_المحلية\*" "backend\data\employee_database\"
    echo Migration complete. You can delete the old directory manually if needed.
)
goto :EOF

:INSTALL_BACKEND
echo Installing backend dependencies...
cd backend
call npm install
if %ERRORLEVEL% neq 0 (
    echo Failed to install backend dependencies.
    cd ..
    pause
    goto MENU
)
cd ..
goto :EOF

:INSTALL_FRONTEND
echo Installing frontend dependencies...
cd frontend
call npm install
if %ERRORLEVEL% neq 0 (
    echo Failed to install frontend dependencies.
    cd ..
    pause
    goto MENU
)
cd ..
goto :EOF

:SAMPLE_DATA
REM Sample data creation has been disabled as existing database is being used
goto :EOF

:START_SYSTEM
cls
echo ======================================================
echo          Starting Employee Archive System
echo ======================================================
echo.

call :CHECK_NODE
call :CREATE_DIRS

REM Check if dependencies have been installed before - optimized for speed
if not exist "setup_complete.flag" (
    echo First time setup - installing dependencies...

    REM Install backend dependencies
    call :INSTALL_BACKEND

    REM Install frontend dependencies
    call :INSTALL_FRONTEND

    REM Create flag file to indicate setup is complete
    echo Setup completed on %date% at %time% > setup_complete.flag
    echo Dependencies installed successfully.
) else (
    REM Skip dependency check for faster startup
)

call :SAMPLE_DATA

echo.
echo Starting the system...
echo Default login: admin / admin123

REM Start both servers in a single window
start /min "Employee Archive System" run-both-servers.bat

REM Reduce waiting time for faster startup
echo Starting servers...

REM Open the application in the default browser
echo Opening application in browser...
start http://localhost:5173/

echo System started successfully!
timeout /t 1 /nobreak > nul
exit

:START_BACKEND
cls
echo ======================================================
echo          Starting Backend Server Only
echo ======================================================
echo.

call :CHECK_NODE
call :CREATE_DIRS
call :INSTALL_BACKEND
call :SAMPLE_DATA

echo.
echo Starting backend server on port 5000...
echo.
echo Press any key to start the backend server...
pause > nul

cd backend
start "Backend Server - Employee Archive System" cmd /k "npm run dev"
cd ..

echo.
echo Backend server started successfully!
echo.
echo Press any key to return to the menu...
pause > nul
goto MENU

:START_FRONTEND
cls
echo ======================================================
echo          Starting Frontend Only
echo ======================================================
echo.
echo Note: The backend server should be running for the frontend to work properly.
echo.

call :CHECK_NODE
call :INSTALL_FRONTEND

echo.
echo Starting frontend on port 5173...
echo.
echo Press any key to start the frontend...
pause > nul

cd frontend
start "Frontend Server - Employee Archive System" cmd /k "npm run dev"
cd ..

echo.
echo Frontend started successfully!
echo.
echo Press any key to return to the menu...
pause > nul
goto MENU

:EXIT
echo.
echo Thank you for using Employee Archive System.
echo.
timeout /t 2 > nul
exit /b 0
