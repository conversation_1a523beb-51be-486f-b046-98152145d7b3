import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  FiArrowRight,
  FiFile,
  FiUpload,
  FiSearch,
  FiEye,
  FiTrash2,
  FiPrinter,
  FiUser,
  FiAlertTriangle,
  FiCheckCircle,
  FiX,
  FiDownload,
  FiChevronRight,
  FiChevronLeft
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const EmployeeDetails = () => {
  const { name } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [employee, setEmployee] = useState(null);
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showAlertModal, setShowAlertModal] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileExists, setFileExists] = useState(false);
  const [existingFiles, setExistingFiles] = useState([]);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [deleteSuccessMessage, setDeleteSuccessMessage] = useState('');
  const [downloadSuccessMessage, setDownloadSuccessMessage] = useState('');
  const [showDeleteSuccessMessage, setShowDeleteSuccessMessage] = useState(false);
  const [showDownloadSuccessMessage, setShowDownloadSuccessMessage] = useState(false);
  const [uploadError, setUploadError] = useState(null);

  // متغيرات حالة للتصفح
  const [currentPage, setCurrentPage] = useState(1);
  const [filesPerPage] = useState(10); // عدد الملفات في كل صفحة

  useEffect(() => {
    fetchEmployeeDetails();

    // Check if there's a file parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const fileParam = urlParams.get('file');
    if (fileParam) {
      setSearchQuery(fileParam);
    }
  }, [name]);

  const fetchEmployeeDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log(`Fetching details for employee: ${name}`);

      // Make sure we have the auth token in headers
      const token = localStorage.getItem('token');
      if (token) {
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      }

      // Create a mock employee object with the name from URL
      // This ensures we always have an employee object even if the API call fails
      const mockEmployee = {
        name: name,
        path: name,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      try {
        // Fetch employee details
        const employeeResponse = await axios.get(`/api/employees/${name}`);
        console.log('Employee details response:', employeeResponse.data);
        setEmployee(employeeResponse.data);
      } catch (empErr) {
        console.error('Error fetching employee details:', empErr);
        console.error('Error details:', empErr.response ? empErr.response.data : 'No response data');
        // Use the mock employee instead of throwing an error
        console.log('Using mock employee data:', mockEmployee);
        setEmployee(mockEmployee);
      }

      try {
        // Fetch employee files
        console.log(`Fetching files for employee: ${name}`);
        const filesResponse = await axios.get(`/api/files/employee/${name}`);
        console.log('Employee files response:', filesResponse.data);
        setFiles(filesResponse.data);
        setCurrentPage(1); // إعادة تعيين الصفحة الحالية عند تحديث قائمة الملفات
      } catch (filesErr) {
        console.error('Error fetching employee files:', filesErr);
        console.error('Error details:', filesErr.response ? filesErr.response.data : 'No response data');
        // Continue even if files fetch fails - we'll just show an empty files list
        setFiles([]);
      }
    } catch (err) {
      console.error('Error in fetchEmployeeDetails:', err);
      setError(err.message || 'حدث خطأ أثناء جلب بيانات الموظف');
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // التحقق من عدد الملفات
      if (files.length > 20) {
        setUploadError('لا يمكن رفع أكثر من 20 ملف في وقت واحد');
        return;
      }

      // حساب الحجم الإجمالي للملفات
      const totalSize = files.reduce((total, file) => total + file.size, 0);
      const totalSizeMB = totalSize / (1024 * 1024);

      // التحقق من الحجم الإجمالي
      if (totalSizeMB > 100) {
        setUploadError(`لا يمكن رفع ملفات يتجاوز حجمها الإجمالي 100 ميجابايت. الحجم الحالي: ${totalSizeMB.toFixed(2)} ميجابايت`);
        return;
      }

      setSelectedFiles(files);
      setUploadError(null); // إزالة أي رسائل خطأ سابقة
      console.log('Files selected via input:', files.length, 'files:', files.map(f => f.name), `Total size: ${totalSizeMB.toFixed(2)} MB`);
    }
  };

  // معالجات أحداث سحب وإفلات الملفات
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files);

      // التحقق من عدد الملفات (الملفات الحالية + الملفات الجديدة)
      const totalFiles = selectedFiles.length + newFiles.length;
      if (totalFiles > 20) {
        setUploadError(`لا يمكن رفع أكثر من 20 ملف في وقت واحد. لديك حاليًا ${selectedFiles.length} ملفات وتحاول إضافة ${newFiles.length} ملفات جديدة.`);
        return;
      }

      // حساب الحجم الإجمالي للملفات (الملفات الحالية + الملفات الجديدة)
      const currentSize = selectedFiles.reduce((total, file) => total + file.size, 0);
      const newSize = newFiles.reduce((total, file) => total + file.size, 0);
      const totalSize = currentSize + newSize;
      const totalSizeMB = totalSize / (1024 * 1024);

      // التحقق من الحجم الإجمالي
      if (totalSizeMB > 100) {
        setUploadError(`لا يمكن رفع ملفات يتجاوز حجمها الإجمالي 100 ميجابايت. الحجم الحالي: ${totalSizeMB.toFixed(2)} ميجابايت`);
        return;
      }

      setSelectedFiles(prev => [...prev, ...newFiles]);
      setUploadError(null); // إزالة أي رسائل خطأ سابقة
      e.dataTransfer.clearData();
      console.log('Files dropped:', newFiles.length, 'files. Total files:', totalFiles, `Total size: ${totalSizeMB.toFixed(2)} MB`);
    }
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const handleUpload = async (e) => {
    e.preventDefault();

    if (selectedFiles.length === 0) {
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);
      setFileExists(false);
      setExistingFiles([]);
      setUploadError(null);

      const formData = new FormData();

      // Append each file to the form data
      selectedFiles.forEach(file => {
        formData.append('files', file);
      });

      const response = await axios.post(`/api/files/employee/${name}/multiple`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
        },
        validateStatus: function (status) {
          // Accept 409 Conflict status (file exists) as a valid response
          return (status >= 200 && status < 300) || status === 409;
        }
      });

      // Check if files already exist or are duplicates
      if (response.data.existingFilesCount > 0 || response.data.duplicateFilesCount > 0) {
        // Set file exists state and show alert modal
        setFileExists(true);
        setExistingFiles([
          ...(response.data.existingFiles || []),
          ...(response.data.duplicateFiles || []).map(file => ({
            fileName: file.fileName,
            isDuplicate: true,
            existingLocation: file.existingLocation
          }))
        ]);
        setShowAlertModal(true);
        setShowUploadModal(false); // Hide upload modal
        console.log('Files already exist or are duplicates:', {
          existing: response.data.existingFiles,
          duplicates: response.data.duplicateFiles
        });
      } else {
        // Refresh files list
        fetchEmployeeDetails();

        // Reset form and close modal only on success
        setSelectedFiles([]);
        setShowUploadModal(false);

        // Show success message
        const filesCount = selectedFiles.length;
        setSuccessMessage(filesCount > 1
          ? `تم رفع ${filesCount} ملفات بنجاح`
          : 'تم رفع الملف بنجاح');
        setShowSuccessMessage(true);

        // Hide success message after 5 seconds
        setTimeout(() => {
          setShowSuccessMessage(false);
        }, 5000);
      }
    } catch (err) {
      console.error('Error uploading files:', err);
      alert('حدث خطأ أثناء رفع الملفات');
    } finally {
      setUploading(false);
    }
  };

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [fileToDelete, setFileToDelete] = useState(null);
  const [deletingFile, setDeletingFile] = useState(false);

  const handleDeleteFile = async () => {
    if (!fileToDelete) return;

    try {
      setDeletingFile(true);

      await axios.delete(`/api/files/employee/${name}/${fileToDelete.name}`);

      // Refresh files list
      fetchEmployeeDetails();

      // Show success message
      setDeleteSuccessMessage(`تم حذف الملف "${fileToDelete.name}" بنجاح`);
      setShowDeleteSuccessMessage(true);

      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowDeleteSuccessMessage(false);
      }, 5000);

      // Reset state
      setFileToDelete(null);
      setShowDeleteModal(false);
    } catch (err) {
      console.error('Error deleting file:', err);
      alert('حدث خطأ أثناء حذف الملف');
    } finally {
      setDeletingFile(false);
    }
  };

  const handlePrintFile = (filename) => {
    // الحصول على رمز المصادقة من التخزين المحلي أو sessionStorage
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!token) {
      alert('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
      return;
    }

    // Create a blob URL with the file content
    const printWindow = window.open('', '_blank');

    if (printWindow) {
      printWindow.document.write('<html><body><h1>جاري تحميل المستند...</h1></body></html>');

      // Fetch the file with authorization header
      fetch(`/api/files/employee/${name}/${filename}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('فشل في تحميل الملف');
        }
        return response.blob();
      })
      .then(blob => {
        // Create object URL
        const objectUrl = URL.createObjectURL(blob);

        // Redirect to the object URL
        printWindow.location.href = objectUrl;
      })
      .catch(error => {
        printWindow.document.write(`<html><body><h1>خطأ: ${error.message}</h1></body></html>`);
      });
    }
  };

  const handleDownloadFile = (filename) => {
    // الحصول على رمز المصادقة من التخزين المحلي أو sessionStorage
    const token = localStorage.getItem('token') || sessionStorage.getItem('token');

    if (!token) {
      alert('لم يتم العثور على رمز المصادقة. الرجاء تسجيل الدخول مرة أخرى.');
      return;
    }

    // Fetch the file with authorization header
    fetch(`/api/files/employee/${name}/${filename}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    .then(response => {
      if (!response.ok) {
        throw new Error('فشل في تحميل الملف');
      }
      return response.blob();
    })
    .then(blob => {
      // Create object URL
      const objectUrl = URL.createObjectURL(blob);

      // Create a temporary link element
      const a = document.createElement('a');
      a.href = objectUrl;
      a.download = filename; // Set the download attribute to the filename
      document.body.appendChild(a);

      // Trigger the download
      a.click();

      // Clean up
      URL.revokeObjectURL(objectUrl);
      document.body.removeChild(a);

      // Show success message
      setDownloadSuccessMessage(`تم تنزيل الملف "${filename}" بنجاح`);
      setShowDownloadSuccessMessage(true);

      // Hide success message after 5 seconds
      setTimeout(() => {
        setShowDownloadSuccessMessage(false);
      }, 5000);

      // Log activity
      axios.post('/api/logs', {
        action: 'download_file',
        details: `تم تنزيل الملف: ${filename} للموظف: ${name}`
      }).catch(err => console.error('Error logging activity:', err));
    })
    .catch(error => {
      console.error('Error downloading file:', error);
      alert('حدث خطأ أثناء تنزيل الملف');
    });
  };



  const filteredFiles = files.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(filteredFiles.length / filesPerPage);

  // الحصول على الملفات المعروضة في الصفحة الحالية
  const indexOfLastFile = currentPage * filesPerPage;
  const indexOfFirstFile = indexOfLastFile - filesPerPage;
  const currentFiles = filteredFiles.slice(indexOfFirstFile, indexOfLastFile);

  // دالة للانتقال إلى صفحة معينة
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // التمرير إلى أعلى الصفحة عند تغيير الصفحة
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div>
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate('/employees')}
          className="flex items-center text-gray-600 hover:text-gray-900 transform hover:scale-110"
        >
          <FiArrowRight className="ml-1" />
          <span>العودة إلى قائمة الموظفين</span>
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <p className="text-gray-500">جاري تحميل بيانات الموظف...</p>
        </div>
      ) : error ? (
        <div className="text-center py-8">
          <p className="text-red-500">{error}</p>
          <button
            onClick={fetchEmployeeDetails}
            className="mt-2 btn btn-secondary"
          >
            إعادة المحاولة
          </button>
        </div>
      ) : employee ? (
        <div>
           <div className="relative mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
            <div className="relative p-6 rounded-lg overflow-hidden">
              <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

              <div className="flex items-center">
                      <div>
                        <h1 className="text-3xl font-bold text-gray-800 mb-2">{employee.name}</h1>
                        <p className="text-gray-600">تاريخ الإنشاء: {formatDate(employee.createdAt)}</p>
                      </div>

                      {(user.role === 'admin' || user.role === 'editor') && (
                        <div className="flex space-x-3 space-x-reverse absolute top-10 left-6">
                          <button
                            onClick={() => setShowUploadModal(true)}
                            className="btn btn-primary flex items-center shadow-sm hover:shadow-md transition-all"
                            title="رفع ملفات جديدة"
                          >
                            <FiUpload className="ml-1" />
                            رفع ملفات
                          </button>
                        </div>
                      )}
                    </div>

                </div>
          </div>

          {/* Success Messages */}
          {showSuccessMessage && (
            <div className="mb-4 bg-blue-50 border border-blue-200 text-blue-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in">
              <FiCheckCircle className="text-blue-500 text-xl ml-2 flex-shrink-0" />
              <div className="flex-grow">{successMessage}</div>
              <button
                onClick={() => setShowSuccessMessage(false)}
                className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
              >
                <FiX />
              </button>
            </div>
          )}

          {/* Delete Success Message */}
          {showDeleteSuccessMessage && (
            <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in">
              <FiCheckCircle className="text-red-500 text-xl ml-2 flex-shrink-0" />
              <div className="flex-grow">{deleteSuccessMessage}</div>
              <button
                onClick={() => setShowDeleteSuccessMessage(false)}
                className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
              >
                <FiX />
              </button>
            </div>
          )}

          {/* Download Success Message */}
          {showDownloadSuccessMessage && (
            <div className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in">
              <FiCheckCircle className="text-green-500 text-xl ml-2 flex-shrink-0" />
              <div className="flex-grow">{downloadSuccessMessage}</div>
              <button
                onClick={() => setShowDownloadSuccessMessage(false)}
                className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
              >
                <FiX />
              </button>
            </div>
          )}

          {/* Search */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiSearch className="w-5 h-5 text-primary-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">البحث عن ملف</h2>
          </div>
        </div>
        <div className="p-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FiSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
              placeholder=" ابحث عن ملف..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setCurrentPage(1); // إعادة تعيين الصفحة الحالية عند تغيير البحث
              }}
              autoFocus
            />
            {searchQuery && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <button
                  onClick={() => setSearchQuery('')}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  aria-label="مسح البحث"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

          {/* Files List */}
          <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
              <div className="flex items-center">
                <div className="p-2 bg-white rounded-full shadow-sm ml-3">
                  <FiFile className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-lg font-semibold text-gray-800">ملفات الموظف</h2>
              </div>
            </div>

            <div className="p-4 bg-white rounded-b-lg">
              {files.length === 0 ? (
                <div className="text-center py-8">
                  <FiFile className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">لا توجد ملفات</h3>
                  <p className="mt-1 text-gray-500">قم برفع ملف جديد للبدء.</p>
                </div>
              ) : filteredFiles.length === 0 ? (
                <div className="text-center py-8">
                  <FiSearch className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-lg font-medium text-gray-900">لا توجد نتائج</h3>
                  <p className="mt-1 text-gray-500">لا توجد ملفات تطابق بحثك. حاول بكلمات أخرى.</p>
                </div>
              ) : (
                <div className="overflow-hidden rounded-lg shadow-sm border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead>
                      <tr className="bg-gray-50">
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          اسم الملف
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الحجم
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          تاريخ الإنشاء
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {currentFiles.map((file) => (
                        <tr key={file.name} className="hover:bg-gray-50 transition-colors">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="text-blue-600 ml-2">
                                <FiFile className="w-5 h-5" />
                              </div>
                              <div className="text-sm font-medium text-gray-900" dir="auto">{file.name}</div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{formatFileSize(file.size)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-500">{formatDate(file.createdAt)}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex space-x-3 space-x-reverse">
                              <Link
                                to={`/files/${name}/${file.name}`}
                                className="p-1.5 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 transition-colors"
                                title="عرض"
                              >
                                <FiEye className="w-4 h-4" />
                              </Link>

                              <button
                                onClick={() => handleDownloadFile(file.name)}
                                className="p-1.5 rounded-full bg-green-50 text-green-600 hover:bg-green-100 transition-colors"
                                title="تنزيل"
                              >
                                <FiDownload className="w-4 h-4" />
                              </button>

                              <button
                                onClick={() => handlePrintFile(file.name)}
                                className="p-1.5 rounded-full bg-indigo-50 text-indigo-600 hover:bg-indigo-100 transition-colors"
                                title="طباعة"
                              >
                                <FiPrinter className="w-4 h-4" />
                              </button>

                              {(user.role === 'admin' || user.role === 'editor') && (
                                <button
                                  onClick={() => {
                                    setFileToDelete(file);
                                    setShowDeleteModal(true);
                                  }}
                                  className="p-1.5 rounded-full bg-red-50 text-red-600 hover:bg-red-100 transition-colors"
                                  title="حذف"
                                >
                                  <FiTrash2 className="w-4 h-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          عرض {indexOfFirstFile + 1} إلى {Math.min(indexOfLastFile, filteredFiles.length)} من أصل {filteredFiles.length} ملف
                        </div>
                        <div className="flex space-x-1 space-x-reverse">
                          <button
                            onClick={() => paginate(currentPage - 1)}
                            disabled={currentPage === 1}
                            className={`flex items-center px-3 py-1 rounded-md ${
                              currentPage === 1
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                            }`}
                          >
                            <FiChevronRight className="ml-1" />
                            <span>السابق</span>
                          </button>

                          {/* أرقام الصفحات */}
                          {[...Array(totalPages)].map((_, index) => {
                            const pageNumber = index + 1;
                            // عرض أول صفحتين وآخر صفحتين والصفحة الحالية وما حولها
                            if (
                              pageNumber === 1 ||
                              pageNumber === totalPages ||
                              (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                            ) {
                              return (
                                <button
                                  key={pageNumber}
                                  onClick={() => paginate(pageNumber)}
                                  className={`px-3 py-1 rounded-md ${
                                    currentPage === pageNumber
                                      ? 'bg-primary-600 text-white'
                                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                                  }`}
                                >
                                  {pageNumber}
                                </button>
                              );
                            } else if (
                              (pageNumber === 2 && currentPage > 3) ||
                              (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                            ) {
                              // عرض نقاط للصفحات المحذوفة
                              return <span key={pageNumber} className="px-2 py-1">...</span>;
                            }
                            return null;
                          })}

                          <button
                            onClick={() => paginate(currentPage + 1)}
                            disabled={currentPage === totalPages}
                            className={`flex items-center px-3 py-1 rounded-md ${
                              currentPage === totalPages
                                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                                : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                            }`}
                          >
                            <span>التالي</span>
                            <FiChevronLeft className="mr-1" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-red-500">الموظف غير موجود</p>
          <button
            onClick={() => navigate('/employees')}
            className="mt-2 btn btn-secondary"
          >
            العودة إلى قائمة الموظفين
          </button>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-gradient-to-r from-blue-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center ml-3">
                    <FiUpload className="text-blue-600 text-xl" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      رفع ملفات جديدة
                    </h3>
                    <p className="text-sm text-gray-500">
                      يمكنك رفع عدة ملفات دفعة واحدة إلى مجلد الموظف "{name}".
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6">
                <form onSubmit={handleUpload}>
                  {uploadError && (
                    <div className="mb-4 p-4 text-sm text-red-700 bg-red-100 rounded-lg flex items-center border-r-4 border-red-500 shadow-sm">
                      <FiAlertTriangle className="ml-2 flex-shrink-0 text-lg text-red-600" />
                      <span className="font-bold">{uploadError}</span>
                    </div>
                  )}
                  <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div
                      className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"
                      onDragOver={handleDragOver}
                      onDragEnter={handleDragEnter}
                      onDragLeave={handleDragLeave}
                      onDrop={handleDrop}
                    >
                      <input
                        type="file"
                        id="file"
                        onChange={handleFileChange}
                        className="hidden"
                        disabled={uploading}
                        multiple
                        required
                      />
                      <label htmlFor="file" className="cursor-pointer block">
                        <FiUpload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                        <span className="block text-sm font-medium text-gray-700 mb-1">
                          {selectedFiles.length > 0 ? `تم اختيار ${selectedFiles.length} ملفات` : 'اضغط لاختيار الملفات'}
                        </span>
                        <span className="text-xs text-gray-500">أو قم بسحب الملفات وإفلاتها هنا</span>
                      </label>

                      {selectedFiles.length > 0 && (
                        <div className="mt-4 text-right">
                          <p className="text-sm font-medium text-gray-700 mb-2">الملفات المختارة:</p>
                          <ul className="text-xs text-gray-600 max-h-32 overflow-y-auto">
                            {selectedFiles.map((file, index) => (
                              <li key={index} className="mb-2 flex items-center justify-between bg-gray-50 p-2 rounded-md border border-gray-200">
                                <div className="flex items-center">
                                  <FiFile className="ml-1 text-blue-500" />
                                  <span className="truncate max-w-[150px]" dir="auto">{file.name}</span>
                                  <span className="mr-1 text-gray-500">({Math.round(file.size / 1024)} KB)</span>
                                </div>
                                <button
                                  type="button"
                                  onClick={() => removeFile(index)}
                                  className="text-red-500 hover:text-red-700 transition-colors"
                                  title="إزالة الملف"
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>

                    {uploading && (
                      <div className="mt-4">
                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                          <span>جاري الرفع...</span>
                          <span>{uploadProgress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${uploadProgress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={handleUpload}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${uploading || selectedFiles.length === 0
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
                    }
                  `}
                  disabled={uploading || selectedFiles.length === 0}
                >
                  <FiUpload className="ml-2 text-lg" />
                  {uploading ? 'جاري الرفع...' : 'رفع الملفات'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowUploadModal(false);
                    setSelectedFiles([]);
                    setUploadError(null);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  disabled={uploading}
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Alert Modal for File Exists */}
      {showAlertModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                      <FiAlertTriangle className="ml-2 text-yellow-600" />
                      تنبيه: بعض الملفات موجودة مسبقاً أو مكررة
                    </h3>
                    <div className="mt-4">
                      <p className="text-sm text-gray-500 mb-3">
                        تم تخطي الملفات التالية:
                      </p>
                      <div className="max-h-60 overflow-y-auto bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                        {existingFiles.map((file, index) => (
                          <div key={index} className="mb-2 pb-2 border-b border-yellow-200 last:border-0">
                            <div className="flex items-center">
                              <FiFile className="ml-2 text-yellow-600" />
                              <div className="flex-1">
                                <div className="font-medium" dir="auto">{file.fileName}</div>
                                {file.isDuplicate ? (
                                  <div className="text-xs text-red-600 mt-1">
                                    ملف مكرر - موجود في: {file.existingLocation}
                                  </div>
                                ) : (
                                  <div className="text-xs text-yellow-600 mt-1">
                                    موجود في نفس المجلد
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                      <p className="mt-3 text-sm text-gray-600">
                        الرجاء اختيار ملفات أخرى أو تغيير أسماء الملفات قبل الرفع.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => {
                    setShowAlertModal(false);
                    setShowUploadModal(true);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  اختيار ملفات أخرى
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowAlertModal(false);
                    setFileExists(false);
                    setExistingFiles([]);
                  }}
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  فهمت
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}

      {/* Delete File Modal */}
      {showDeleteModal && fileToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      حذف ملف
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      تأكيد حذف الملف <span className="font-semibold text-red-600">"{fileToDelete.name}"</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير هام</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم حذف الملف "{fileToDelete.name}" بشكل نهائي ولا يمكن استرجاعه.
                        </p>
                        <p className="mt-2">
                          هل أنت متأكد من رغبتك في حذف هذا الملف؟
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center">
                    <FiFile className="ml-2 text-gray-500" />
                    <div>
                      <div className="font-medium" dir="auto">{fileToDelete.name}</div>
                      <div className="text-sm text-gray-500">
                        الحجم: {formatFileSize(fileToDelete.size)} | تاريخ الإنشاء: {formatDate(fileToDelete.createdAt)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleDeleteFile}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${deletingFile
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-red-600 hover:bg-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={deletingFile}
                >
                  <FiTrash2 className="ml-2" />
                  {deletingFile ? 'جاري الحذف...' : 'تأكيد الحذف'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setFileToDelete(null);
                    setShowDeleteModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default EmployeeDetails;
