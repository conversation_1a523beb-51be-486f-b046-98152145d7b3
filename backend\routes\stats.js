const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs-extra');
const { db } = require('../db/database');
const { authenticateToken } = require('../middleware/auth');

// نقطة نهاية للحصول على إحصائيات النظام
router.get('/', authenticateToken, (req, res) => {
  try {
    // الحصول على عدد الموظفين من خلال عد المجلدات في مجلد الموظفين
    const dataDir = path.join(__dirname, '../data', 'employee_database');
    let employeeCount = 0;

    if (fs.existsSync(dataDir)) {
      try {
        const employees = fs.readdirSync(dataDir).filter(file =>
          fs.statSync(path.join(dataDir, file)).isDirectory()
        );
        employeeCount = employees.length;
      } catch (err) {
        console.error('Error reading employee directories:', err);
      }
    }

    // الحصول على عدد الملفات
    db.get('SELECT COUNT(*) as count FROM file_metadata', [], (err, fileCountResult) => {
      if (err) {
        console.error('Error counting files:', err);
        return res.status(500).json({ message: 'حدث خطأ أثناء جلب الإحصائيات' });
      }

      // إرجاع الإحصائيات
      res.json({
        employeeCount: employeeCount,
        fileCount: fileCountResult ? fileCountResult.count : 0
      });
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب الإحصائيات' });
  }
});

module.exports = router;
