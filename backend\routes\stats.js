const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs-extra');
const { db } = require('../db/database');
const { authenticateToken } = require('../middleware/auth');

// نقطة نهاية للحصول على إحصائيات النظام
router.get('/', authenticateToken, (req, res) => {
  try {
    // الحصول على عدد الموظفين من خلال عد المجلدات في مجلد الموظفين
    const dataDir = path.join(__dirname, '../data', 'employee_database');
    let employeeCount = 0;
    let fileCount = 0;

    if (fs.existsSync(dataDir)) {
      try {
        const employees = fs.readdirSync(dataDir).filter(file =>
          fs.statSync(path.join(dataDir, file)).isDirectory()
        );
        employeeCount = employees.length;

        // حساب عدد الملفات الفعلية من النظام
        employees.forEach(employee => {
          const employeePath = path.join(dataDir, employee);
          try {
            const files = fs.readdirSync(employeePath).filter(file =>
              fs.statSync(path.join(employeePath, file)).isFile()
            );
            fileCount += files.length;
          } catch (fileErr) {
            console.error(`Error counting files for employee ${employee}:`, fileErr);
          }
        });

        console.log(`📊 Stats calculated: ${employeeCount} employees, ${fileCount} files`);

      } catch (err) {
        console.error('Error reading employee directories:', err);
      }
    }

    // إرجاع الإحصائيات المحسوبة من النظام الفعلي
    res.json({
      employeeCount: employeeCount,
      fileCount: fileCount
    });

  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ message: 'حدث خطأ أثناء جلب الإحصائيات' });
  }
});

module.exports = router;
