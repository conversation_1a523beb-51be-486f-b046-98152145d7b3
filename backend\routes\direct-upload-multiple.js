const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { authenticateToken, isEditor } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');
const { db } = require('../db/database');

const router = express.Router();

// Base directory for employee data
const dataDir = path.join(__dirname, '../data', 'employee_database');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const tempDir = path.join(__dirname, '../temp');
    fs.ensureDirSync(tempDir);
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // Keep original filename as is - m<PERSON> already handles UTF-8 encoding correctly
    // Just log the filename for debugging
    console.log(`Processing file with original name: ${file.originalname}`);
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit per file
    files: 20 // الحد الأقصى لعدد الملفات هو 20 ملف
  }
});

// Multiple files upload endpoint (now at root path)
router.post('/', (req, res, next) => {
  console.log('Received request to /api/direct-upload-multiple - before auth');
  console.log('Headers:', req.headers);
  next();
}, authenticateToken, (req, res, next) => {
  console.log('Authentication passed, checking editor permissions');
  next();
}, isEditor, upload.array('files', 20), async (req, res) => {
  try {
    console.log('Received request to /api/direct-upload-multiple');
    console.log('Request body keys:', Object.keys(req.body));
    console.log('Request files:', req.files ? req.files.length : 'none');

    const files = req.files;
    if (!files || files.length === 0) {
      console.log('No files found in request');
      return res.status(400).json({ message: 'No files uploaded' });
    }

    console.log('Files received:', files.map(f => ({ name: f.originalname, size: f.size })));

    // Get employees from request body
    let employees = [];

    console.log('Full request body:', req.body);

    // Try to parse employees from different sources
    if (req.body.employeesJson) {
      try {
        console.log('Found employeesJson:', req.body.employeesJson);
        employees = JSON.parse(req.body.employeesJson);
        console.log('Parsed employeesJson successfully:', employees);
      } catch (e) {
        console.error('Error parsing employeesJson:', e);
        console.error('Raw employeesJson value:', req.body.employeesJson);
      }
    } else if (req.body.employees) {
      console.log('Found employees in request body');
      if (Array.isArray(req.body.employees)) {
        employees = req.body.employees;
        console.log('employees is an array:', employees);
      } else if (typeof req.body.employees === 'string') {
        console.log('employees is a string:', req.body.employees);
        try {
          employees = JSON.parse(req.body.employees);
          console.log('Parsed employees string as JSON:', employees);
        } catch (e) {
          employees = [req.body.employees];
          console.log('Using employees as a single value:', employees);
        }
      }
    } else {
      // Try to find employees in form data format
      console.log('Looking for employees in form data format');
      employees = [];
      let i = 0;
      while (req.body[`employees[${i}]`] !== undefined) {
        employees.push(req.body[`employees[${i}]`]);
        console.log(`Found employees[${i}]:`, req.body[`employees[${i}]`]);
        i++;
      }
      console.log('Found employees in form data format:', employees);
    }

    console.log('Employees to process:', employees);
    console.log('Files to upload:', files.map(f => f.originalname));

    if (!employees || !Array.isArray(employees) || employees.length === 0) {
      // Clean up the uploaded files
      for (const file of files) {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      }
      return res.status(400).json({ message: 'No employees specified' });
    }

    // Process each file for each employee
    const results = [];
    const errors = [];
    const existingFiles = [];

    // For each employee
    for (const employee of employees) {
      // Ensure employee directory exists
      const employeePath = path.join(dataDir, employee);
      fs.ensureDirSync(employeePath);

      // For each file
      for (const file of files) {
        try {
          // Check if file already exists
          const destPath = path.join(employeePath, file.originalname);
          if (fs.existsSync(destPath)) {
            console.log(`File already exists: ${destPath}`);
            existingFiles.push({
              employee,
              fileName: file.originalname,
              path: `${employee}/${file.originalname}`
            });
            continue; // Skip this file for this employee
          }

          // Copy file to employee directory
          await fs.copy(file.path, destPath);

          // Add to database
          await new Promise((resolve, reject) => {
            db.run(
              'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
              [employee, file.originalname, file.originalname, file.size],
              function(err) {
                if (err) {
                  console.error(`Database error for ${employee}:`, err);
                  reject(err);
                } else {
                  console.log(`Database entry created for ${employee}, file: ${file.originalname}, ID: ${this.lastID}`);
                  results.push({
                    employee,
                    fileId: this.lastID,
                    name: file.originalname,
                    path: `${employee}/${file.originalname}`,
                    size: file.size
                  });
                  resolve();
                }
              }
            );
          });
        } catch (err) {
          console.error(`Error processing file ${file.originalname} for employee ${employee}:`, err);
          errors.push({
            employee,
            file: file.originalname,
            error: err.message
          });
        }
      }
    }

    // Clean up the uploaded files
    for (const file of files) {
      try {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      } catch (err) {
        console.error(`Error deleting temporary file ${file.path}:`, err);
      }
    }

    // Log activity
    logActivity(
      req.user.id,
      'bulk_upload_multiple_files',
      `Uploaded ${files.length} files to ${employees.length} employees`
    );

    // تحديد الرسالة بناءً على وجود ملفات مكررة
    let message = 'Files uploaded successfully';
    const totalOperations = files.length * employees.length;

    if (existingFiles.length > 0) {
      if (existingFiles.length === totalOperations) {
        message = 'All files already exist for the selected employees';
      } else if (results.length === 0) {
        message = 'No files were uploaded. All selected files already exist for the selected employees.';
      } else {
        message = `${results.length} files uploaded successfully. ${existingFiles.length} files already exist for the selected employees.`;
      }
    }

    res.status(200).json({
      message,
      filesCount: files.length,
      employeesCount: employees.length,
      successCount: results.length,
      errorCount: errors.length,
      existingFilesCount: existingFiles.length,
      existingFiles: existingFiles.length > 0 ? existingFiles : undefined,
      results,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (err) {
    console.error('Error in multiple files upload:', err);
    res.status(500).json({
      message: 'Error uploading files',
      error: err.message
    });
  }
});

module.exports = router;
