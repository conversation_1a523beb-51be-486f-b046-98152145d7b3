import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FiActivity,
  FiFilter,
  FiRefreshCw,
  FiTrash2,
  FiSearch,
  FiCheckCircle,
  FiX,
  FiChevronRight,
  FiChevronLeft,
  FiAlertCircle
} from 'react-icons/fi';

const ActivityLogs = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    action: '',
    userId: ''
  });
  const [users, setUsers] = useState([]);
  const [showClearModal, setShowClearModal] = useState(false);
  const [clearing, setClearing] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [logsPerPage] = useState(10); // عدد السجلات في كل صفحة
  const [successMessage, setSuccessMessage] = useState(null);
  const [successMessageType, setSuccessMessageType] = useState('delete'); // 'delete' for clear logs

  useEffect(() => {
    fetchLogs();
    fetchUsers();
  }, []);

  useEffect(() => {
    fetchLogs(true);
  }, [filters]);

  // إزالة رسالة النجاح بعد 5 ثوانٍ
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const fetchLogs = async (isFilterChange = false) => {
    try {
      if (isFilterChange) {
        setCurrentPage(1);
      }

      setLoading(true);
      setError(null);

      const params = {};

      if (filters.action) {
        params.action = filters.action;
      }

      if (filters.userId) {
        params.userId = filters.userId;
      }

      const response = await axios.get('/api/logs', { params });
      setLogs(response.data);
    } catch (err) {
      console.error('Error fetching logs:', err);
      setError('حدث خطأ أثناء جلب سجل النشاط');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await axios.get('/api/users');
      setUsers(response.data);
    } catch (err) {
      console.error('Error fetching users:', err);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value
    });
  };

  const clearFilters = () => {
    setFilters({
      action: '',
      userId: ''
    });
  };

  const handleClearLogs = async () => {
    try {
      setClearing(true);

      await axios.delete('/api/logs/clear');

      // Refresh logs
      setLogs([]);
      setShowClearModal(false);

      // إظهار رسالة النجاح
      setSuccessMessage('تم مسح سجل النشاط بنجاح');
      setSuccessMessageType('delete');
    } catch (err) {
      console.error('Error clearing logs:', err);
      alert('حدث خطأ أثناء مسح سجل النشاط');
    } finally {
      setClearing(false);
    }
  };



  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getActionText = (action) => {
    switch (action) {
      case 'login':
        return 'تسجيل دخول';
      case 'view_employee':
        return 'عرض موظف';
      case 'view_file':
        return 'عرض ملف';
      case 'upload_file':
        return 'رفع ملف';
      case 'delete_file':
        return 'حذف ملف';
      case 'create_employee':
        return 'إنشاء موظف';
      case 'delete_employee':
        return 'حذف موظف';
      case 'create_user':
        return 'إنشاء مستخدم';
      case 'update_user':
        return 'تحديث مستخدم';
      case 'delete_user':
        return 'حذف مستخدم';
      case 'create_backup':
        return 'إنشاء نسخة احتياطية';
      case 'restore_backup':
        return 'استعادة نسخة احتياطية';
      case 'bulk_upload_file':
        return 'رفع ملف لعدة موظفين';
      case 'search_files':
        return 'بحث عن ملفات';
      case 'list_employees':
        return 'عرض قائمة الموظفين';
      case 'list_files':
        return 'عرض قائمة الملفات';
      case 'download_backup':
        return 'تنزيل نسخة احتياطية';
      case 'delete_backup':
        return 'حذف نسخة احتياطية';
      case 'download_file':
        return 'تنزيل ملف';
      default:
        return action;
    }
  };

  const getActionOptions = () => {
    const actions = [
      'login',
      'view_employee',
      'view_file',
      'upload_file',
      'delete_file',
      'download_file',
      'create_employee',
      'delete_employee',
      'create_user',
      'update_user',
      'delete_user',
      'create_backup',
      'restore_backup',
      'bulk_upload_file',
      'search_files',
      'list_employees',
      'list_files',
      'download_backup',
      'delete_backup'
    ];

    return actions.map(action => (
      <option key={action} value={action}>
        {getActionText(action)}
      </option>
    ));
  };

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(logs.length / logsPerPage);

  // الحصول على السجلات المعروضة في الصفحة الحالية
  const indexOfLastLog = currentPage * logsPerPage;
  const indexOfFirstLog = indexOfLastLog - logsPerPage;
  const currentLogs = logs.slice(indexOfFirstLog, indexOfLastLog);

  // دالة للانتقال إلى صفحة معينة
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // التمرير إلى أعلى الصفحة عند تغيير الصفحة
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  return (
    <div>
      {/* Header Section */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">سجل النشاط</h1>
              <p className="text-gray-600">متابعة جميع الأنشطة والإجراءات في النظام</p>
            </div>

            <button
              onClick={() => setShowClearModal(true)}
              className="btn btn-danger flex items-center shadow-sm hover:shadow-md transition-all"
            >
              <FiTrash2 className="ml-1" />
              مسح السجل
            </button>
          </div>
        </div>
      </div>

      {/* رسالة النجاح */}
      {successMessage && (
        <div className={`mb-4 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in
          ${successMessageType === 'delete'
            ? 'bg-red-50 border border-red-200 text-red-800'
            : 'bg-green-50 border border-green-200 text-green-800'
          }`}
        >
          <FiCheckCircle className={`text-xl ml-2 flex-shrink-0
            ${successMessageType === 'delete'
              ? 'text-red-500'
              : 'text-green-500'
            }`}
          />
          <div className="flex-grow">{successMessage}</div>
          <button
            onClick={() => setSuccessMessage(null)}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      {/* Filters */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiFilter className="w-5 h-5 text-primary-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">تصفية النتائج</h2>
          </div>
        </div>

        <div className="p-4 bg-white rounded-b-lg">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="action" className="block text-sm font-medium text-gray-700 mb-2">
                نوع النشاط
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FiActivity className="text-gray-400" />
                </div>
                <select
                  id="action"
                  name="action"
                  className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
                  value={filters.action}
                  onChange={handleFilterChange}
                >
                  <option value="">الكل</option>
                  {getActionOptions()}
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-2">
                المستخدم
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <FiSearch className="text-gray-400" />
                </div>
                <select
                  id="userId"
                  name="userId"
                  className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
                  value={filters.userId}
                  onChange={handleFilterChange}
                >
                  <option value="">الكل</option>
                  {users.map(user => (
                    <option key={user.id} value={user.id}>
                      {user.username}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="btn btn-secondary flex items-center shadow-sm hover:shadow-md transition-all"
              >
                <FiRefreshCw className="ml-1" />
                إعادة ضبط
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Logs List */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiActivity className="w-5 h-5 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">سجل النشاط</h2>
          </div>
        </div>

        <div className="p-4 bg-white rounded-b-lg">
          {loading && logs.length === 0 ? (
            <div className="text-center py-8">
              <div className="animate-spin h-12 w-12 border-4 border-primary-500 rounded-full border-t-transparent mb-4 mx-auto"></div>
              <p className="text-gray-500 font-medium">جاري تحميل سجل النشاط...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="bg-red-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-red-500 font-medium text-lg mb-4">{error}</p>
              <button
                onClick={() => fetchLogs(true)}
                className="btn btn-secondary inline-flex items-center px-4 py-2"
              >
                <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                إعادة المحاولة
              </button>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8">
              <FiActivity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">لا يوجد سجل نشاط</h3>
              <p className="mt-1 text-gray-500">لم يتم تسجيل أي نشاط بعد.</p>
            </div>
          ) : (
            <div className="overflow-hidden rounded-lg shadow-sm border border-gray-200">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        النشاط
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التفاصيل
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التاريخ
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentLogs.map((log) => (
                      <tr key={log.id} className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold shadow-sm">
                              {log.user_username?.charAt(0).toUpperCase() || '?'}
                            </div>
                            <div className="mr-3">
                              <div className="text-sm font-medium text-gray-900">{log.user_username}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                            {getActionText(log.action)}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">{log.details || '-'}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-500">{formatDate(log.timestamp)}</div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      عرض {indexOfFirstLog + 1} إلى {Math.min(indexOfLastLog, logs.length)} من أصل {logs.length} سجل
                    </div>
                    <div className="flex space-x-1 space-x-reverse">
                      <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <FiChevronRight className="ml-1" />
                        <span>السابق</span>
                      </button>

                      {/* أرقام الصفحات */}
                      {[...Array(totalPages)].map((_, index) => {
                        const pageNumber = index + 1;
                        // عرض أول صفحتين وآخر صفحتين والصفحة الحالية وما حولها
                        if (
                          pageNumber === 1 ||
                          pageNumber === totalPages ||
                          (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                        ) {
                          return (
                            <button
                              key={pageNumber}
                              onClick={() => paginate(pageNumber)}
                              className={`px-3 py-1 rounded-md ${
                                currentPage === pageNumber
                                  ? 'bg-primary-600 text-white'
                                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                              }`}
                            >
                              {pageNumber}
                            </button>
                          );
                        } else if (
                          (pageNumber === 2 && currentPage > 3) ||
                          (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                        ) {
                          // عرض نقاط للصفحات المحذوفة
                          return <span key={pageNumber} className="px-2 py-1">...</span>;
                        }
                        return null;
                      })}

                      <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === totalPages
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <span>التالي</span>
                        <FiChevronLeft className="mr-1" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Clear Logs Modal */}
      {showClearModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      مسح سجل النشاط
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      تأكيد مسح سجل النشاط بالكامل
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير هام</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم حذف جميع سجلات النشاط في النظام بشكل نهائي.
                        </p>
                        <p className="font-bold mt-1">
                          هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-yellow-800">نصيحة</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          يمكنك استخدام خاصية التصفية بدلاً من مسح السجل بالكامل للاطلاع على سجلات محددة.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleClearLogs}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${clearing
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-red-600 hover:bg-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={clearing}
                >
                  <FiTrash2 className="ml-2" />
                  {clearing ? 'جاري المسح...' : 'تأكيد المسح'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowClearModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ActivityLogs;
