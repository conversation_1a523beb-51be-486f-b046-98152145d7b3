import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import {
  FiArrowRight,
  FiUpload,
  FiUsers,
  FiCheck,
  FiAlertTriangle,
  FiSearch,
  FiFile,
  FiX,
  FiInfo,
  FiCheckCircle
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const BulkUpload = () => {
  const navigate = useNavigate();
  const { user, token, getToken } = useAuth();
  const [employees, setEmployees] = useState([]);
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [existingFiles, setExistingFiles] = useState([]);

  const [showAlertModal, setShowAlertModal] = useState(false);
  const [uploadResults, setUploadResults] = useState(null);

  useEffect(() => {
    fetchEmployees();
  }, []);

  // تحديث حالة تحديد الكل عندما يتغير تحديد الموظفين
  useEffect(() => {
    if (selectedEmployees.length === 0) {
      setSelectAll(false);
    } else if (selectedEmployees.length === employees.length && employees.length > 0) {
      setSelectAll(true);
    }
  }, [selectedEmployees, employees]);

  // تحديث قائمة الموظفين المحددين عند تغيير البحث
  useEffect(() => {
    if (selectAll) {
      // تحديد جميع الموظفين المرئيين (مع مراعاة البحث)
      const visibleEmployees = employees
        .filter(employee => employee.name.toLowerCase().includes(searchQuery.toLowerCase()))
        .map(employee => employee.name);
      setSelectedEmployees(visibleEmployees);
    }
  }, [searchQuery, employees, selectAll]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/employees');
      setEmployees(response.data);
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError('حدث خطأ أثناء جلب بيانات الموظفين');
    } finally {
      setLoading(false);
    }
  };

  const handleEmployeeSelect = (employeeName) => {
    setSelectedEmployees(prev => {
      if (prev.includes(employeeName)) {
        return prev.filter(name => name !== employeeName);
      } else {
        return [...prev, employeeName];
      }
    });
  };

  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);

    if (newSelectAll) {
      // تحديد جميع الموظفين المرئيين (مع مراعاة البحث)
      const visibleEmployees = employees.filter(employee =>
        employee.name.toLowerCase().includes(searchQuery.toLowerCase())
      ).map(employee => employee.name);
      setSelectedEmployees(visibleEmployees);
    } else {
      // إلغاء تحديد جميع الموظفين
      setSelectedEmployees([]);
    }
  };

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      // التحقق من عدد الملفات
      if (files.length > 20) {
        setError('لا يمكن رفع أكثر من 20 ملف في وقت واحد');
        return;
      }

      // حساب الحجم الإجمالي للملفات
      const totalSize = files.reduce((total, file) => total + file.size, 0);
      const totalSizeMB = totalSize / (1024 * 1024);

      // التحقق من الحجم الإجمالي
      if (totalSizeMB > 100) {
        setError(`لا يمكن رفع ملفات يتجاوز حجمها الإجمالي 100 ميجابايت. الحجم الحالي: ${totalSizeMB.toFixed(2)} ميجابايت`);
        return;
      }

      setSelectedFiles(files);
      setError(null); // إزالة أي رسائل خطأ سابقة
      console.log('Files selected via input:', files.length, 'files:', files.map(f => f.name), `Total size: ${totalSizeMB.toFixed(2)} MB`);
    }
  };

  // معالجات أحداث سحب وإفلات الملفات
  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragEnter = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files);

      // التحقق من عدد الملفات
      if (files.length > 20) {
        setError('لا يمكن رفع أكثر من 20 ملف في وقت واحد');
        return;
      }

      // حساب الحجم الإجمالي للملفات
      const totalSize = files.reduce((total, file) => total + file.size, 0);
      const totalSizeMB = totalSize / (1024 * 1024);

      // التحقق من الحجم الإجمالي
      if (totalSizeMB > 100) {
        setError(`لا يمكن رفع ملفات يتجاوز حجمها الإجمالي 100 ميجابايت. الحجم الحالي: ${totalSizeMB.toFixed(2)} ميجابايت`);
        return;
      }

      // تعيين الملفات المسحوبة مباشرة
      setSelectedFiles(files);
      setError(null); // إزالة أي رسائل خطأ سابقة
      e.dataTransfer.clearData();
      console.log('Files dropped:', files.length, 'files:', files.map(f => f.name), `Total size: ${totalSizeMB.toFixed(2)} MB`);
    }
  };

  const removeFile = (index) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  const handleUpload = async (e) => {
    if (e && e.preventDefault) {
      e.preventDefault();
    }

    console.log('Upload button clicked - handleUpload function called');
    // تمت إزالة التنبيه هنا

    if (selectedFiles.length === 0) {
      setError('يرجى اختيار ملف واحد على الأقل للرفع');
      console.log('No files selected');
      // تمت إزالة التنبيه هنا
      return;
    }

    console.log('Selected files:', selectedFiles.map(f => f.name));

    if (selectedEmployees.length === 0) {
      setError('يرجى اختيار موظف واحد على الأقل');
      return;
    }

    try {
      setUploading(true);
      setUploadProgress(0);
      setError(null);
      setSuccess(false);

      console.log('Selected employees:', selectedEmployees);
      console.log('Selected files:', selectedFiles);

      const formData = new FormData();

      // Append each file
      selectedFiles.forEach((file) => {
        formData.append('files', file);
        console.log(`Appended file: ${file.name}, size: ${file.size}`);
      });

      // Convert selectedEmployees array to a format that can be sent in FormData
      selectedEmployees.forEach((employee, index) => {
        formData.append(`employees[${index}]`, employee);
        console.log(`Appended employee[${index}]: ${employee}`);
      });

      // Also append the entire array as JSON for backup
      formData.append('employeesJson', JSON.stringify(selectedEmployees));
      console.log('Appended employeesJson:', JSON.stringify(selectedEmployees));

      // Log all form data entries for debugging
      console.log('FormData entries:');
      for (let pair of formData.entries()) {
        console.log(pair[0], pair[1]);
      }

      // Use the direct upload endpoint for multiple files (with updated path)
      console.log('Sending request to: /api/direct-upload-multiple');

      // Get token from useAuth hook
      const currentToken = token || getToken();
      console.log('Token available:', !!currentToken);
      console.log('Token value:', currentToken);

      if (!currentToken) {
        setError('لم يتم العثور على رمز المصادقة. يرجى تسجيل الدخول مرة أخرى.');
        return;
      }

      const response = await axios.post('/api/direct-upload-multiple', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${currentToken}`
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentCompleted);
          console.log(`Upload progress: ${percentCompleted}%`);
        },
        timeout: 600000 // 10 minutes timeout
      });

      console.log('Upload response:', response.data);

      // Reset form
      setSelectedFiles([]);
      setUploadProgress(0);

      // إلغاء تحديد الموظفين بعد النجاح
      setSelectedEmployees([]);
      setSelectAll(false);

      // Reset file input
      document.getElementById('file').value = '';

      // تخزين نتائج الرفع
      setUploadResults(response.data);

      // التحقق من النتائج وعرض الرسائل المناسبة
      const hasSuccessfulUploads = response.data.successCount > 0;
      const hasExistingFiles = response.data.existingFilesCount > 0;

      if (hasExistingFiles) {
        // إعداد بيانات الملفات المكررة لعرضها في النافذة المنبثقة
        setExistingFiles(response.data.existingFiles || []);
        setShowAlertModal(true);
      }

      if (hasSuccessfulUploads) {
        // إعداد رسالة النجاح بناءً على الملفات والموظفين الأصليين
        const successCount = response.data.successCount;
        const totalFiles = selectedFiles.length;
        const totalEmployees = selectedEmployees.length;
        const existingCount = response.data.existingFilesCount || 0;

        // حساب عدد الملفات الفريدة التي تم رفعها بنجاح
        const totalOperations = totalFiles * totalEmployees;
        const successfulOperations = successCount;
        const failedOperations = existingCount;

        // حساب عدد الملفات التي تم رفعها فعلياً (بدون تكرار)
        const uniqueFilesUploaded = Math.min(totalFiles, Math.ceil(successfulOperations / totalEmployees));

        let message = '';

        // بناء الرسالة بناءً على السيناريو
        if (totalFiles === 1 && totalEmployees === 1) {
          // ملف واحد لموظف واحد
          message = 'تم رفع الملف بنجاح';
        } else if (totalFiles === 1) {
          // ملف واحد لعدة موظفين
          if (successfulOperations === totalEmployees) {
            message = `تم رفع الملف بنجاح لجميع الموظفين المحددين (${totalEmployees} موظفين)`;
          } else {
            const successfulEmployees = successfulOperations;
            message = `تم رفع الملف بنجاح لـ ${successfulEmployees} من أصل ${totalEmployees} موظفين`;
          }
        } else if (totalEmployees === 1) {
          // عدة ملفات لموظف واحد
          if (totalFiles === uniqueFilesUploaded) {
            message = `تم رفع جميع الملفات (${totalFiles} ملفات) بنجاح للموظف المحدد`;
          } else {
            message = `تم رفع ${uniqueFilesUploaded} من أصل ${totalFiles} ملفات للموظف المحدد`;
          }
        } else {
          // عدة ملفات لعدة موظفين
          if (successfulOperations === totalOperations) {
            message = `تم رفع جميع الملفات (${totalFiles} ملفات) بنجاح لجميع الموظفين المحددين (${totalEmployees} موظفين)`;
          } else {
            message = `تم رفع ${totalFiles} ملفات بنجاح (${successfulOperations} عملية رفع من أصل ${totalOperations})`;
          }
        }

        // إضافة معلومات عن الملفات المكررة إذا وجدت
        if (hasExistingFiles) {
          if (existingCount === 1) {
            message += ` - تم تخطي عملية رفع واحدة لوجود الملف مسبقاً`;
          } else {
            message += ` - تم تخطي ${existingCount} عمليات رفع لوجود الملفات مسبقاً`;
          }
        }

        setSuccessMessage(message);
        setSuccess(true);

        // إخفاء رسالة النجاح بعد 7 ثوان (وقت أطول للقراءة)
        setTimeout(() => {
          setSuccess(false);
        }, 7000);
      } else if (hasExistingFiles) {
        // إذا لم يتم رفع أي ملفات جديدة ولكن توجد ملفات مكررة
        // لا نعرض رسالة نجاح، فقط النافذة المنبثقة للملفات المكررة
        setSuccess(false);
      }
    } catch (err) {
      console.error('Error uploading file:', err);

      if (err.response) {
        // الخادم استجاب برمز حالة خارج نطاق 2xx
        console.error('Error response status:', err.response.status);
        console.error('Error response headers:', err.response.headers);
        console.error('Error response data:', err.response.data);

        // التحقق من نوع الخطأ
        if (err.response.status === 413) {
          setError('حجم الملفات كبير جدًا. يرجى تقليل عدد الملفات أو حجمها والمحاولة مرة أخرى.');
        } else if (err.response.data && err.response.data.message) {
          setError(`حدث خطأ أثناء رفع الملف: ${err.response.data.message}`);
        } else {
          setError(`حدث خطأ أثناء رفع الملف (${err.response.status}): ${err.response.statusText || 'خطأ غير معروف'}`);
        }
      } else if (err.request) {
        // تم إرسال الطلب ولكن لم يتم استلام استجابة
        console.error('Error request (no response received):', err.request);

        if (err.code === 'ECONNABORTED') {
          setError('انتهت مهلة الاتصال أثناء رفع الملفات. يرجى تقليل عدد الملفات أو حجمها والمحاولة مرة أخرى.');
        } else {
          setError('لم يتم استلام استجابة من الخادم. تحقق من اتصالك بالإنترنت واتصال الخادم.');
        }
      } else {
        // حدث خطأ أثناء إعداد الطلب
        console.error('Error setting up request:', err.message);
        console.error('Error stack:', err.stack);
        setError('حدث خطأ أثناء إعداد طلب الرفع: ' + err.message);
      }

      // طباعة معلومات إضافية للتصحيح
      console.error('Full error object:', JSON.stringify(err, Object.getOwnPropertyNames(err)));
    } finally {
      setUploading(false);
    }
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate('/employees')}
          className="flex items-center text-gray-600 hover:text-gray-900 transform hover:scale-110"
        >
          <FiArrowRight className="ml-1" />
          <span>العودة إلى قائمة الموظفين</span>
        </button>

        <div className="flex items-center text-sm text-gray-500">
          <FiInfo className="ml-1 text-blue-500" />
          <span>يمكنك رفع عدة ملفات لعدة موظفين في نفس الوقت</span>
        </div>
      </div>

      <div className="card mb-6 shadow-lg border-t-4 border-blue-500">
        <div className="bg-gradient-to-r from-blue-50 to-white p-6 rounded-t-lg">
          <h1 className="text-3xl font-bold text-gray-800 mb-3 flex items-center">
            <FiUpload className="ml-2 text-blue-500" />
            رفع ملفات لعدة موظفين
          </h1>
          <p className="text-gray-600 mb-2 text-lg">
            يمكنك رفع عدة ملفات لعدة موظفين في وقت واحد بكل سهولة.
          </p>
          <p className="text-gray-500 text-sm">
            اختر الملفات والموظفين المطلوبين ثم اضغط على زر الرفع.
          </p>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-4 text-sm text-red-700 bg-red-100 rounded-lg flex items-center border-r-4 border-red-500 shadow-sm">
            <FiAlertTriangle className="ml-2 flex-shrink-0 text-lg text-red-600" />
            <span className="font-bold">{error}</span>
          </div>
        )}

        {success && (
          <div className="mx-6 mt-4 mb-4 bg-green-50 border border-green-200 text-green-800 rounded-lg shadow-md p-4 transition-all duration-500 animate-fade-in">
            <div className="flex items-start">
              <FiCheckCircle className="text-green-500 text-xl ml-2 flex-shrink-0 mt-0.5" />
              <div className="flex-grow">
                <div className="font-medium text-green-800 mb-1">عملية رفع ناجحة!</div>
                <div className="text-sm text-green-700">{successMessage}</div>
              </div>
              <button
                onClick={() => setSuccess(false)}
                className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
              >
                <FiX />
              </button>
            </div>
          </div>
        )}



        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* القسم الأول: اختيار الملفات */}
            <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center ml-3">
                  <FiFile className="text-blue-600 text-xl" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-800">اختر الملفات</h2>
                  <p className="text-xs text-gray-500">يمكنك اختيار عدة ملفات في نفس الوقت</p>
                </div>
              </div>

              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
              >
                <input
                  type="file"
                  id="file"
                  onChange={handleFileChange}
                  className="hidden"
                  disabled={uploading}
                  multiple
                  required
                />
                <label htmlFor="file" className="cursor-pointer block">
                  <FiUpload className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                  <span className="block text-sm font-medium text-gray-700 mb-1">
                    {selectedFiles.length > 0 ? `تم اختيار ${selectedFiles.length} ملفات` : 'اضغط لاختيار الملفات'}
                  </span>
                  <span className="text-xs text-gray-500">أو قم بسحب الملفات وإفلاتها هنا</span>
                </label>

                {selectedFiles.length > 0 && (
                  <div className="mt-4 text-right">
                    <p className="text-sm font-medium text-gray-700 mb-2">الملفات المختارة:</p>
                    <ul className="text-xs text-gray-600 max-h-32 overflow-y-auto">
                      {selectedFiles.map((file, index) => (
                        <li key={index} className="mb-2 flex items-center justify-between bg-gray-50 p-2 rounded-md border border-gray-200">
                          <div className="flex items-center">
                            <FiFile className="ml-1 text-blue-500" />
                            <span className="truncate max-w-[150px]">{file.name}</span>
                            <span className="mr-1 text-gray-500">({Math.round(file.size / 1024)} KB)</span>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                            title="إزالة الملف"
                          >
                            <FiX />
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {uploading && (
                <div className="mt-4">
                  <div className="flex justify-between text-xs text-gray-600 mb-1">
                    <span>جاري الرفع...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* القسم الثاني: اختيار الموظفين */}
            <div className="bg-white p-5 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center ml-3">
                  <FiUsers className="text-green-600 text-xl" />
                </div>
                <div>
                  <h2 className="text-lg font-semibold text-gray-800">اختر الموظفين</h2>
                  <p className="text-xs text-gray-500">حدد الموظفين الذين تريد رفع الملفات لهم</p>
                </div>
              </div>

              <div className="flex justify-between items-center mb-3">
                <div className="relative flex-1">
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <FiSearch className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="input pr-10 w-full focus:ring-2 focus:ring-blue-300 transition-all"
                    placeholder="ابحث عن موظف..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <button
                  type="button"
                  onClick={handleSelectAll}
                  className="mr-2 text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                >
                  {selectAll ? 'إلغاء تحديد الكل' : 'تحديد الكل'}
                </button>
              </div>

              {loading ? (
                <div className="flex items-center justify-center h-40 bg-gray-50 rounded-lg">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  <p className="mr-3 text-gray-500">جاري تحميل بيانات الموظفين...</p>
                </div>
              ) : employees.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-40 bg-gray-50 rounded-lg">
                  <FiUsers className="h-10 w-10 text-gray-400 mb-2" />
                  <p className="text-gray-500">لا يوجد موظفين</p>
                </div>
              ) : (
                <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg p-2 bg-gray-50">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {employees
                      .filter(employee => employee.name.toLowerCase().includes(searchQuery.toLowerCase()))
                      .map((employee) => (
                        <div
                          key={employee.name}
                          className={`flex items-center p-2 rounded-md transition-colors ${
                            selectedEmployees.includes(employee.name)
                              ? 'bg-blue-50 border border-blue-200'
                              : 'hover:bg-gray-100'
                          }`}
                        >
                          <input
                            type="checkbox"
                            id={`employee-${employee.name}`}
                            checked={selectedEmployees.includes(employee.name)}
                            onChange={() => handleEmployeeSelect(employee.name)}
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                          />
                          <label
                            htmlFor={`employee-${employee.name}`}
                            className={`text-sm cursor-pointer ${
                              selectedEmployees.includes(employee.name) ? 'font-medium text-blue-700' : 'text-gray-700'
                            }`}
                          >
                            {employee.name}
                          </label>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              <div className="mt-3 flex items-center justify-between">
                <p className="text-sm text-gray-600">
                  تم تحديد <span className="font-medium text-blue-600">{selectedEmployees.length}</span> من أصل <span className="font-medium">{employees.length}</span> موظف
                </p>
                {searchQuery && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    البحث: "{searchQuery}"
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-center">
            <button
              type="button"
              onClick={handleUpload}
              className={`
                flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                transition-all duration-300 shadow-md
                ${uploading || selectedFiles.length === 0 || selectedEmployees.length === 0
                  ? 'bg-gray-400 cursor-not-allowed opacity-70'
                  : 'bg-blue-600 hover:bg-blue-700 hover:shadow-lg'
                }
              `}
              disabled={uploading || selectedFiles.length === 0 || selectedEmployees.length === 0}
            >
              <FiUpload className="ml-2 text-lg" />
              {uploading ? 'جاري الرفع...' : 'رفع الملفات للموظفين المحددين'}
            </button>
          </div>
        </div>
      </div>

      {/* Alert Modal for Existing Files */}
      {showAlertModal && existingFiles.length > 0 && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
                      <FiAlertTriangle className="ml-2 text-yellow-600" />
                      نتائج عملية الرفع
                    </h3>
                    <div className="mt-4">
                      {/* عرض نتائج النجاح أولاً */}
                      {uploadResults && uploadResults.successCount > 0 && (
                        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <div className="flex items-center mb-2">
                            <FiCheckCircle className="ml-2 text-green-600" />
                            <span className="font-medium text-green-800">تم بنجاح</span>
                          </div>
                          <p className="text-sm text-green-700">
                            تم رفع <span className="font-bold">{uploadResults.successCount}</span> ملفات بنجاح للموظفين المحددين.
                          </p>
                        </div>
                      )}

                      {/* عرض الملفات المكررة */}
                      {existingFiles.length > 0 && (
                        <div className="mb-4">
                          <div className="flex items-center mb-2">
                            <FiAlertTriangle className="ml-2 text-yellow-600" />
                            <span className="font-medium text-yellow-800">ملفات مكررة</span>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">
                            تم تخطي الملفات التالية لأنها موجودة مسبقاً:
                          </p>
                          <div className="max-h-40 overflow-y-auto bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                            {existingFiles.map((file, index) => (
                              <div key={index} className="mb-2 pb-2 border-b border-yellow-200 last:border-0 flex items-center">
                                <FiFile className="ml-2 text-yellow-600" />
                                <div>
                                  <div className="font-medium text-sm">{file.fileName}</div>
                                  <div className="text-xs text-gray-500">الموظف: {file.employee}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* رسالة إضافية إذا لم يتم رفع أي ملفات */}
                      {uploadResults && uploadResults.successCount === 0 && existingFiles.length > 0 && (
                        <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                          <p className="text-sm text-gray-600">
                            لم يتم رفع أي ملفات جديدة لأن جميع الملفات المحددة موجودة مسبقاً.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  onClick={() => {
                    setShowAlertModal(false);
                    setExistingFiles([]);
                    setUploadResults(null);
                  }}
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                >
                  فهمت
                </button>
              </div>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default BulkUpload;
