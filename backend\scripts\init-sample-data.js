/**
 * This script initializes the data directory with sample employee folders and files
 * Run with: node scripts/init-sample-data.js
 */

const fs = require('fs-extra');
const path = require('path');

// Base directory for data
const dataDir = path.join(__dirname, '../data');
const employeeDataDir = path.join(dataDir, 'employee_database');

// Ensure directories exist
fs.ensureDirSync(dataDir);
fs.ensureDirSync(employeeDataDir);

// Sample employees
const employees = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>'
];

// Create a simple text file instead of PDF
const createSampleFile = (filePath, content) => {
  fs.writeFileSync(filePath, content);
};

// Create employee folders and sample files
const initSampleData = () => {
  console.log('Initializing sample data...');

  // Create backups directory
  const backupsDir = path.join(dataDir, 'backups');
  fs.ensureDirSync(backupsDir);

  for (const employee of employees) {
    const employeePath = path.join(employeeDataDir, employee);
    fs.ensureDirSync(employeePath);

    console.log(`Created employee folder: ${employee}`);

    // Create sample files for each employee
    const fileNames = [
      `Salary_2023_${employee}.txt`,
      `Performance_Report_${employee}.txt`,
      `Annual_Leave_${employee}.txt`
    ];

    for (const fileName of fileNames) {
      const filePath = path.join(employeePath, fileName);

      // Skip if file already exists
      if (fs.existsSync(filePath)) {
        continue;
      }

      // Create sample file
      createSampleFile(
        filePath,
        `This is a sample file ${fileName} for employee ${employee}.\n\nThis file was automatically created as an example for display only.`
      );

      console.log(`Created sample file: ${employee}/${fileName}`);
    }
  }

  console.log('Sample data initialization complete!');
};

try {
  initSampleData();
} catch (err) {
  console.error('Error initializing sample data:', err);
  process.exit(1);
}
