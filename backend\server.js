const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const { initializeDatabase } = require('./db/database');

// وظيفة للتحقق من وجود نسخة احتياطية معلقة واستعادتها
const checkAndRestorePendingBackup = () => {
  console.log('Checking for pending backup restore...');

  const dataDir = path.join(__dirname, 'data');
  console.log(`Data directory: ${dataDir}`);

  // تأكد من وجود مجلد البيانات
  if (!fs.existsSync(dataDir)) {
    console.log('Data directory does not exist, creating it');
    fs.ensureDirSync(dataDir);
  }

  const flagPath = path.join(dataDir, 'restore_pending.flag');
  const pendingRestorePath = path.join(dataDir, 'pending_restore');

  console.log(`Checking for flag file: ${flagPath}`);

  // التحقق من وجود ملف العلامة
  if (fs.existsSync(flagPath)) {
    console.log('Flag file found, starting restore process');

    try {
      console.log('تم العثور على نسخة احتياطية معلقة. جاري استعادتها...');

      // قراءة معلومات النسخة الاحتياطية
      let flagData;
      try {
        const flagContent = fs.readFileSync(flagPath, 'utf8');
        console.log(`Flag file content: ${flagContent}`);
        flagData = JSON.parse(flagContent);
        console.log(`Parsed flag data: ${JSON.stringify(flagData)}`);
      } catch (parseErr) {
        console.error('Error parsing flag file:', parseErr);
        throw new Error('ملف العلامة تالف أو غير صالح');
      }

      console.log(`استعادة النسخة الاحتياطية: ${flagData.filename} (${flagData.timestamp})`);

      // التحقق من وجود مجلد النسخة الاحتياطية المؤقتة
      console.log(`Checking for pending restore directory: ${pendingRestorePath}`);
      if (fs.existsSync(pendingRestorePath)) {
        console.log('Pending restore directory found');

        const tempDbPath = path.join(pendingRestorePath, 'archive.db');
        const tempEmployeeDataDir = path.join(pendingRestorePath, 'employee_database');

        console.log(`Checking for database file: ${tempDbPath}`);
        const dbExists = fs.existsSync(tempDbPath);
        console.log(`Database file exists: ${dbExists}`);

        console.log(`Checking for employee data directory: ${tempEmployeeDataDir}`);
        const employeeDataExists = fs.existsSync(tempEmployeeDataDir);
        console.log(`Employee data directory exists: ${employeeDataExists}`);

        // التحقق من وجود الملفات المطلوبة
        if (dbExists && employeeDataExists) {
          console.log('All required files found, proceeding with restore');

          // استبدال ملفات قاعدة البيانات
          const currentDbPath = path.join(dataDir, 'archive.db');
          const currentEmployeeDataDir = path.join(dataDir, 'employee_database');

          // حذف الملفات الحالية
          if (fs.existsSync(currentDbPath)) {
            console.log(`Removing current database file: ${currentDbPath}`);
            fs.unlinkSync(currentDbPath);
          }

          if (fs.existsSync(currentEmployeeDataDir)) {
            console.log(`Removing current employee data directory: ${currentEmployeeDataDir}`);
            fs.removeSync(currentEmployeeDataDir);
          }

          // نسخ الملفات الجديدة
          console.log(`Copying database file: ${tempDbPath} -> ${currentDbPath}`);
          fs.copySync(tempDbPath, currentDbPath);

          console.log(`Copying employee data directory: ${tempEmployeeDataDir} -> ${currentEmployeeDataDir}`);
          fs.copySync(tempEmployeeDataDir, currentEmployeeDataDir);

          console.log('تم استعادة النسخة الاحتياطية بنجاح.');
        } else {
          console.error('ملفات النسخة الاحتياطية المؤقتة غير مكتملة.');
        }

        // حذف مجلد النسخة الاحتياطية المؤقتة
        console.log(`Removing pending restore directory: ${pendingRestorePath}`);
        fs.removeSync(pendingRestorePath);
      } else {
        console.error('مجلد النسخة الاحتياطية المؤقتة غير موجود.');
      }

      // حذف ملف العلامة
      console.log(`Removing flag file: ${flagPath}`);
      fs.unlinkSync(flagPath);

      console.log('Backup restore process completed successfully');
    } catch (err) {
      console.error('حدث خطأ أثناء استعادة النسخة الاحتياطية المعلقة:', err);

      // محاولة حذف ملف العلامة في حالة الفشل
      try {
        console.log('Cleaning up after error');

        if (fs.existsSync(flagPath)) {
          console.log(`Removing flag file: ${flagPath}`);
          fs.unlinkSync(flagPath);
        }

        if (fs.existsSync(pendingRestorePath)) {
          console.log(`Removing pending restore directory: ${pendingRestorePath}`);
          fs.removeSync(pendingRestorePath);
        }
      } catch (e) {
        console.error('فشل في تنظيف الملفات المؤقتة:', e);
      }
    }
  } else {
    console.log('No pending backup restore found');
  }
};

// Import routes
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const fileRoutes = require('./routes/files');
const bulkUploadRoutes = require('./routes/bulk-upload');
const directUploadRoutes = require('./routes/direct-upload');
const directUploadMultipleRoutes = require('./routes/direct-upload-multiple');
const userRoutes = require('./routes/users');
const logRoutes = require('./routes/logs');
const backupRoutes = require('./routes/backup');
const statsRoutes = require('./routes/stats');

// Initialize express app
const app = express();
const PORT = process.env.PORT || 5000;

// Set UTF-8 encoding for all responses
app.use((req, res, next) => {
  res.setHeader('Content-Type', 'application/json; charset=utf-8');
  next();
});

// Middleware
app.use(cors());
app.use(express.json({ limit: '100mb' }));
app.use(express.urlencoded({ extended: true, limit: '100mb' }));

// Increase timeout
app.use((req, res, next) => {
  res.setTimeout(600000); // 10 minutes
  next();
});

// التحقق من وجود نسخة احتياطية معلقة واستعادتها قبل تهيئة قاعدة البيانات
checkAndRestorePendingBackup();

// Initialize database
initializeDatabase();

// Create data directory structure if it doesn't exist
const dataDir = path.join(__dirname, 'data', 'employee_database');

// Ensure the directory exists
fs.ensureDirSync(dataDir);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/bulk-upload', bulkUploadRoutes);
// تسجيل مسارات الرفع المباشر بمسارات مختلفة لتجنب التداخل
console.log('Registering direct upload routes at /api/direct-upload');
app.use('/api/direct-upload', directUploadRoutes);

// تسجيل مسارات الرفع المباشر المتعدد بمسار مختلف
console.log('Registering direct upload multiple routes at /api/direct-upload-multiple');
app.use('/api/direct-upload-multiple', directUploadMultipleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/logs', logRoutes);
app.use('/api/backup', backupRoutes);
app.use('/api/stats', statsRoutes);

// Serve static files (for production)
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/dist')));
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/dist/index.html'));
  });
} else {
  // حتى في وضع التطوير، نقوم بتوجيه طلبات الملفات بشكل صحيح
  app.get('/files/:employee/:filename', (req, res) => {
    // إعادة توجيه طلبات الملفات إلى الواجهة الأمامية
    res.redirect(`http://localhost:5173/files/${req.params.employee}/${req.params.filename}`);
  });

  // إضافة دعم لـ BrowserRouter في وضع التطوير
  app.get('/api/*', (req, res, next) => {
    next(); // تمرير طلبات API إلى المعالجات المناسبة
  });

  // توجيه جميع الطلبات الأخرى إلى الواجهة الأمامية
  app.get('*', (req, res) => {
    res.redirect(`http://localhost:5173${req.originalUrl}`);
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
