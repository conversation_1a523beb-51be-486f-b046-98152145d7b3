// Shared configuration file for frontend and backend 
 
const config = { 
  // Server settings 
  server: { 
    port: 5000, 
    baseUrl: 'http://localhost:5000', 
  }, 
 
  // Frontend settings 
  frontend: { 
    port: 5173, 
    baseUrl: 'http://localhost:5173', 
  }, 
 
  // Authentication settings 
  auth: { 
    jwtSecret: 'your_jwt_secret_key', 
    tokenExpiry: '24h', 
    defaultAdmin: { 
      username: 'admin', 
      password: 'admin123', 
      role: 'admin' 
    } 
  }, 
 
  // Database settings 
  database: { 
    path: './data/archive.db', 
    employeeDir: './data/employee_database' 
  } 
}; 
 
module.exports = config; 
