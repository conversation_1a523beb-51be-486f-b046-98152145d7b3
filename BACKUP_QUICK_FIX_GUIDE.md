# دليل سريع لحل مشكلة استعادة النسخ الاحتياطية

## المشكلة
رسالة خطأ: "بنية ملف النسخة الاحتياطية غير صالحة - لم يتم العثور على ملف قاعدة البيانات أو مجلد بيانات الموظفين"

## الحل السريع

### الخطوة 1: فحص النسخة الاحتياطية
1. اذهب إلى صفحة "النسخ الاحتياطي والاستعادة"
2. اضغط على "إنشاء وتنزيل نسخة احتياطية" 
3. اختر ملف النسخة الاحتياطية المراد استعادتها
4. **اضغط على زر "فحص النسخة" الجديد** ⭐
5. ستظهر نافذة تعرض حالة النسخة الاحتياطية

### الخطوة 2: تفسير نتائج الفحص

#### ✅ إذا كانت النسخة صالحة:
- ستظهر رسالة "النسخة الاحتياطية صالحة"
- يمكنك المتابعة بالاستعادة بأمان

#### ❌ إذا كانت النسخة غير صالحة:
- ستظهر الملفات المفقودة
- اتبع الحلول أدناه

### الخطوة 3: الحلول حسب المشكلة

#### المشكلة: ملف قاعدة البيانات مفقود
**الحل:**
1. تأكد من أن النسخة الاحتياطية تحتوي على ملف `.db`
2. إذا كان اسم الملف مختلف عن `archive.db`، أعد تسميته
3. تأكد من أن الملف في الجذر الرئيسي للنسخة الاحتياطية

#### المشكلة: مجلد بيانات الموظفين مفقود
**الحل:**
1. تأكد من وجود مجلد يحتوي على بيانات الموظفين
2. أسماء مقبولة للمجلد:
   - `employee_database`
   - `employee_data`
   - `employees`
   - `data`

#### المشكلة: النسخة الاحتياطية تالفة
**الحل:**
1. أنشئ نسخة احتياطية جديدة من النظام الحالي
2. استخدم النسخة الجديدة للاستعادة

## الميزات الجديدة المضافة

### 🔍 زر فحص النسخة الاحتياطية
- يظهر بجانب زر "استعادة" عند اختيار ملف
- يفحص بنية النسخة الاحتياطية قبل الاستعادة
- يعرض تقرير مفصل عن محتويات النسخة

### 📋 نافذة تفاصيل الفحص
تعرض:
- ✅/❌ حالة ملف قاعدة البيانات
- ✅/❌ حالة مجلد بيانات الموظفين  
- 📁 قائمة بجميع محتويات النسخة الاحتياطية
- 📍 مسارات الملفات الموجودة

### 🛠️ تحسينات في البحث
- البحث الذكي عن الملفات بأسماء مختلفة
- دعم بنى مختلفة للنسخ الاحتياطية
- رسائل خطأ أكثر وضوحاً

## خطوات الاستعادة المحسنة

### 1. التحضير
```
✅ تأكد من وجود نسخة احتياطية من البيانات الحالية
✅ تأكد من صلاحيات المدير
✅ أغلق جميع العمليات الأخرى في النظام
```

### 2. الفحص
```
📁 اختر ملف النسخة الاحتياطية
🔍 اضغط "فحص النسخة"
📋 راجع تقرير الفحص
✅ تأكد من صحة النسخة
```

### 3. الاستعادة
```
⚠️ اقرأ التحذيرات بعناية
✅ اضغط "استعادة"
✅ انتظر اكتمال العملية
🔄 أعد تشغيل النظام
```

## نصائح مهمة

### ✅ أفضل الممارسات:
- **دائماً** افحص النسخة الاحتياطية قبل الاستعادة
- أنشئ نسخة احتياطية من البيانات الحالية قبل الاستعادة
- استخدم نسخ احتياطية حديثة (أقل من أسبوع)
- تأكد من سلامة ملف النسخة الاحتياطية (لم يتلف أثناء النقل)

### ⚠️ تجنب هذه الأخطاء:
- لا تستعيد نسخة احتياطية دون فحصها أولاً
- لا تستخدم نسخ احتياطية من أنظمة مختلفة دون تأكيد التوافق
- لا تقاطع عملية الاستعادة أثناء التنفيذ

## إذا استمرت المشكلة

### 🔧 أدوات التشخيص المتقدمة:
1. **أداة التشخيص الشاملة:**
   ```bash
   node backup-diagnostic.js
   ```

2. **فحص نسخة احتياطية محددة:**
   ```bash
   node check-existing-backup.js path/to/backup.zip
   ```

### 📞 طلب المساعدة:
إذا لم تنجح الحلول أعلاه:
1. احفظ رسالة الخطأ كاملة
2. احفظ تقرير فحص النسخة الاحتياطية
3. تحقق من logs الخادم
4. اتصل بالدعم الفني مع هذه المعلومات

---

## ملخص سريع

1. **اختر النسخة الاحتياطية** 📁
2. **اضغط "فحص النسخة"** 🔍  
3. **راجع النتائج** 📋
4. **إذا كانت صالحة → استعد** ✅
5. **إذا كانت غير صالحة → أصلح أو استخدم نسخة أخرى** 🔧

**الميزة الجديدة الأهم: زر "فحص النسخة" يوفر عليك الوقت ويمنع الأخطاء!** ⭐
