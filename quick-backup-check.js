// أداة فحص سريعة للنسخة الاحتياطية
// الاستخدام: node quick-backup-check.js <مسار_النسخة_الاحتياطية>

const fs = require('fs-extra');
const path = require('path');
const { execSync } = require('child_process');

const backupPath = process.argv[2];

if (!backupPath) {
  console.log('❌ يرجى تحديد مسار النسخة الاحتياطية');
  console.log('الاستخدام: node quick-backup-check.js <مسار_النسخة_الاحتياطية>');
  process.exit(1);
}

console.log('🔍 فحص سريع للنسخة الاحتياطية');
console.log('================================\n');

// التحقق من وجود الملف
if (!fs.existsSync(backupPath)) {
  console.log('❌ الملف غير موجود:', backupPath);
  process.exit(1);
}

const stats = fs.statSync(backupPath);
console.log(`📁 الملف: ${backupPath}`);
console.log(`📏 الحجم: ${(stats.size / (1024 * 1024)).toFixed(2)} ميجابايت`);
console.log(`📅 تاريخ التعديل: ${stats.mtime.toLocaleString('ar')}\n`);

// استخراج النسخة الاحتياطية
const extractDir = path.join(__dirname, 'temp-quick-check');

try {
  // تنظيف المجلد المؤقت
  if (fs.existsSync(extractDir)) {
    fs.removeSync(extractDir);
  }
  fs.ensureDirSync(extractDir);

  console.log('📦 جاري استخراج النسخة الاحتياطية...');
  execSync(`powershell -command "Expand-Archive -Path '${backupPath}' -DestinationPath '${extractDir}' -Force"`, {
    stdio: 'inherit'
  });

  console.log('✅ تم الاستخراج بنجاح\n');

  // البحث المحسن عن الملفات
  const findFileOrDir = (dir, nameToFind, isDir = false, depth = 0) => {
    if (depth > 5) return null;
    
    try {
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);

        // مطابقة دقيقة
        if (entry.name === nameToFind && entry.isDirectory() === isDir) {
          return fullPath;
        }

        // البحث المحسن لملف قاعدة البيانات
        if (!isDir && nameToFind === 'archive.db') {
          if (entry.name.endsWith('.db') || 
              entry.name.toLowerCase().includes('archive') ||
              entry.name.toLowerCase().includes('database') ||
              entry.name.toLowerCase().includes('db')) {
            return fullPath;
          }
        }

        // البحث المحسن لمجلد الموظفين
        if (isDir && nameToFind === 'employee_database') {
          const lowerName = entry.name.toLowerCase();
          if (lowerName.includes('employee') ||
              lowerName.includes('data') ||
              lowerName === 'employees' ||
              lowerName === 'employee_data' ||
              lowerName === 'emp_data' ||
              lowerName === 'uploads' ||
              lowerName === 'files') {
            return fullPath;
          }
        }

        // البحث في المجلدات الفرعية
        if (entry.isDirectory()) {
          const found = findFileOrDir(fullPath, nameToFind, isDir, depth + 1);
          if (found) return found;
        }
      }
    } catch (err) {
      console.error(`❌ خطأ في قراءة المجلد ${dir}:`, err.message);
    }
    
    return null;
  };

  // البحث عن الملفات المطلوبة
  console.log('🔍 البحث عن الملفات المطلوبة...\n');

  const dbPath = findFileOrDir(extractDir, 'archive.db', false);
  const employeeDir = findFileOrDir(extractDir, 'employee_database', true);

  // عرض النتائج
  console.log('📊 نتائج الفحص:');
  console.log('================');

  console.log(`📄 ملف قاعدة البيانات: ${dbPath ? '✅ موجود' : '❌ غير موجود'}`);
  if (dbPath) {
    const dbStats = fs.statSync(dbPath);
    console.log(`   📍 المسار: ${path.relative(extractDir, dbPath)}`);
    console.log(`   📏 الحجم: ${(dbStats.size / 1024).toFixed(1)} كيلوبايت`);
  }

  console.log(`📁 مجلد بيانات الموظفين: ${employeeDir ? '✅ موجود' : '❌ غير موجود'}`);
  if (employeeDir) {
    console.log(`   📍 المسار: ${path.relative(extractDir, employeeDir)}`);
    try {
      const employees = fs.readdirSync(employeeDir);
      console.log(`   👥 عدد الموظفين: ${employees.length}`);
    } catch (err) {
      console.log(`   ❌ خطأ في قراءة المجلد: ${err.message}`);
    }
  }

  // الحكم النهائي
  console.log('\n🎯 الحكم النهائي:');
  console.log('================');

  if (dbPath && employeeDir) {
    console.log('✅ النسخة الاحتياطية صالحة ويمكن استعادتها');
  } else {
    console.log('❌ النسخة الاحتياطية غير صالحة');
    
    if (!dbPath) {
      console.log('   - ملف قاعدة البيانات مفقود');
    }
    if (!employeeDir) {
      console.log('   - مجلد بيانات الموظفين مفقود');
    }

    console.log('\n🔍 تحليل مفصل للمحتويات:');
    
    // عرض جميع الملفات والمجلدات
    const analyzeContents = (dir, prefix = '', depth = 0) => {
      if (depth > 3) return;
      
      const entries = fs.readdirSync(dir, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        
        if (entry.isDirectory()) {
          console.log(`${prefix}📁 ${entry.name}/`);
          
          // تحديد المجلدات المحتملة
          const lowerName = entry.name.toLowerCase();
          if (lowerName.includes('employee') || lowerName.includes('data') || 
              lowerName === 'employees' || lowerName === 'uploads' || lowerName === 'files') {
            console.log(`${prefix}   ⭐ مجلد محتمل لبيانات الموظفين!`);
          }
          
          if (depth < 2) {
            analyzeContents(fullPath, prefix + '  ', depth + 1);
          }
        } else {
          const fileStats = fs.statSync(fullPath);
          console.log(`${prefix}📄 ${entry.name} (${(fileStats.size / 1024).toFixed(1)} KB)`);
          
          // تحديد الملفات المحتملة
          if (entry.name.endsWith('.db') || 
              entry.name.toLowerCase().includes('archive') ||
              entry.name.toLowerCase().includes('database')) {
            console.log(`${prefix}   ⭐ ملف محتمل لقاعدة البيانات!`);
          }
        }
      }
    };

    analyzeContents(extractDir);
  }

} catch (err) {
  console.error('❌ خطأ في فحص النسخة الاحتياطية:', err.message);
} finally {
  // تنظيف المجلد المؤقت
  try {
    if (fs.existsSync(extractDir)) {
      fs.removeSync(extractDir);
      console.log('\n🧹 تم تنظيف الملفات المؤقتة');
    }
  } catch (cleanupErr) {
    console.log('\n⚠️ تحذير: فشل في تنظيف الملفات المؤقتة:', cleanupErr.message);
  }
}

console.log('\n================================');
console.log('انتهى الفحص السريع');

// نصائح للمستخدم
if (!dbPath || !employeeDir) {
  console.log('\n💡 نصائح لحل المشكلة:');
  console.log('1. تأكد من أن النسخة الاحتياطية تم إنشاؤها من نفس النظام');
  console.log('2. جرب إنشاء نسخة احتياطية جديدة من النظام الحالي');
  console.log('3. تحقق من أن الملف لم يتلف أثناء النقل أو التخزين');
  console.log('4. إذا رأيت ملفات مميزة بـ ⭐، قد تحتاج لإعادة تسميتها');
}
