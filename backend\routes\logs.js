const express = require('express');
const { db } = require('../db/database');
const { authenticateToken, isAdmin } = require('../middleware/auth');

const router = express.Router();

// Get all activity logs (admin only)
router.get('/', authenticateToken, isAdmin, (req, res) => {
  const { limit = 100, offset = 0, action, userId } = req.query;
  
  let query = `
    SELECT 
      al.id, 
      al.action, 
      al.details, 
      al.timestamp, 
      u.username as user_username,
      u.id as user_id
    FROM activity_logs al
    JOIN users u ON al.user_id = u.id
  `;
  
  const params = [];
  
  // Add filters if provided
  if (action || userId) {
    query += ' WHERE';
    
    if (action) {
      query += ' al.action = ?';
      params.push(action);
      
      if (userId) {
        query += ' AND';
      }
    }
    
    if (userId) {
      query += ' al.user_id = ?';
      params.push(userId);
    }
  }
  
  // Add order and pagination
  query += ' ORDER BY al.timestamp DESC LIMIT ? OFFSET ?';
  params.push(limit, offset);
  
  db.all(query, params, (err, logs) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
    
    res.json(logs);
  });
});

// Get activity log by ID (admin only)
router.get('/:id', authenticateToken, isAdmin, (req, res) => {
  const { id } = req.params;
  
  db.get(`
    SELECT 
      al.id, 
      al.action, 
      al.details, 
      al.timestamp, 
      u.username as user_username,
      u.id as user_id
    FROM activity_logs al
    JOIN users u ON al.user_id = u.id
    WHERE al.id = ?
  `, [id], (err, log) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
    
    if (!log) {
      return res.status(404).json({ message: 'Activity log not found' });
    }
    
    res.json(log);
  });
});

// Get activity logs for current user
router.get('/user/me', authenticateToken, (req, res) => {
  const { limit = 100, offset = 0 } = req.query;
  
  db.all(`
    SELECT 
      al.id, 
      al.action, 
      al.details, 
      al.timestamp
    FROM activity_logs al
    WHERE al.user_id = ?
    ORDER BY al.timestamp DESC
    LIMIT ? OFFSET ?
  `, [req.user.id, limit, offset], (err, logs) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
    
    res.json(logs);
  });
});

// Clear all logs (admin only)
router.delete('/clear', authenticateToken, isAdmin, (req, res) => {
  db.run('DELETE FROM activity_logs', function(err) {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }
    
    res.json({ message: 'All activity logs cleared successfully' });
  });
});

module.exports = router;
