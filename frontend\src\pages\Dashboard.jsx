import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FiUsers, FiFile, FiActivity, FiSearch } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import employeeFilesLogo from '../assets/employee-files-logo.png';

const Dashboard = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    employeeCount: 0,
    fileCount: 0,
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState({ files: [], employees: [] });
  const [searching, setSearching] = useState(false);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        console.log('Fetching dashboard stats...');

        // جلب النشاط الأخير أولاً
        let recentActivity = [];
        try {
          if (user.role === 'admin') {
            const logsResponse = await axios.get('/api/logs?limit=5');
            recentActivity = logsResponse.data;
            console.log('Admin logs fetched:', recentActivity);
          } else {
            const logsResponse = await axios.get('/api/logs/user/me?limit=5');
            recentActivity = logsResponse.data;
            console.log('User logs fetched:', recentActivity);
          }
        } catch (logsError) {
          console.error('Error fetching logs:', logsError);
        }

        // جلب إحصائيات النظام
        try {
          const statsResponse = await axios.get('/api/stats');
          console.log('Stats response:', statsResponse.data);

          if (statsResponse.data && (statsResponse.data.employeeCount !== undefined || statsResponse.data.fileCount !== undefined)) {
            setStats({
              employeeCount: statsResponse.data.employeeCount || 0,
              fileCount: statsResponse.data.fileCount || 0,
              recentActivity
            });
            console.log('Stats set from API:', statsResponse.data);
          } else {
            throw new Error('Invalid stats response');
          }
        } catch (statsError) {
          console.error('Error fetching stats from API:', statsError);

          // الطريقة البديلة إذا فشلت نقطة النهاية الرئيسية
          try {
            // جلب عدد الموظفين
            const employeesResponse = await axios.get('/api/employees');
            const employeeCount = employeesResponse.data.length;
            console.log('Employee count from fallback:', employeeCount);

            // جلب عدد الملفات
            let fileCount = 0;
            try {
              const filesCountResponse = await axios.get('/api/files/count');
              if (filesCountResponse.data && filesCountResponse.data.count !== undefined) {
                fileCount = filesCountResponse.data.count;
                console.log('File count from count API:', fileCount);
              } else {
                throw new Error('Invalid file count response');
              }
            } catch (fileCountError) {
              console.error('Error fetching file count:', fileCountError);

              // الطريقة الاحتياطية لحساب عدد الملفات
              for (const employee of employeesResponse.data) {
                try {
                  const filesResponse = await axios.get(`/api/files/employee/${employee.name}`);
                  fileCount += filesResponse.data.length;
                } catch (err) {
                  console.error(`Error fetching files for employee ${employee.name}:`, err);
                }
              }
              console.log('File count from manual count:', fileCount);
            }

            setStats({
              employeeCount,
              fileCount,
              recentActivity
            });
            console.log('Stats set from fallback method');
          } catch (fallbackError) {
            console.error('Error in fallback stats fetching:', fallbackError);
          }
        }
      } catch (error) {
        console.error('Error in main stats fetching:', error);
      } finally {
        setLoading(false);
        console.log('Stats loading completed');
      }
    };

    fetchStats();
  }, [user.role]);

  // تحديث الإحصائيات تلقائياً كل 30 ثانية
  useEffect(() => {
    const interval = setInterval(async () => {
      try {
        console.log('🔄 تحديث الإحصائيات التلقائي...');

        // جلب إحصائيات النظام مباشرة
        const statsResponse = await axios.get('/api/stats');
        console.log('📊 إحصائيات محدثة تلقائياً:', statsResponse.data);

        if (statsResponse.data) {
          setStats(prevStats => ({
            ...prevStats,
            employeeCount: statsResponse.data.employeeCount || 0,
            fileCount: statsResponse.data.fileCount || 0
          }));
          console.log('✅ تم تحديث الإحصائيات تلقائياً');
        }
      } catch (error) {
        console.error('❌ خطأ في التحديث التلقائي للإحصائيات:', error);
      }
    }, 30000); // كل 30 ثانية

    // تنظيف المؤقت عند إلغاء تحميل المكون
    return () => clearInterval(interval);
  }, []);

  // تحديث الإحصائيات عند العودة للصفحة (focus)
  useEffect(() => {
    const handleFocus = async () => {
      try {
        console.log('🔄 تحديث الإحصائيات عند العودة للصفحة...');

        const statsResponse = await axios.get('/api/stats');
        if (statsResponse.data) {
          setStats(prevStats => ({
            ...prevStats,
            employeeCount: statsResponse.data.employeeCount || 0,
            fileCount: statsResponse.data.fileCount || 0
          }));
          console.log('✅ تم تحديث الإحصائيات عند العودة للصفحة');
        }
      } catch (error) {
        console.error('❌ خطأ في تحديث الإحصائيات عند العودة:', error);
      }
    };

    // إضافة مستمع للأحداث
    window.addEventListener('focus', handleFocus);
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        handleFocus();
      }
    });

    // تنظيف المستمعين
    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('visibilitychange', handleFocus);
    };
  }, []);



  // Effect for auto search when typing
  useEffect(() => {
    // Debounce function to prevent too many API calls
    const debounceTimeout = setTimeout(() => {
      if (searchQuery.trim()) {
        performSearch();
      } else {
        // Clear results if search query is empty
        setSearchResults({ files: [], employees: [] });
      }
    }, 300); // Wait 300ms after typing stops before searching

    return () => clearTimeout(debounceTimeout); // Cleanup timeout
  }, [searchQuery]);

  // Function to perform search - used by both auto-search and manual search
  const performSearch = async () => {
    if (!searchQuery.trim()) return;

    try {
      setSearching(true);

      // Search for files
      const filesResponse = await axios.get(`/api/files/search?query=${searchQuery}`);

      // Search for employees (by checking if any employee name contains the search query)
      const employeesResponse = await axios.get('/api/employees');
      const matchingEmployees = employeesResponse.data.filter(
        employee => employee.name.toLowerCase().includes(searchQuery.toLowerCase())
      );

      // Combine results
      setSearchResults({
        files: filesResponse.data,
        employees: matchingEmployees
      });
    } catch (error) {
      console.error('Error searching:', error);
    } finally {
      setSearching(false);
    }
  };

  // Handle manual search form submission
  const handleSearch = async (e) => {
    e.preventDefault();
    performSearch();
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getActionText = (action) => {
    switch (action) {
      case 'login':
        return 'تسجيل دخول';
      case 'view_employee':
        return 'عرض موظف';
      case 'view_file':
        return 'عرض ملف';
      case 'upload_file':
        return 'رفع ملف';
      case 'delete_file':
        return 'حذف ملف';
      case 'create_employee':
        return 'إنشاء موظف';
      case 'delete_employee':
        return 'حذف موظف';
      case 'create_user':
        return 'إنشاء مستخدم';
      case 'update_user':
        return 'تحديث مستخدم';
      case 'delete_user':
        return 'حذف مستخدم';
      case 'create_backup':
        return 'إنشاء نسخة احتياطية';
      case 'restore_backup':
        return 'استعادة نسخة احتياطية';
      default:
        return action;
    }
  };

  return (
    <div>
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">لوحة التحكم</h1>
              <p className="text-orange-500 font-medium">مرحباً بك في نظام ارشفة ملفات الموظفين</p>
            </div>
            <div className="hidden md:block">
              <img src={employeeFilesLogo} alt="نظام ارشفة ملفات الموظفين" className="w-24 h-24" />
            </div>
          </div>
        </div>
      </div>

      {/* Search */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiSearch className="w-5 h-5 text-primary-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">البحث السريع</h2>
          </div>
        </div>
        <div className="p-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FiSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
              placeholder="ابحث عن موظف أو ملف..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              autoFocus
            />
            {searching && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <div className="animate-spin h-4 w-4 border-2 border-primary-500 rounded-full border-t-transparent"></div>
              </div>
            )}
            {searchQuery && !searching && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <button
                  onClick={() => setSearchQuery('')}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  aria-label="مسح البحث"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
          </div>

          {/* Search Results */}
          {searchQuery.trim() && (
            <div className="mt-6 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden animate-fade-in">
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h3 className="text-md font-semibold text-gray-700">نتائج البحث</h3>
                  {(searchResults.employees.length > 0 || searchResults.files.length > 0) && (
                    <span className="text-xs bg-primary-100 text-primary-800 py-1 px-2 rounded-full shadow-sm">
                      {searchResults.employees.length + searchResults.files.length} نتيجة
                    </span>
                  )}
                </div>
              </div>

              <div className="p-4">
                {searchResults.employees.length === 0 && searchResults.files.length === 0 ? (
                  searching ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <div className="animate-spin h-10 w-10 border-3 border-primary-500 rounded-full border-t-transparent mb-4"></div>
                      <p className="text-gray-500 font-medium">جاري البحث...</p>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                      <div className="bg-gray-100 p-4 rounded-full mb-3 shadow-inner">
                        <FiSearch className="w-8 h-8 text-gray-400" />
                      </div>
                      <p className="text-gray-600 mb-1 font-medium">لا توجد نتائج مطابقة</p>
                      <p className="text-gray-500 text-sm">حاول استخدام كلمات بحث مختلفة</p>
                    </div>
                  )
                ) : (
                  <div className="space-y-6">
                    {searchResults.employees.length > 0 && (
                      <div>
                        <div className="flex items-center mb-3">
                          <div className="p-2 rounded-full bg-blue-100 text-blue-600 ml-2 shadow-sm">
                            <FiUsers className="w-5 h-5" />
                          </div>
                          <h4 className="text-sm font-medium text-gray-700">الموظفين ({searchResults.employees.length})</h4>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {searchResults.employees.map((employee) => (
                            <Link
                              key={employee.name}
                              to={`/employees/${employee.name}`}
                              className="flex items-center p-3 rounded-lg border border-gray-200 hover:bg-blue-50 hover:border-blue-200 transition-all transform hover:-translate-y-1 hover:shadow-md"
                            >
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold ml-3 shadow-sm">
                                {employee.name.charAt(0)}
                              </div>
                              <div>
                                <div className="font-medium text-gray-800">{employee.name}</div>
                                <div className="text-xs text-gray-500 flex items-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                                  </svg>
                                  عرض ملفات الموظف
                                </div>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}

                    {searchResults.files.length > 0 && (
                      <div>
                        <div className="flex items-center mb-3">
                          <div className="p-2 rounded-full bg-green-100 text-green-600 ml-2 shadow-sm">
                            <FiFile className="w-5 h-5" />
                          </div>
                          <h4 className="text-sm font-medium text-gray-700">الملفات ({searchResults.files.length})</h4>
                        </div>
                        <div className="bg-gray-50 rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                          {searchResults.files.map((file, index) => (
                            <Link
                              key={`${file.employee}-${file.name}`}
                              to={`/employees/${file.employee}?file=${encodeURIComponent(file.name)}`}
                              className={`flex items-center p-3 hover:bg-green-50 transition-all ${
                                index !== searchResults.files.length - 1 ? 'border-b border-gray-200' : ''
                              }`}
                            >
                              <div className="p-2 rounded bg-white border border-gray-200 ml-3 shadow-sm">
                                <FiFile className="w-4 h-4 text-green-500" />
                              </div>
                              <div className="flex-1">
                                <div className="font-medium text-gray-800">{file.name}</div>
                                <div className="text-xs text-gray-500 flex items-center">
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 ml-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                  </svg>
                                  الموظف: {file.employee}
                                </div>
                              </div>
                              <div className="bg-primary-100 text-primary-700 text-sm py-1 px-3 rounded-full hover:bg-primary-200 transition-colors">عرض</div>
                            </Link>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-800">إحصائيات النظام</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="card relative overflow-hidden transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
          <div className="absolute top-0 left-0 w-32 h-32 bg-blue-50 rounded-full -ml-16 -mt-16 opacity-50"></div>
          <div className="absolute bottom-0 right-0 w-16 h-16 bg-blue-50 rounded-full -mr-8 -mb-8 opacity-50"></div>
          <div className="relative">
            <div className="flex items-center">
              <div className="p-4 rounded-lg bg-gradient-to-br from-blue-400 to-blue-600 text-white shadow-md">
                <FiUsers className="w-7 h-7" />
              </div>
              <div className="mr-4">
                <h2 className="text-lg font-semibold text-gray-700">الموظفين</h2>
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <p className="text-3xl font-bold text-gray-800">{stats.employeeCount}</p>
                    <div className="mr-2 w-2 h-2 bg-blue-400 rounded-full animate-pulse" title="تحديث تلقائي كل 30 ثانية"></div>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-6 pt-4 border-t border-gray-100">
              <Link
                to="/employees"
                className="flex items-center text-sm text-primary-600 hover:text-primary-700 transition-colors"
              >
                <span>عرض جميع الموظفين</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            </div>
          </div>
        </div>

        <div className="card relative overflow-hidden transform transition-all duration-300 hover:shadow-lg hover:-translate-y-1">
          <div className="absolute top-0 left-0 w-32 h-32 bg-green-50 rounded-full -ml-16 -mt-16 opacity-50"></div>
          <div className="absolute bottom-0 right-0 w-16 h-16 bg-green-50 rounded-full -mr-8 -mb-8 opacity-50"></div>
          <div className="relative">
            <div className="flex items-center">
              <div className="p-4 rounded-lg bg-gradient-to-br from-green-400 to-green-600 text-white shadow-md">
                <FiFile className="w-7 h-7" />
              </div>
              <div className="mr-4">
                <h2 className="text-lg font-semibold text-gray-700">الملفات</h2>
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <p className="text-3xl font-bold text-gray-800">{stats.fileCount}</p>
                    <div className="mr-2 w-2 h-2 bg-green-400 rounded-full animate-pulse" title="تحديث تلقائي كل 30 ثانية"></div>
                  </div>
                )}
              </div>
            </div>
            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center text-sm text-green-600">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>إجمالي الملفات في النظام</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="card relative overflow-hidden transform transition-all duration-300 hover:shadow-lg">
        <div className="absolute top-0 left-0 w-40 h-40 bg-indigo-50 rounded-full -ml-20 -mt-20 opacity-30"></div>
        <div className="absolute bottom-0 right-0 w-32 h-32 bg-primary-50 rounded-full -mr-16 -mb-16 opacity-30"></div>

        <div className="relative">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <div className="p-2 rounded-lg bg-gradient-to-r from-indigo-400 to-purple-500 text-white shadow-md ml-3">
                <FiActivity className="w-5 h-5" />
              </div>
              <h2 className="text-lg font-semibold text-gray-800">النشاط الأخير</h2>
            </div>
            {user.role === 'admin' && (
              <Link
                to="/logs"
                className="flex items-center text-sm text-primary-600 hover:text-primary-700 transition-colors bg-primary-50 hover:bg-primary-100 px-3 py-1 rounded-full"
              >
                <span>عرض الكل</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </Link>
            )}
          </div>

          {loading ? (
            <div className="flex flex-col items-center justify-center py-8">
              <div className="animate-spin h-10 w-10 border-3 border-primary-500 rounded-full border-t-transparent mb-4"></div>
              <p className="text-gray-500 font-medium">جاري تحميل النشاط...</p>
            </div>
          ) : stats.recentActivity.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <div className="bg-gray-100 p-4 rounded-full mb-3 shadow-inner">
                <FiActivity className="w-8 h-8 text-gray-400" />
              </div>
              <p className="text-gray-600 mb-1 font-medium">لا يوجد نشاط حديث</p>
              <p className="text-gray-500 text-sm">ستظهر هنا آخر الأنشطة في النظام</p>
            </div>
          ) : (
            <ul className="space-y-4">
              {stats.recentActivity.map((activity) => (
                <li key={activity.id} className="flex items-start p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="w-10 h-10 rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-white font-bold shadow-sm ml-3">
                    {activity.user_username?.charAt(0).toUpperCase() || '?'}
                  </div>
                  <div className="mr-1 flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-800">
                        {activity.user_username && (
                          <span className="font-semibold text-primary-700">{activity.user_username}</span>
                        )}
                      </p>
                      <p className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">{formatDate(activity.timestamp)}</p>
                    </div>
                    <div className="mt-1 flex items-center">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 ml-2">
                        {getActionText(activity.action)}
                      </span>
                      {activity.details && (
                        <span className="text-sm text-gray-600 truncate">{activity.details}</span>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
