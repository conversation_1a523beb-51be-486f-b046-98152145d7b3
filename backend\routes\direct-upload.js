const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { authenticateToken, isEditor } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');
const { uploadFileToEmployees } = require('../scripts/direct-upload');

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const tempDir = path.join(__dirname, '../temp');
    fs.ensureDirSync(tempDir);
    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // Keep original filename as is - multer already handles UTF-8 encoding correctly
    // Just log the filename for debugging
    console.log(`Processing file with original name: ${file.originalname}`);
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Direct upload endpoint
router.post('/', authenticateToken, isEditor, upload.single('file'), async (req, res) => {
  try {
    const file = req.file;
    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    console.log('File uploaded to temp directory:', file);

    // Get employees from request body
    let employees = [];

    // Try to parse employees from different sources
    if (req.body.employeesJson) {
      try {
        employees = JSON.parse(req.body.employeesJson);
      } catch (e) {
        console.error('Error parsing employeesJson:', e);
      }
    } else if (req.body.employees) {
      if (Array.isArray(req.body.employees)) {
        employees = req.body.employees;
      } else if (typeof req.body.employees === 'string') {
        try {
          employees = JSON.parse(req.body.employees);
        } catch (e) {
          employees = [req.body.employees];
        }
      }
    } else {
      // Try to find employees in form data format
      employees = [];
      let i = 0;
      while (req.body[`employees[${i}]`] !== undefined) {
        employees.push(req.body[`employees[${i}]`]);
        i++;
      }
    }

    console.log('Employees to process:', employees);

    if (!employees || !Array.isArray(employees) || employees.length === 0) {
      // Clean up the uploaded file
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      return res.status(400).json({ message: 'No employees specified' });
    }

    // Use the direct upload function
    const result = await uploadFileToEmployees(file.path, employees, req.user.id);

    // Clean up the uploaded file
    try {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
        console.log('Temporary file deleted');
      }
    } catch (err) {
      console.error('Error deleting temporary file:', err);
    }

    // Log activity
    logActivity(req.user.id, 'bulk_upload_file', `Uploaded file: ${file.originalname} to ${result.successCount} employees`);

    // تحديد الرسالة بناءً على وجود ملفات مكررة
    let message = 'File uploaded successfully';
    if (result.existingFilesCount > 0) {
      if (result.existingFilesCount === employees.length) {
        message = 'All files already exist for the selected employees';
      } else {
        message = `File uploaded successfully to ${result.successCount} employees. File already exists for ${result.existingFilesCount} employees.`;
      }
    }

    res.status(200).json({
      message,
      file: file.originalname,
      employeesProcessed: result.successCount,
      totalEmployees: employees.length,
      existingFilesCount: result.existingFilesCount,
      existingFiles: result.existingFiles,
      ...result
    });
  } catch (err) {
    console.error('Error in direct upload:', err);
    res.status(500).json({
      message: 'Error uploading file',
      error: err.message
    });
  }
});

module.exports = router;
