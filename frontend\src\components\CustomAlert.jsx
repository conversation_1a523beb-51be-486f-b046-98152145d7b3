import React from 'react';
import { FiAlertTriangle, FiX } from 'react-icons/fi';

const CustomAlert = ({ message, onClose }) => {
  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity">
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

        <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-gradient-to-r from-blue-50 to-white p-6 rounded-t-lg">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-yellow-100 flex items-center justify-center ml-3">
                <FiAlertTriangle className="text-yellow-600 text-xl" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-800">
                  تنبيه
                </h3>
                <p className="text-sm text-gray-500">
                  يرجى قراءة الرسالة التالية
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6">
            <div className="text-center mb-4">
              <p className="text-lg text-gray-700">{message}</p>
            </div>
          </div>

          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              موافق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomAlert;
