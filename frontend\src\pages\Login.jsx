import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  FiUser,
  FiLock,
  FiAlertCircle,
  FiLogIn,
  FiX,
  FiCheck
} from 'react-icons/fi';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const { login, loading } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    // تمرير حالة "تذكرني" إلى دالة تسجيل الدخول
    const success = await login(username, password, rememberMe);

    if (success) {
      navigate('/dashboard');
    }
  };

  return (
    <div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500">
            <FiAlertCircle className="text-red-500 text-xl ml-2 flex-shrink-0" />
            <div className="flex-grow">{error}</div>
            <button
              onClick={() => setError('')}
              className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
              type="button"
            >
              <FiX />
            </button>
          </div>
        )}

        <div>
          <label htmlFor="username" className="block mb-2 text-sm font-medium text-gray-700">
            اسم المستخدم
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FiUser className="text-gray-400" />
            </div>
            <input
              type="text"
              id="username"
              className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
              placeholder="أدخل اسم المستخدم"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              disabled={loading}
              autoFocus
            />
          </div>
        </div>

        <div>
          <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-700">
            كلمة المرور
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FiLock className="text-gray-400" />
            </div>
            <input
              type="password"
              id="password"
              className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
              placeholder="أدخل كلمة المرور"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={loading}
            />
          </div>
        </div>

        {/* خيار تذكرني */}
        <div className="flex items-center mt-2">
          <label className="flex items-center cursor-pointer">
            <div className="relative">
              <input
                type="checkbox"
                className="sr-only"
                checked={rememberMe}
                onChange={() => setRememberMe(!rememberMe)}
              />
              <div className={`block w-5 h-5 rounded border ${rememberMe ? 'bg-primary-600 border-primary-600' : 'bg-white border-gray-300'} transition-colors`}></div>
              {rememberMe && (
                <FiCheck className="text-white absolute top-0.5 left-0.5 w-4 h-4" />
              )}
            </div>
            <span className="mr-2 text-sm text-gray-700">تذكرني</span>
          </label>
        </div>

        <button
          type="submit"
          className={`
            w-full flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
            transition-all duration-300 shadow-md hover:shadow-lg mt-6
            ${loading
              ? 'bg-gray-400 cursor-not-allowed opacity-70'
              : 'bg-primary-600 hover:bg-primary-700'
            }
          `}
          disabled={loading}
        >
          {loading ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              جاري تسجيل الدخول...
            </>
          ) : (
            <>
              <FiLogIn className="ml-2" />
              تسجيل الدخول
            </>
          )}
        </button>
      </form>
    </div>
  );
};

export default Login;
