import { useState, useEffect, useRef } from 'react';
import axios from 'axios';
import {
  FiUser,
  FiLock,
  FiAlertCircle,
  FiCheck,
  FiX,
  FiInfo,
  FiCheckCircle,
  FiShield
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Profile = () => {
  const { user, verifyToken } = useAuth();
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // استخدام useRef للتحكم في مؤقت إخفاء رسالة النجاح
  const successTimerRef = useRef(null);

  // إظهار رسالة ترحيب عند تحميل الصفحة
  useEffect(() => {
    console.log('Profile component mounted');
  }, []);

  // إظهار رسالة النجاح
  const showSuccess = (message) => {
    // إلغاء المؤقت السابق إذا كان موجودًا
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
    }

    // تعيين رسالة النجاح وإظهارها
    setSuccessMessage(message);
    setShowSuccessMessage(true);
    console.log('Showing success message:', message);

    // إعداد مؤقت لإخفاء رسالة النجاح بعد 5 ثوانٍ
    successTimerRef.current = setTimeout(() => {
      setShowSuccessMessage(false);
      console.log('Auto-hiding success message');
    }, 5000);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Clear messages when user starts typing
    setError(null);

    // إلغاء المؤقت السابق إذا كان موجودًا وإخفاء رسالة النجاح
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
      successTimerRef.current = null;
    }
    setShowSuccessMessage(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate passwords
    if (formData.newPassword !== formData.confirmPassword) {
      setError('كلمة المرور الجديدة وتأكيدها غير متطابقين');
      return;
    }

    if (formData.newPassword.length < 6) {
      setError('كلمة المرور الجديدة يجب أن تكون 6 أحرف على الأقل');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // إلغاء المؤقت السابق إذا كان موجودًا وإخفاء رسالة النجاح
      if (successTimerRef.current) {
        clearTimeout(successTimerRef.current);
        successTimerRef.current = null;
      }
      setShowSuccessMessage(false);

      console.log('Sending password change request...');

      // Call API to change password
      const response = await axios.post('/api/auth/change-password', {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      });

      console.log('Password change response:', response);

      // Reset form
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      // Show success message with a slight delay to ensure state updates
      setTimeout(() => {
        showSuccess('تم تغيير كلمة المرور بنجاح');
      }, 100);

      // Verify token to refresh user data
      verifyToken();
    } catch (err) {
      console.error('Error changing password:', err);
      if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else {
        setError('حدث خطأ أثناء تغيير كلمة المرور');
      }
    } finally {
      setLoading(false);
    }
  };

  const getRoleText = (role) => {
    switch (role) {
      case 'admin':
        return 'مدير';
      case 'editor':
        return 'محرر';
      case 'viewer':
        return 'مشاهد';
      default:
        return role;
    }
  };

  return (
    <div>
      {/* Header Section */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">الملف الشخصي</h1>
              <p className="text-gray-600">إدارة معلومات حسابك وتغيير كلمة المرور</p>
            </div>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {showSuccessMessage && (
        <div
          className="mb-4 bg-green-50 border border-green-200 text-green-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in"
        >
          <FiCheckCircle className="text-green-500 text-xl ml-2 flex-shrink-0" />
          <div className="flex-grow">{successMessage}</div>
          <button
            onClick={() => {
              if (successTimerRef.current) {
                clearTimeout(successTimerRef.current);
                successTimerRef.current = null;
              }
              setShowSuccessMessage(false);
            }}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* User Info */}
        <div className="md:col-span-1">
          <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
            <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
              <div className="flex items-center">
                <div className="p-2 bg-white rounded-full shadow-sm ml-3">
                  <FiUser className="w-5 h-5 text-primary-600" />
                </div>
                <h2 className="text-lg font-semibold text-gray-800">معلومات المستخدم</h2>
              </div>
            </div>

            <div className="p-6 bg-white rounded-b-lg">
              <div className="flex flex-col items-center">
                <div className="w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center text-white text-3xl font-bold mb-4 shadow-lg">
                  {user?.username.charAt(0).toUpperCase()}
                </div>

                <h2 className="text-xl font-semibold text-gray-800">{user?.username}</h2>

                <span className={`px-3 py-1 mt-2 inline-flex text-sm leading-5 font-semibold rounded-full ${
                  user?.role === 'admin'
                    ? 'bg-purple-100 text-purple-800'
                    : user?.role === 'editor'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-blue-100 text-blue-800'
                }`}>
                  {getRoleText(user?.role)}
                </span>

                <div className="mt-6 w-full">
                  <div className="border-t border-gray-200 pt-4">
                    <dl className="divide-y divide-gray-200">
                      <div className="py-3 flex justify-between items-center">
                        <dt className="text-sm font-medium text-gray-500">اسم المستخدم</dt>
                        <dd className="text-sm font-medium text-gray-900 bg-gray-50 px-3 py-1 rounded-lg">{user?.username}</dd>
                      </div>
                      <div className="py-3 flex justify-between items-center">
                        <dt className="text-sm font-medium text-gray-500">الصلاحية</dt>
                        <dd className="text-sm font-medium text-gray-900 bg-gray-50 px-3 py-1 rounded-lg">{getRoleText(user?.role)}</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Change Password */}
        <div className="md:col-span-2">
          <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
              <div className="flex items-center">
                <div className="p-2 bg-white rounded-full shadow-sm ml-3">
                  <FiLock className="w-5 h-5 text-blue-600" />
                </div>
                <h2 className="text-lg font-semibold text-gray-800">تغيير كلمة المرور</h2>
              </div>
            </div>

            <div className="p-6 bg-white rounded-b-lg">
              {error && (
                <div className="mb-4 bg-red-50 border border-red-200 text-red-800 rounded-lg shadow-md p-3 flex items-center transition-all duration-500">
                  <FiAlertCircle className="text-red-500 text-xl ml-2 flex-shrink-0" />
                  <div className="flex-grow">{error}</div>
                  <button
                    onClick={() => setError(null)}
                    className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
                  >
                    <FiX />
                  </button>
                </div>
              )}

              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="currentPassword" className="block mb-2 text-sm font-medium text-gray-700">
                    كلمة المرور الحالية
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiLock className="text-gray-400" />
                    </div>
                    <input
                      type="password"
                      id="currentPassword"
                      name="currentPassword"
                      className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
                      placeholder="أدخل كلمة المرور الحالية"
                      value={formData.currentPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label htmlFor="newPassword" className="block mb-2 text-sm font-medium text-gray-700">
                    كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiLock className="text-gray-400" />
                    </div>
                    <input
                      type="password"
                      id="newPassword"
                      name="newPassword"
                      className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
                      placeholder="أدخل كلمة المرور الجديدة"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <p className="mt-1 text-xs text-gray-500">
                    كلمة المرور يجب أن تكون 6 أحرف على الأقل
                  </p>
                </div>

                <div className="mb-6">
                  <label htmlFor="confirmPassword" className="block mb-2 text-sm font-medium text-gray-700">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <FiLock className="text-gray-400" />
                    </div>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
                      placeholder="أدخل تأكيد كلمة المرور الجديدة"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>

                <div className="card mb-6 transform transition-all duration-300 hover:shadow-sm">
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-t-lg border-b border-blue-200">
                    <div className="flex items-center">
                      <div className="p-1.5 bg-white rounded-full shadow-sm ml-2">
                        <FiInfo className="w-4 h-4 text-blue-600" />
                      </div>
                      <h3 className="text-sm font-semibold text-gray-800">نصائح لكلمة مرور قوية</h3>
                    </div>
                  </div>
                  <div className="p-3 bg-white rounded-b-lg">
                    <ul className="text-sm text-gray-600 list-disc list-inside mr-2 space-y-1">
                      <li>استخدم 8 أحرف على الأقل</li>
                      <li>استخدم مزيجًا من الأحرف الكبيرة والصغيرة</li>
                      <li>أضف أرقامًا ورموزًا خاصة</li>
                    </ul>
                  </div>
                </div>

                <button
                  type="submit"
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md hover:shadow-lg
                    ${loading
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-primary-600 hover:bg-primary-700'
                    }
                  `}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      جاري الحفظ...
                    </>
                  ) : (
                    <>
                      <FiShield className="ml-2" />
                      حفظ التغييرات
                    </>
                  )}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
