import { Outlet, Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Footer from '../components/Footer';
import { FiArchive } from 'react-icons/fi';
import employeeFilesLogo from '../assets/employee-files-logo.png';

const AuthLayout = () => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50">
        <div className="w-16 h-16 mb-4 bg-primary-600 rounded-full flex items-center justify-center text-white shadow-lg">
          <FiArchive className="w-8 h-8" />
        </div>
        <div className="animate-spin h-12 w-12 border-4 border-primary-500 rounded-full border-t-transparent mb-4"></div>
        <p className="text-gray-600 font-medium">جاري تحميل النظام...</p>
      </div>
    );
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" />;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="flex-grow flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-xl shadow-lg overflow-hidden transform transition-all duration-300 hover:shadow-xl">
          <div className="relative">
            <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-30"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-30"></div>

            <div className="relative p-8">
              <div className="text-center mb-6">
                <div className="flex items-center justify-center mb-4">
                  <img src={employeeFilesLogo} alt="نظام ارشفة ملفات الموظفين" className="w-24 h-24" />
                </div>
                <h1 className="text-3xl font-bold text-orange-500 mb-2">نظام ارشفة ملفات الموظفين</h1>
                <p className="text-gray-600">قم بتسجيل الدخول للوصول إلى النظام</p>
              </div>

              <Outlet />
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AuthLayout;
