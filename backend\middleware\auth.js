const jwt = require('jsonwebtoken');
const { db } = require('../db/database');

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret_key';

// Middleware to authenticate JWT token
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers.authorization;

  console.log('Auth middleware - Authorization header:', authHeader);

  if (!authHeader) {
    console.log('Auth middleware - No authorization header found');
    return res.status(401).json({ message: 'Authorization required' });
  }

  const token = authHeader.split(' ')[1];
  console.log('Auth middleware - Extracted token:', token ? 'Token present' : 'No token');

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('Auth middleware - Token verified successfully for user:', decoded.id);

    // Check if user still exists in database
    db.get('SELECT * FROM users WHERE id = ?', [decoded.id], (err, user) => {
      if (err) {
        console.error('Auth middleware - Database error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      if (!user) {
        console.log('Auth middleware - User no longer exists:', decoded.id);
        return res.status(401).json({ message: 'User no longer exists' });
      }

      console.log('Auth middleware - User found:', user.username, 'Role:', user.role);

      // Attach user info to request
      req.user = {
        id: user.id,
        username: user.username,
        role: user.role
      };

      console.log('Auth middleware - Authentication successful, proceeding to next middleware');
      next();
    });
  } catch (err) {
    console.error('Auth middleware - Token verification error:', err);
    return res.status(401).json({ message: 'Invalid token' });
  }
};

// Middleware to check if user has admin role
const isAdmin = (req, res, next) => {
  if (req.user && req.user.role === 'admin') {
    next();
  } else {
    res.status(403).json({ message: 'Admin privileges required' });
  }
};

// Middleware to check if user has editor role or higher
const isEditor = (req, res, next) => {
  console.log('isEditor middleware - Checking user role:', req.user ? req.user.role : 'No user');
  if (req.user && (req.user.role === 'admin' || req.user.role === 'editor')) {
    console.log('isEditor middleware - User has editor privileges');
    next();
  } else {
    console.log('isEditor middleware - User does not have editor privileges');
    res.status(403).json({ message: 'Editor privileges required' });
  }
};

module.exports = {
  authenticateToken,
  isAdmin,
  isEditor
};
