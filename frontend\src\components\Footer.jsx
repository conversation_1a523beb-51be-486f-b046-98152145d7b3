import React from 'react';
import { FiHeart } from 'react-icons/fi';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 py-4 mt-auto shadow-inner">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-600 text-sm mb-3 md:mb-0 flex items-center">
            <div className="bg-primary-50 p-1.5 rounded-full shadow-sm mr-2">
              <div className="bg-primary-100 p-1 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary-600" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M6.625 2.655A9 9 0 0119 11a1 1 0 11-2 0 7 7 0 00-9.625-6.492 1 1 0 11-.75-1.853zM4.662 4.959A1 1 0 014.75 6.37 6.97 6.97 0 003 11a1 1 0 11-2 0 8.97 8.97 0 012.25-5.953 1 1 0 011.412-.088z" clipRule="evenodd" />
                  <path fillRule="evenodd" d="M5 11a5 5 0 1110 0 1 1 0 11-2 0 3 3 0 10-6 0c0 1.677-.345 3.276-.968 4.729a1 1 0 11-1.838-.789A9.964 9.964 0 005 11z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          <span className="text-gray-600 font-medium">  نظام ارشفة ملفات الموظفين، جميع الحقوق محفوظة &copy; {currentYear}</span>
          </div>
          <div className="text-gray-600 text-sm flex items-center">
            <span className="ml-1">تم التطوير بواسطة</span>
            <div className="flex items-center bg-primary-50 px-2 py-1 rounded-full shadow-sm mx-1">
              <FiHeart className="text-red-500 mr-1 h-3 w-3" />
              <a
                href="http://www.fb.com/prog.hamid"
                target="_blank"
                rel="noopener noreferrer"
                className="font-semibold text-primary-600 hover:text-primary-700 transition-colors"
              >
                Hamid Al Fahad
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
