{"name": "employee-archive-system-backend", "version": "1.0.0", "description": "Backend for Employee Archive System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"archiver": "^5.3.1", "bcrypt": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "extract-zip": "^2.0.1", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.0", "multer": "^1.4.5-lts.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^2.0.22"}}