===========================================================
            نظام أرشفة ملفات الموظفين - دليل سريع
===========================================================

طريقة نقل النظام إلى حاسوب آخر
----------------------------

1. انسخ مجلد "employee-archive-system" بالكامل إلى الحاسوب الجديد.

2. قم بتشغيل ملف "setup.bat" بالنقر المزدوج عليه.
   - سيقوم هذا الملف بتثبيت جميع المتطلبات اللازمة تلقائياً.
   - سيتم إنشاء اختصار على سطح المكتب للنظام.

3. بعد اكتمال عملية التثبيت، يمكنك تشغيل النظام بإحدى الطرق التالية:
   - النقر على الاختصار الموجود على سطح المكتب.
   - النقر المزدوج على ملف "start.bat" في مجلد النظام.

بيانات الدخول الافتراضية
---------------------
- اسم المستخدم: admin
- كلمة المرور: admin123

نقل البيانات
---------
لنقل بيانات النظام من الحاسوب القديم إلى الحاسوب الجديد:

1. قم بعمل نسخة احتياطية من النظام القديم:
   - افتح النظام على الحاسوب القديم
   - انتقل إلى صفحة "النسخ الاحتياطي"
   - انقر على زر "إنشاء نسخة احتياطية جديدة"
   - احفظ ملف النسخة الاحتياطية (ملف .zip)

2. انقل ملف النسخة الاحتياطية إلى الحاسوب الجديد.

3. قم باستعادة النسخة الاحتياطية على النظام الجديد:
   - افتح النظام على الحاسوب الجديد
   - انتقل إلى صفحة "النسخ الاحتياطي"
   - انقر على زر "استعادة من نسخة احتياطية"
   - اختر ملف النسخة الاحتياطية الذي نقلته

حل المشكلات الشائعة
----------------

1. النظام لا يعمل بعد التثبيت:
   - تأكد من تثبيت Node.js بشكل صحيح
   - تأكد من وجود اتصال بالإنترنت أثناء عملية التثبيت الأولى
   - جرب تشغيل ملف "setup.bat" مرة أخرى بصلاحيات المسؤول

2. رسالة خطأ "لا يمكن الاتصال بالخادم":
   - تأكد من تشغيل الخادم الخلفي
   - أعد تشغيل النظام بالكامل باستخدام ملف "start.bat"

3. مشكلة في تسجيل الدخول:
   - استخدم بيانات الدخول الافتراضية:
     * اسم المستخدم: admin
     * كلمة المرور: admin123

4. ظهور رموز غريبة في نافذة موجه الأوامر (CMD):
   - هذه مشكلة طبيعية تتعلق بعدم دعم موجه الأوامر للغة العربية بشكل صحيح
   - لا تقلق، فهذا لا يؤثر على عمل النظام
   - واجهة المستخدم الرسومية في المتصفح ستظهر باللغة العربية بشكل صحيح

للمساعدة والدعم الفني
------------------
للحصول على المساعدة أو الإبلاغ عن مشكلة، يرجى التواصل مع:
حامد الفهد
http://www.fb.com/prog.hamid

===========================================================
