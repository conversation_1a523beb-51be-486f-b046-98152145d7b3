====================================================
            EMPLOYEE ARCHIVE SYSTEM
====================================================

SIMPLIFIED INSTRUCTIONS:

1. Make sure Node.js is installed on your computer
   - Download and install Node.js from the official website: https://nodejs.org/
   - Choose the LTS (Long Term Support) version
   - Make sure to enable the option to add Node.js to PATH during installation

2. To run the system:
   - Double-click on the "start-system-en.bat" file
   - Follow the instructions on the screen
   - When asked about creating sample data, type "Y" and press Enter

3. Accessing the system:
   - Open your browser and go to: http://localhost:5173
   - Use the default login credentials:
     * Username: admin
     * Password: admin123

4. Stopping the system:
   - Close the open windows for the backend and frontend servers

====================================================
            TROUBLESHOOTING
====================================================

1. If you see the message "npm is not recognized as an internal or external command":
   - Make sure Node.js is properly installed
   - Make sure Node.js is added to the PATH environment variable
   - Restart your computer and try again

2. If the system cannot connect to the backend server:
   - Make sure the backend server is running (there should be an open window for the backend server)
   - Make sure port 5000 is not being used by another application

3. If you have problems installing dependencies:
   - Try running Command Prompt as Administrator
   - Make sure you have an internet connection

====================================================
            MANUAL STARTUP
====================================================

If you want to run the system manually, follow these steps:

1. Open Command Prompt

2. Navigate to the project folder:
   cd path_to_folder\employee-archive-system

3. Install dependencies:
   cd backend
   npm install
   cd ..\frontend
   npm install
   cd ..

4. Create sample data (optional):
   cd backend
   node scripts/init-sample-data.js
   cd ..

5. Start the backend server (in a Command Prompt window):
   cd backend
   npm run dev

6. Start the frontend (in another Command Prompt window):
   cd frontend
   npm run dev

7. Open your browser and go to: http://localhost:5173
