const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { db } = require('../db/database');
const { authenticateToken, isEditor } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

const router = express.Router();

// Base directory for employee data
const dataDir = path.join(__dirname, '../data', 'employee_database');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Store files in a temp directory
    const tempDir = path.join(__dirname, '../temp');
    console.log('Creating temp directory:', tempDir);

    // Ensure the temp directory exists
    try {
      fs.ensureDirSync(tempDir);
      console.log('Temp directory created/exists:', fs.existsSync(tempDir));
      cb(null, tempDir);
    } catch (err) {
      console.error('Error creating temp directory:', err);
      cb(err);
    }
  },
  filename: (req, file, cb) => {
    // Keep original filename as is - multer already handles UTF-8 encoding correctly
    // Just log the filename for debugging
    console.log(`Processing file with original name: ${file.originalname}`);
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 50 * 1024 * 1024 }, // 50MB limit
});

// Simplified bulk upload endpoint
router.post('/', authenticateToken, isEditor, upload.single('file'), async (req, res) => {
  try {
    const file = req.file;
    if (!file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Get employees from request body
    let employees = [];

    // Try to parse employees from different sources
    if (req.body.employeesJson) {
      try {
        employees = JSON.parse(req.body.employeesJson);
      } catch (e) {
        console.error('Error parsing employeesJson:', e);
      }
    } else if (req.body.employees) {
      if (Array.isArray(req.body.employees)) {
        employees = req.body.employees;
      } else if (typeof req.body.employees === 'string') {
        try {
          employees = JSON.parse(req.body.employees);
        } catch (e) {
          employees = [req.body.employees];
        }
      }
    } else {
      // Try to find employees in form data format
      employees = [];
      let i = 0;
      while (req.body[`employees[${i}]`] !== undefined) {
        employees.push(req.body[`employees[${i}]`]);
        i++;
      }
    }

    console.log('Employees to process:', employees);

    if (!employees || !Array.isArray(employees) || employees.length === 0) {
      // Clean up the uploaded file
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      return res.status(400).json({ message: 'No employees specified' });
    }

    // Process each employee
    const results = [];
    console.log('File to copy:', file);
    console.log('File path:', file.path);
    console.log('File exists:', fs.existsSync(file.path));
    console.log('Data directory:', dataDir);
    console.log('Data directory exists:', fs.existsSync(dataDir));

    for (const employee of employees) {
      try {
        console.log(`Processing employee: ${employee}`);
        const employeePath = path.join(dataDir, employee);
        console.log(`Employee path: ${employeePath}`);

        // Ensure employee directory exists
        fs.ensureDirSync(employeePath);
        console.log(`Employee directory created/exists: ${fs.existsSync(employeePath)}`);

        const destPath = path.join(employeePath, file.originalname);
        console.log(`Destination path: ${destPath}`);

        // Copy file with explicit error handling
        try {
          // Read the file content
          const fileContent = fs.readFileSync(file.path);
          console.log(`Read ${fileContent.length} bytes from source file`);

          // Write the file content to the destination
          fs.writeFileSync(destPath, fileContent);
          console.log(`File written successfully to: ${destPath}`);

          // Verify the file exists
          const fileExists = fs.existsSync(destPath);
          console.log(`File exists at destination: ${fileExists}`);

          if (!fileExists) {
            throw new Error(`File was not created at ${destPath}`);
          }

          // Verify file size
          const stats = fs.statSync(destPath);
          console.log(`File size at destination: ${stats.size} bytes`);

          if (stats.size === 0) {
            throw new Error(`File was created but is empty: ${destPath}`);
          }
        } catch (copyErr) {
          console.error(`Error copying file to ${destPath}:`, copyErr);
          throw copyErr; // Re-throw to be caught by the outer try-catch
        }

        // Add to database
        const result = await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
            [employee, file.originalname, file.originalname, file.size],
            function(err) {
              if (err) {
                console.error(`Database error for ${employee}:`, err);
                reject(err);
              } else {
                console.log(`Database entry created for ${employee}, ID: ${this.lastID}`);
                resolve({
                  employee,
                  fileId: this.lastID,
                  name: file.originalname,
                  path: `${employee}/${file.originalname}`,
                  size: file.size
                });
              }
            }
          );
        });

        results.push(result);
        console.log(`Successfully processed employee: ${employee}`);
      } catch (err) {
        console.error(`Error processing employee ${employee}:`, err);
        // Continue with other employees
      }
    }

    // Clean up the uploaded file
    try {
      if (fs.existsSync(file.path)) {
        console.log(`Deleting temporary file: ${file.path}`);
        fs.unlinkSync(file.path);
        console.log(`Temporary file deleted: ${!fs.existsSync(file.path)}`);
      } else {
        console.log(`Temporary file does not exist: ${file.path}`);
      }
    } catch (err) {
      console.error(`Error deleting temporary file: ${file.path}`, err);
      // Continue even if we can't delete the temp file
    }

    // Log activity
    logActivity(req.user.id, 'bulk_upload_file', `Uploaded file: ${file.originalname} to ${results.length} employees`);

    res.status(200).json({
      message: 'File uploaded successfully',
      file: file.originalname,
      employeesProcessed: results.length,
      totalEmployees: employees.length,
      results
    });
  } catch (err) {
    console.error('Error in bulk upload:', err);
    res.status(500).json({
      message: 'Error uploading file',
      error: err.message
    });
  }
});

module.exports = router;
