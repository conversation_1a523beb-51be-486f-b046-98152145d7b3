import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
      },
    },
    // إضافة إعدادات لدعم التوجيه الصحيح عند تحديث الصفحة
    historyApiFallback: true,
  },
  // إضافة إعدادات لدعم التوجيه الصحيح عند بناء التطبيق
  build: {
    rollupOptions: {
      output: {
        manualChunks: undefined,
      },
    },
  },
})
