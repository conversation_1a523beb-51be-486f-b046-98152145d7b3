Set WshShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get current directory
strCurrentDir = objFSO.GetAbsolutePathName(".")

' Get desktop path
strDesktop = WshShell.SpecialFolders("Desktop")

' Create shortcut for start.bat
Set objShortcut = WshShell.CreateShortcut(strDesktop & "\Employee Archive System.lnk")
objShortcut.TargetPath = strCurrentDir & "\start.bat"
objShortcut.WorkingDirectory = strCurrentDir
objShortcut.Description = "Employee Archive System - Start Application"
objShortcut.IconLocation = strCurrentDir & "\icons\app-icon.ico"
objShortcut.Save

' Create shortcut for setup.bat
Set objShortcut2 = WshShell.CreateShortcut(strDesktop & "\Setup Employee Archive System.lnk")
objShortcut2.TargetPath = strCurrentDir & "\setup.bat"
objShortcut2.WorkingDirectory = strCurrentDir
objShortcut2.Description = "Employee Archive System - Setup and Install"
objShortcut2.IconLocation = strCurrentDir & "\icons\setup-icon.ico"
objShortcut2.Save

' Create shortcut for run-both-servers.bat
Set objShortcut3 = WshShell.CreateShortcut(strDesktop & "\Employee Archive - Dual Servers.lnk")
objShortcut3.TargetPath = strCurrentDir & "\run-both-servers.bat"
objShortcut3.WorkingDirectory = strCurrentDir
objShortcut3.Description = "Employee Archive System - Run Both Servers Separately"
objShortcut3.IconLocation = strCurrentDir & "\icons\server-icon.ico"
objShortcut3.Save

WScript.Echo "Desktop shortcuts created successfully!" & vbCrLf & vbCrLf & _
            "Created shortcuts:" & vbCrLf & _
            "• Employee Archive System.lnk" & vbCrLf & _
            "• Setup Employee Archive System.lnk" & vbCrLf & _
            "• Employee Archive - Dual Servers.lnk"
