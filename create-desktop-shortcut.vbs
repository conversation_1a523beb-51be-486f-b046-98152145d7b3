' VBScript to create a desktop shortcut with a custom icon for start.bat
' This script will create a shortcut on the desktop

' Create WScript Shell object
Set WshShell = CreateObject("WScript.Shell")
Set FSO = CreateObject("Scripting.FileSystemObject")

' Get the current directory and desktop path
CurrentDir = FSO.GetParentFolderName(WScript.ScriptFullName)
DesktopPath = WshShell.SpecialFolders("Desktop")

' Create a shortcut on the desktop
Set shortcut = WshShell.CreateShortcut(DesktopPath & "\Employee Archive System.lnk")

' Set the target path to start.bat
shortcut.TargetPath = CurrentDir & "\start.bat"

' Set working directory
shortcut.WorkingDirectory = CurrentDir

' Set a description
shortcut.Description = "Employee Archive System"

' Set the icon to a document with green arrow icon (you can change the number to use a different icon)
shortcut.IconLocation = "%SystemRoot%\System32\SHELL32.dll,21"

' Save the shortcut
shortcut.Save

' Display a message
WScript.Echo "Desktop shortcut created successfully!" & vbCrLf & _
             "You can now use the ""Employee Archive System"" shortcut to start the system directly from your desktop."
