import { createContext, useContext, useState, useCallback } from 'react'; 
import axios from 'axios'; 
 
const AuthContext = createContext(); 
 
export const AuthProvider = ({ children }) =
  const [user, setUser] = useState(null); 
  const [isAuthenticated, setIsAuthenticated] = useState(false); 
  const [loading, setLoading] = useState(false); 
  const [error, setError] = useState(null); 
 
  // Login function 
  const login = async (username, password) =
    try { 
      setLoading(true); 
      setError(null); 
ECHO is off.
      const response = await axios.post('/api/auth/login', { username, password }); 
ECHO is off.
      const { token, user } = response.data; 
ECHO is off.
      // Save token to localStorage 
      localStorage.setItem('token', token); 
ECHO is off.
      // Set axios default headers 
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`; 
ECHO is off.
      setUser(user); 
      setIsAuthenticated(true); 
ECHO is off.
      return true; 
    } catch (err) { 
      return false; 
    } finally { 
      setLoading(false); 
    } 
  }; 
 
  // Logout function 
  const logout = () =
    // Remove token from localStorage 
    localStorage.removeItem('token'); 
ECHO is off.
    // Remove axios default headers 
    delete axios.defaults.headers.common['Authorization']; 
ECHO is off.
    setUser(null); 
    setIsAuthenticated(false); 
  }; 
 
  // Verify token function 
  const verifyToken = useCallback(async () =
    try { 
      setLoading(true); 
ECHO is off.
      const token = localStorage.getItem('token'); 
ECHO is off.
      if (!token) { 
        setIsAuthenticated(false); 
        setUser(null); 
        setLoading(false); 
        return; 
      } 
ECHO is off.
      // Set axios default headers 
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`; 
ECHO is off.
      const response = await axios.get('/api/auth/verify'); 
ECHO is off.
      setUser(response.data.user); 
      setIsAuthenticated(true); 
    } catch (err) { 
      // Token is invalid or expired 
      localStorage.removeItem('token'); 
      delete axios.defaults.headers.common['Authorization']; 
ECHO is off.
      setUser(null); 
      setIsAuthenticated(false); 
    } finally { 
      setLoading(false); 
    } 
  }, []); 
 
  // Initialize authentication state on app load 
  useState(() =
    verifyToken(); 
  }); 
 
  const value = { 
    user, 
    isAuthenticated, 
    loading, 
    error, 
    login, 
    logout, 
    verifyToken 
  }; 
 
  return ( 
