// أداة تشخيص النسخ الاحتياطية
// يمكن تشغيل هذا الملف لفحص بنية النسخة الاحتياطية

const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

const dataDir = path.join(__dirname, 'backend', 'data');
const testBackupPath = path.join(__dirname, 'test-backup.zip');
const testExtractDir = path.join(__dirname, 'test-extract');

console.log('=== أداة تشخيص النسخ الاحتياطية ===\n');

// 1. فحص البيانات الحالية
console.log('1. فحص البيانات الحالية:');
console.log(`   مجلد البيانات: ${dataDir}`);

const dbPath = path.join(dataDir, 'archive.db');
const employeeDataDir = path.join(dataDir, 'employee_database');

console.log(`   ملف قاعدة البيانات: ${fs.existsSync(dbPath) ? '✅ موجود' : '❌ غير موجود'}`);
console.log(`   مجلد بيانات الموظفين: ${fs.existsSync(employeeDataDir) ? '✅ موجود' : '❌ غير موجود'}`);

if (fs.existsSync(dbPath)) {
  const dbStats = fs.statSync(dbPath);
  console.log(`   حجم قاعدة البيانات: ${dbStats.size} بايت`);
}

if (fs.existsSync(employeeDataDir)) {
  try {
    const employees = fs.readdirSync(employeeDataDir);
    console.log(`   عدد مجلدات الموظفين: ${employees.length}`);
    if (employees.length > 0) {
      console.log(`   أمثلة على الموظفين: ${employees.slice(0, 3).join(', ')}`);
    }
  } catch (err) {
    console.log(`   خطأ في قراءة مجلد الموظفين: ${err.message}`);
  }
}

// 2. إنشاء نسخة احتياطية اختبارية
console.log('\n2. إنشاء نسخة احتياطية اختبارية:');

if (!fs.existsSync(dbPath) || !fs.existsSync(employeeDataDir)) {
  console.log('❌ لا يمكن إنشاء نسخة احتياطية - البيانات الأساسية غير موجودة');
  process.exit(1);
}

try {
  // إزالة النسخة الاختبارية السابقة إن وجدت
  if (fs.existsSync(testBackupPath)) {
    fs.unlinkSync(testBackupPath);
  }

  console.log('   جاري إنشاء النسخة الاحتياطية...');

  const output = fs.createWriteStream(testBackupPath);
  const archive = archiver('zip', { zlib: { level: 9 } });

  archive.pipe(output);

  // إضافة ملف قاعدة البيانات
  archive.file(dbPath, { name: 'archive.db' });
  console.log('   ✅ تم إضافة ملف قاعدة البيانات');

  // إضافة مجلد بيانات الموظفين
  archive.directory(employeeDataDir, 'employee_database');
  console.log('   ✅ تم إضافة مجلد بيانات الموظفين');

  await new Promise((resolve, reject) => {
    output.on('close', resolve);
    archive.on('error', reject);
    archive.finalize();
  });

  const backupStats = fs.statSync(testBackupPath);
  console.log(`   ✅ تم إنشاء النسخة الاحتياطية بنجاح (${backupStats.size} بايت)`);

} catch (err) {
  console.log(`   ❌ فشل في إنشاء النسخة الاحتياطية: ${err.message}`);
  process.exit(1);
}

// 3. استخراج النسخة الاحتياطية وفحص بنيتها
console.log('\n3. فحص بنية النسخة الاحتياطية:');

try {
  // إزالة مجلد الاستخراج السابق إن وجد
  if (fs.existsSync(testExtractDir)) {
    fs.removeSync(testExtractDir);
  }

  fs.ensureDirSync(testExtractDir);

  console.log('   جاري استخراج النسخة الاحتياطية...');

  // استخراج النسخة الاحتياطية
  execSync(`powershell -command "Expand-Archive -Path '${testBackupPath}' -DestinationPath '${testExtractDir}' -Force"`, {
    stdio: 'inherit'
  });

  console.log('   ✅ تم استخراج النسخة الاحتياطية بنجاح');

  // فحص محتويات النسخة الاحتياطية
  console.log('\n   محتويات النسخة الاحتياطية:');

  const listContents = (dir, prefix = '   ') => {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      if (entry.isDirectory()) {
        console.log(`${prefix}📁 ${entry.name}/`);
        if (prefix.length < 10) { // تحديد العمق
          listContents(fullPath, prefix + '  ');
        }
      } else {
        const stats = fs.statSync(fullPath);
        console.log(`${prefix}📄 ${entry.name} (${stats.size} بايت)`);
      }
    }
  };

  listContents(testExtractDir);

  // البحث عن الملفات المطلوبة
  console.log('\n   البحث عن الملفات المطلوبة:');

  const findFileOrDir = (dir, nameToFind, isDir = false) => {
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);

      if (entry.name === nameToFind && entry.isDirectory() === isDir) {
        return fullPath;
      }

      if (entry.isDirectory()) {
        const found = findFileOrDir(fullPath, nameToFind, isDir);
        if (found) return found;
      }
    }
    return null;
  };

  const extractedDbPath = findFileOrDir(testExtractDir, 'archive.db', false);
  const extractedEmployeeDir = findFileOrDir(testExtractDir, 'employee_database', true);

  console.log(`   ملف قاعدة البيانات: ${extractedDbPath ? '✅ موجود' : '❌ غير موجود'}`);
  console.log(`   مجلد بيانات الموظفين: ${extractedEmployeeDir ? '✅ موجود' : '❌ غير موجود'}`);

  if (extractedDbPath) {
    console.log(`   مسار قاعدة البيانات: ${extractedDbPath}`);
  }

  if (extractedEmployeeDir) {
    console.log(`   مسار مجلد الموظفين: ${extractedEmployeeDir}`);
  }

} catch (err) {
  console.log(`   ❌ فشل في فحص النسخة الاحتياطية: ${err.message}`);
}

// 4. تنظيف الملفات الاختبارية
console.log('\n4. تنظيف الملفات الاختبارية:');

try {
  if (fs.existsSync(testBackupPath)) {
    fs.unlinkSync(testBackupPath);
    console.log('   ✅ تم حذف النسخة الاحتياطية الاختبارية');
  }

  if (fs.existsSync(testExtractDir)) {
    fs.removeSync(testExtractDir);
    console.log('   ✅ تم حذف مجلد الاستخراج الاختباري');
  }
} catch (err) {
  console.log(`   ⚠️ تحذير: فشل في تنظيف الملفات الاختبارية: ${err.message}`);
}

console.log('\n=== انتهاء التشخيص ===');
console.log('\nإذا كانت جميع الفحوصات ناجحة، فإن مشكلة استعادة النسخة الاحتياطية');
console.log('قد تكون بسبب نسخة احتياطية تالفة أو تم إنشاؤها بطريقة مختلفة.');
console.log('\nجرب إنشاء نسخة احتياطية جديدة من النظام واستعادتها.');
