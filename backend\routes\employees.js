const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const { authenticateToken } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

const router = express.Router();

// Base directory for employee data
const dataDir = path.join(__dirname, '../data', 'employee_database');

// Get all employees
router.get('/', authenticateToken, (req, res) => {
  try {
    // Ensure data directory exists
    fs.ensureDirSync(dataDir);

    // Read all directories (employees)
    const employees = fs.readdirSync(dataDir)
      .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory())
      .map(folder => {
        // حساب عدد الملفات في مجلد الموظف
        const employeePath = path.join(dataDir, folder);
        let fileCount = 0;
        try {
          const files = fs.readdirSync(employeePath);
          fileCount = files.length;
        } catch (fileErr) {
          console.error(`Error counting files for employee ${folder}:`, fileErr);
        }

        return {
          name: folder,
          path: folder,
          createdAt: fs.statSync(path.join(dataDir, folder)).birthtime,
          fileCount: fileCount // إضافة عدد الملفات
        };
      });

    // Log activity
    logActivity(req.user.id, 'list_employees', 'عرض جميع الموظفين');

    res.json(employees);
  } catch (err) {
    console.error('Error reading employee directories:', err);
    res.status(500).json({ message: 'حدث خطأ أثناء قراءة مجلدات الموظفين' });
  }
});

// Get employee by name
router.get('/:name', authenticateToken, (req, res) => {
  const { name } = req.params;
  const employeePath = path.join(dataDir, name);

  try {
    // Check if employee directory exists
    if (!fs.existsSync(employeePath) || !fs.statSync(employeePath).isDirectory()) {
      return res.status(404).json({ message: 'الموظف غير موجود' });
    }

    // Get employee details
    const stats = fs.statSync(employeePath);
    const employee = {
      name,
      path: name,
      createdAt: stats.birthtime,
      updatedAt: stats.mtime
    };

    // Log activity
    logActivity(req.user.id, 'view_employee', `عرض بيانات الموظف: ${name}`);

    res.json(employee);
  } catch (err) {
    console.error(`Error reading employee ${name}:`, err);
    res.status(500).json({ message: `حدث خطأ أثناء قراءة بيانات الموظف ${name}` });
  }
});

// Create new employee
router.post('/', authenticateToken, (req, res) => {
  const { name } = req.body;

  if (!name) {
    return res.status(400).json({ message: 'اسم الموظف مطلوب' });
  }

  const employeePath = path.join(dataDir, name);

  try {
    // Check if employee directory already exists
    if (fs.existsSync(employeePath)) {
      return res.status(400).json({ message: 'الموظف موجود بالفعل' });
    }

    // Create employee directory
    fs.ensureDirSync(employeePath);

    // Get employee details
    const stats = fs.statSync(employeePath);
    const employee = {
      name,
      path: name,
      createdAt: stats.birthtime,
      updatedAt: stats.mtime
    };

    // Log activity
    logActivity(req.user.id, 'create_employee', `إنشاء موظف جديد: ${name}`);

    res.status(201).json(employee);
  } catch (err) {
    console.error(`Error creating employee ${name}:`, err);
    res.status(500).json({ message: `حدث خطأ أثناء إنشاء الموظف ${name}` });
  }
});

// Update employee
router.put('/:name', authenticateToken, (req, res) => {
  const { name } = req.params;
  const { newName } = req.body;

  if (!newName) {
    return res.status(400).json({ message: 'اسم الموظف الجديد مطلوب' });
  }

  const oldEmployeePath = path.join(dataDir, name);
  const newEmployeePath = path.join(dataDir, newName);

  try {
    // Check if old employee directory exists
    if (!fs.existsSync(oldEmployeePath) || !fs.statSync(oldEmployeePath).isDirectory()) {
      return res.status(404).json({ message: 'الموظف غير موجود' });
    }

    // Check if new employee directory already exists
    if (fs.existsSync(newEmployeePath) && name !== newName) {
      return res.status(400).json({ message: 'يوجد موظف آخر بنفس الاسم الجديد' });
    }

    // Rename employee directory
    fs.renameSync(oldEmployeePath, newEmployeePath);

    // Get updated employee details
    const stats = fs.statSync(newEmployeePath);
    const employee = {
      name: newName,
      path: newName,
      createdAt: stats.birthtime,
      updatedAt: stats.mtime
    };

    // Log activity
    logActivity(req.user.id, 'update_employee', `تعديل اسم الموظف من: ${name} إلى: ${newName}`);

    res.json(employee);
  } catch (err) {
    console.error(`Error updating employee ${name}:`, err);
    res.status(500).json({ message: `حدث خطأ أثناء تحديث بيانات الموظف ${name}` });
  }
});

// Delete employee
router.delete('/:name', authenticateToken, (req, res) => {
  const { name } = req.params;
  const employeePath = path.join(dataDir, name);

  try {
    // Check if employee directory exists
    if (!fs.existsSync(employeePath) || !fs.statSync(employeePath).isDirectory()) {
      return res.status(404).json({ message: 'الموظف غير موجود' });
    }

    // Delete employee directory
    fs.removeSync(employeePath);

    // Log activity
    logActivity(req.user.id, 'delete_employee', `حذف الموظف: ${name}`);

    res.json({ message: `تم حذف الموظف ${name} بنجاح` });
  } catch (err) {
    console.error(`Error deleting employee ${name}:`, err);
    res.status(500).json({ message: `حدث خطأ أثناء حذف الموظف ${name}` });
  }
});

module.exports = router;
