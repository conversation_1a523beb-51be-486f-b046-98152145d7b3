const express = require('express');
const bcrypt = require('bcrypt');
const { db } = require('../db/database');
const { authenticateToken, isAdmin } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

const router = express.Router();

// Get all users (admin only)
router.get('/', authenticateToken, isAdmin, (req, res) => {
  db.all('SELECT id, username, role, created_at FROM users', (err, users) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    // Log activity
    logActivity(req.user.id, 'list_users', 'عرض جميع المستخدمين');

    res.json(users);
  });
});

// Get user by ID (admin only)
router.get('/:id', authenticateToken, isAdmin, (req, res) => {
  const { id } = req.params;

  db.get('SELECT id, username, role, created_at FROM users WHERE id = ?', [id], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Log activity
    logActivity(req.user.id, 'view_user', `Viewed user: ${user.username}`);

    res.json(user);
  });
});

// Create new user (admin only)
router.post('/', authenticateToken, isAdmin, (req, res) => {
  const { username, password, role } = req.body;

  if (!username || !password || !role) {
    return res.status(400).json({ message: 'Username, password, and role are required' });
  }

  // Check if username already exists
  db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    if (user) {
      return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
    }

    // Hash password
    bcrypt.hash(password, 10, (err, hash) => {
      if (err) {
        console.error('Password hashing error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      // Insert new user
      db.run(
        'INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
        [username, hash, role],
        function(err) {
          if (err) {
            console.error('Database error:', err);
            return res.status(500).json({ message: 'Internal server error' });
          }

          // Log activity
          logActivity(req.user.id, 'create_user', `Created user: ${username} with role: ${role}`);

          res.status(201).json({
            message: 'User created successfully',
            userId: this.lastID
          });
        }
      );
    });
  });
});

// Update user (admin only)
router.put('/:id', authenticateToken, isAdmin, (req, res) => {
  const { id } = req.params;
  const { username, password, role } = req.body;
  const currentUserId = req.user.id;
  const currentUsername = req.user.username;

  if (!username && !password && !role) {
    return res.status(400).json({ message: 'At least one field (username, password, role) is required' });
  }

  // Check if user exists
  db.get('SELECT * FROM users WHERE id = ?', [id], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if trying to modify the main admin user
    if (user.username === 'admin' && currentUsername !== 'admin') {
      return res.status(403).json({ message: 'لا يمكن تعديل بيانات المستخدم الرئيسي إلا من قبل المستخدم نفسه' });
    }

    // Prevent changing the username of the main admin account
    if (user.username === 'admin' && username && username !== 'admin') {
      return res.status(403).json({ message: 'لا يمكن تغيير اسم المستخدم الرئيسي' });
    }

    // Prevent changing the role of the main admin account
    if (user.username === 'admin' && role && role !== 'admin') {
      return res.status(403).json({ message: 'لا يمكن تغيير صلاحية المستخدم الرئيسي' });
    }

    // If username is being updated, check if it already exists
    if (username && username !== user.username) {
      db.get('SELECT * FROM users WHERE username = ?', [username], (err, existingUser) => {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ message: 'Internal server error' });
        }

        if (existingUser) {
          return res.status(400).json({ message: 'اسم المستخدم موجود بالفعل' });
        }

        updateUser();
      });
    } else {
      updateUser();
    }

    function updateUser() {
      // If password is being updated, hash it
      if (password) {
        bcrypt.hash(password, 10, (err, hash) => {
          if (err) {
            console.error('Password hashing error:', err);
            return res.status(500).json({ message: 'Internal server error' });
          }

          updateUserInDb(hash);
        });
      } else {
        updateUserInDb(null);
      }
    }

    function updateUserInDb(hashedPassword) {
      let query = 'UPDATE users SET ';
      const params = [];

      if (username) {
        query += 'username = ?, ';
        params.push(username);
      }

      if (hashedPassword) {
        query += 'password = ?, ';
        params.push(hashedPassword);
      }

      if (role) {
        query += 'role = ?, ';
        params.push(role);
      }

      // Remove trailing comma and space
      query = query.slice(0, -2);

      query += ' WHERE id = ?';
      params.push(id);

      db.run(query, params, function(err) {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ message: 'Internal server error' });
        }

        // Log activity
        logActivity(req.user.id, 'update_user', `Updated user ID: ${id}`);

        res.json({ message: 'User updated successfully' });
      });
    }
  });
});

// Delete user (admin only)
router.delete('/:id', authenticateToken, isAdmin, (req, res) => {
  const { id } = req.params;
  const currentUsername = req.user.username;

  // First check if this is the main admin account
  db.get('SELECT username, role FROM users WHERE id = ?', [id], (err, user) => {
    if (err) {
      console.error('Database error:', err);
      return res.status(500).json({ message: 'Internal server error' });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Prevent deleting the main admin account
    if (user.username === 'admin') {
      return res.status(403).json({ message: 'لا يمكن حذف المستخدم الرئيسي' });
    }

    // Prevent deleting the last admin user
    db.get('SELECT COUNT(*) as count FROM users WHERE role = ?', ['admin'], (err, result) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      if (result.count <= 1) {
        if (user.role === 'admin') {
          return res.status(400).json({ message: 'لا يمكن حذف آخر مستخدم بصلاحية مدير' });
        }
      }

      deleteUser();
    });
  });

  function deleteUser() {
    // Get user details for logging
    db.get('SELECT username FROM users WHERE id = ?', [id], (err, user) => {
      if (err) {
        console.error('Database error:', err);
        return res.status(500).json({ message: 'Internal server error' });
      }

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Delete user
      db.run('DELETE FROM users WHERE id = ?', [id], function(err) {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ message: 'Internal server error' });
        }

        if (this.changes === 0) {
          return res.status(404).json({ message: 'User not found' });
        }

        // Log activity
        logActivity(req.user.id, 'delete_user', `Deleted user: ${user.username}`);

        res.json({ message: 'User deleted successfully' });
      });
    });
  }
});

module.exports = router;
