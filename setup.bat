@echo off
setlocal enabledelayedexpansion

REM ======================================================
REM          Employee Archive System - Setup
REM ======================================================

title Employee Archive System - Setup

echo ======================================================
echo          Employee Archive System - Setup
echo ======================================================
echo.
echo Checking system requirements...
echo.

REM Check for Node.js
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Node.js not found. Downloading and installing Node.js...
    echo.

    REM Download and install Node.js
    echo Downloading Node.js...

    REM Create temporary folder for download
    mkdir temp 2>nul

    REM Download Node.js using PowerShell
    powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.16.0/node-v18.16.0-x64.msi' -OutFile 'temp\node-installer.msi'}"

    echo Node.js downloaded successfully. Installing...

    REM Install Node.js
    start /wait msiexec /i temp\node-installer.msi /quiet /norestart

    REM Delete temporary file
    del /q temp\node-installer.msi
    rmdir temp

    echo Node.js installed successfully.
    echo.
) else (
    echo Node.js found.
    echo.
)

REM Check for npm
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo npm not found. Please make sure Node.js is installed correctly.
    echo.
    pause
    exit /b 1
) else (
    echo npm found.
    echo.
)

REM Create necessary directories
echo Creating necessary directories...
mkdir backend\data 2>nul
mkdir backend\data\employee_database 2>nul
mkdir backend\data\backups 2>nul
echo Directories created successfully.
echo.

REM Install backend dependencies
echo Installing backend dependencies...
cd backend
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install backend dependencies.
    cd ..
    pause
    exit /b 1
)
cd ..
echo Backend dependencies installed successfully.
echo.

REM Install frontend dependencies
echo Installing frontend dependencies...
cd frontend
call npm install
if %ERRORLEVEL% NEQ 0 (
    echo Failed to install frontend dependencies.
    cd ..
    pause
    exit /b 1
)
cd ..
echo Frontend dependencies installed successfully.
echo.

REM Create system configuration file
echo Creating system configuration file...
echo // Shared configuration file for frontend and backend > config.js
echo. >> config.js
echo const config = { >> config.js
echo   // Server settings >> config.js
echo   server: { >> config.js
echo     port: 5000, >> config.js
echo     baseUrl: 'http://localhost:5000', >> config.js
echo   }, >> config.js
echo. >> config.js
echo   // Frontend settings >> config.js
echo   frontend: { >> config.js
echo     port: 5173, >> config.js
echo     baseUrl: 'http://localhost:5173', >> config.js
echo   }, >> config.js
echo. >> config.js
echo   // Authentication settings >> config.js
echo   auth: { >> config.js
echo     jwtSecret: 'your_jwt_secret_key', >> config.js
echo     tokenExpiry: '24h', >> config.js
echo     defaultAdmin: { >> config.js
echo       username: 'admin', >> config.js
echo       password: 'admin123', >> config.js
echo       role: 'admin' >> config.js
echo     } >> config.js
echo   }, >> config.js
echo. >> config.js
echo   // Database settings >> config.js
echo   database: { >> config.js
echo     path: './data/archive.db', >> config.js
echo     employeeDir: './data/employee_database' >> config.js
echo   } >> config.js
echo }; >> config.js
echo. >> config.js
echo module.exports = config; >> config.js

echo Configuration file created successfully.
echo.

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "& {$WshShell = New-Object -ComObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut([Environment]::GetFolderPath('Desktop') + '\Employee Archive System.lnk'); $Shortcut.TargetPath = '%~dp0start.bat'; $Shortcut.WorkingDirectory = '%~dp0'; $Shortcut.Save()}"
echo Shortcut created successfully.
echo.

echo ======================================================
echo          System installed successfully!
echo ======================================================
echo.
echo You can now run the system in one of the following ways:
echo 1. Double-click on the start.bat file in the system folder
echo 2. Double-click on the "Employee Archive System" shortcut on the desktop
echo.
echo Default login credentials:
echo - Username: admin
echo - Password: admin123
echo.

REM Ask the user if they want to run the system now
set /p RUN_NOW=Do you want to run the system now? (Y/N):
if /i "%RUN_NOW%"=="Y" (
    echo.
    echo Starting the system...
    start "" "%~dp0start.bat"
) else (
    echo.
    echo Thank you. You can run the system later using the methods mentioned above.
)

echo.
echo Press any key to exit...
pause > nul
exit /b 0
