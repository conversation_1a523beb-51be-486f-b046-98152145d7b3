import { useState, useEffect } from 'react';
import axios from 'axios';
import {
  FiUsers,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiSearch,
  FiUser,
  FiAlertCircle,
  FiCheckCircle,
  FiX,
  FiChevronRight,
  FiChevronLeft
} from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';

const Users = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [userToEdit, setUserToEdit] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    role: 'viewer'
  });
  const [submitting, setSubmitting] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState(null);
  const [deletingUser, setDeletingUser] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  // متغيرات حالة للتصفح بالصفحات
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage] = useState(10); // عدد المستخدمين في كل صفحة

  useEffect(() => {
    fetchUsers();
  }, []);

  // إزالة رسالة النجاح بعد 5 ثوانٍ
  useEffect(() => {
    if (successMessage) {
      setShowSuccessMessage(true);
      const timer = setTimeout(() => {
        setShowSuccessMessage(false);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  // تحديد لون رسالة النجاح بناءً على نوع العملية
  const [successMessageType, setSuccessMessageType] = useState('add'); // 'add', 'edit', 'delete'

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.get('/api/users');
      setUsers(response.data);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('حدث خطأ أثناء جلب بيانات المستخدمين');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleAddUser = async (e) => {
    e.preventDefault();

    if (!formData.username || !formData.password) {
      alert('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    // التحقق من طول كلمة المرور
    if (formData.password.length < 6) {
      setError('يجب أن تكون كلمة المرور 6 أحرف على الأقل');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Store username before adding
      const username = formData.username;

      await axios.post('/api/users', formData);

      // Refresh users list
      fetchUsers();

      // Show success message
      setSuccessMessage(`تم إضافة المستخدم "${username}" بنجاح`);
      setSuccessMessageType('add');

      // Reset form
      setFormData({
        username: '',
        password: '',
        role: 'viewer'
      });
      setShowAddModal(false);
    } catch (err) {
      console.error('Error adding user:', err);
      setError(err.response?.data?.message || 'حدث خطأ أثناء إضافة المستخدم');
    } finally {
      setSubmitting(false);
    }
  };

  const handleEditUser = async (e) => {
    e.preventDefault();

    if (!userToEdit) return;

    // التحقق من طول كلمة المرور إذا تم تغييرها
    if (formData.password && formData.password.length < 6) {
      setError('يجب أن تكون كلمة المرور 6 أحرف على الأقل');
      return;
    }

    try {
      setSubmitting(true);
      setError(null);

      // Store username before updating
      const username = formData.username;
      const originalUsername = userToEdit.username;

      // Only send password if it's not empty
      const dataToSend = {
        username: formData.username,
        role: formData.role
      };

      if (formData.password) {
        dataToSend.password = formData.password;
      }

      await axios.put(`/api/users/${userToEdit.id}`, dataToSend);

      // Refresh users list
      fetchUsers();

      // Show success message
      if (username !== originalUsername) {
        setSuccessMessage(`تم تغيير اسم المستخدم من "${originalUsername}" إلى "${username}" بنجاح`);
      } else {
        setSuccessMessage(`تم تحديث بيانات المستخدم "${username}" بنجاح`);
      }
      setSuccessMessageType('edit');

      // Reset form
      setUserToEdit(null);
      setFormData({
        username: '',
        password: '',
        role: 'viewer'
      });
      setShowEditModal(false);
    } catch (err) {
      console.error('Error updating user:', err);
      setError(err.response?.data?.message || 'حدث خطأ أثناء تحديث المستخدم');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setDeletingUser(true);
      setError(null);

      // Store user info before deletion
      const username = userToDelete.username;

      await axios.delete(`/api/users/${userToDelete.id}`);

      // Refresh users list
      fetchUsers();

      // Show success message
      setSuccessMessage(`تم حذف المستخدم "${username}" بنجاح`);
      setSuccessMessageType('delete');

      // Reset state
      setUserToDelete(null);
      setShowDeleteModal(false);
    } catch (err) {
      console.error('Error deleting user:', err);
      setError(err.response?.data?.message || 'حدث خطأ أثناء حذف المستخدم');
    } finally {
      setDeletingUser(false);
    }
  };

  const openEditModal = (user) => {
    setUserToEdit(user);
    setFormData({
      username: user.username,
      password: '', // Don't show the current password
      role: user.role
    });
    setError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
    setShowEditModal(true);
  };

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

  // الحصول على المستخدمين المعروضين في الصفحة الحالية
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);

  // دالة للانتقال إلى صفحة معينة
  const paginate = (pageNumber) => {
    if (pageNumber > 0 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
      // التمرير إلى أعلى الصفحة عند تغيير الصفحة
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRoleText = (role) => {
    switch (role) {
      case 'admin':
        return 'مدير';
      case 'editor':
        return 'محرر';
      case 'viewer':
        return 'مشاهد';
      default:
        return role;
    }
  };

  return (
    <div>
      {/* Header Section */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg opacity-70"></div>
        <div className="relative p-6 rounded-lg overflow-hidden">
          <div className="absolute top-0 right-0 w-40 h-40 bg-primary-100 rounded-full -mr-20 -mt-20 opacity-50"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-100 rounded-full -ml-12 -mb-12 opacity-50"></div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">إدارة المستخدمين</h1>
              <p className="text-gray-600">إدارة حسابات المستخدمين وصلاحياتهم في النظام</p>
            </div>

            <button
              onClick={() => {
                setShowAddModal(true);
                setError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
                setFormData({
                  username: '',
                  password: '',
                  role: 'viewer'
                });
              }}
              className="btn btn-primary flex items-center shadow-sm hover:shadow-md transition-all"
            >
              <FiPlus className="ml-1" />
              إضافة مستخدم
            </button>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {showSuccessMessage && (
        <div className={`mb-4 rounded-lg shadow-md p-3 flex items-center transition-all duration-500 animate-fade-in
          ${successMessageType === 'add'
            ? 'bg-primary-50 border border-primary-200 text-primary-800'
            : successMessageType === 'edit'
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
          }`}
        >
          <FiCheckCircle className={`text-xl ml-2 flex-shrink-0
            ${successMessageType === 'add'
              ? 'text-primary-500'
              : successMessageType === 'edit'
              ? 'text-green-500'
              : 'text-red-500'
            }`}
          />
          <div className="flex-grow">{successMessage}</div>
          <button
            onClick={() => setShowSuccessMessage(false)}
            className="text-gray-400 hover:text-gray-600 ml-2 focus:outline-none"
          >
            <FiX />
          </button>
        </div>
      )}

      {/* Search */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 p-4 rounded-t-lg border-b border-primary-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiSearch className="w-5 h-5 text-primary-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">البحث عن مستخدم</h2>
          </div>
        </div>
        <div className="p-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <FiSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              className="input pr-10 w-full focus:ring-2 focus:ring-primary-300 focus:border-primary-500 transition-all shadow-sm"
              placeholder="ابحث عن مستخدم..."
              value={searchQuery}
              onChange={(e) => {
                setSearchQuery(e.target.value);
                setCurrentPage(1); // إعادة تعيين الصفحة الحالية عند تغيير البحث
              }}
              autoFocus
            />
            {searchQuery && (
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <button
                  onClick={() => setSearchQuery('')}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  aria-label="مسح البحث"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Users List */}
      <div className="card mb-8 transform transition-all duration-300 hover:shadow-lg">
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-t-lg border-b border-blue-200">
          <div className="flex items-center">
            <div className="p-2 bg-white rounded-full shadow-sm ml-3">
              <FiUsers className="w-5 h-5 text-blue-600" />
            </div>
            <h2 className="text-lg font-semibold text-gray-800">قائمة المستخدمين</h2>
          </div>
        </div>

        <div className="p-4 bg-white rounded-b-lg">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin h-12 w-12 border-4 border-primary-500 rounded-full border-t-transparent mb-4 mx-auto"></div>
              <p className="text-gray-500 font-medium">جاري تحميل بيانات المستخدمين...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="bg-red-100 p-4 rounded-full inline-flex items-center justify-center mb-4">
                <svg className="h-12 w-12 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <p className="text-red-500 font-medium text-lg mb-4">{error}</p>
              <button
                onClick={fetchUsers}
                className="btn btn-secondary inline-flex items-center px-4 py-2"
              >
                <svg className="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                إعادة المحاولة
              </button>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-lg font-medium text-gray-900">لا يوجد مستخدمين</h3>
              {searchQuery ? (
                <p className="mt-1 text-gray-500">لا توجد نتائج تطابق بحثك. حاول بكلمات أخرى.</p>
              ) : (
                <p className="mt-1 text-gray-500">قم بإضافة مستخدم جديد للبدء.</p>
              )}
            </div>
          ) : (
            <div className="overflow-hidden rounded-lg shadow-sm border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم المستخدم
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الصلاحية
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الإنشاء
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {currentUsers.map((userItem) => (
                    <tr key={userItem.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold shadow-sm">
                            {userItem.username.charAt(0).toUpperCase()}
                          </div>
                          <div className="mr-3">
                            <div className="text-sm font-medium text-gray-900">{userItem.username}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          userItem.role === 'admin'
                            ? 'bg-purple-100 text-purple-800'
                            : userItem.role === 'editor'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {getRoleText(userItem.role)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{formatDate(userItem.created_at)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-3 space-x-reverse">
                          {/* Only show edit button for admin user if current user is admin */}
                          {(userItem.username !== 'admin' || user.username === 'admin') && (
                            <button
                              onClick={() => openEditModal(userItem)}
                              className="p-1.5 rounded-full bg-green-50 text-green-600 hover:bg-green-100 transition-colors"
                              title="تعديل"
                            >
                              <FiEdit className="w-4 h-4" />
                            </button>
                          )}

                          {/* Don't allow deleting yourself or the main admin account */}
                          {userItem.id !== user.id && userItem.username !== 'admin' && (
                            <button
                              onClick={() => {
                                setUserToDelete(userItem);
                                setError(null); // إعادة تعيين رسالة الخطأ عند فتح النافذة
                                setShowDeleteModal(true);
                              }}
                              className="p-1.5 rounded-full bg-red-50 text-red-600 hover:bg-red-100 transition-colors"
                              title="حذف"
                            >
                              <FiTrash2 className="w-4 h-4" />
                            </button>
                          )}

                          {/* Show info icon for admin user if current user is not admin */}
                          {userItem.username === 'admin' && user.username !== 'admin' && (
                            <span
                              className="p-1.5 rounded-full bg-gray-50 text-gray-400 cursor-not-allowed"
                              title="لا يمكن تعديل المستخدم الرئيسي"
                            >
                              <FiAlertCircle className="w-4 h-4" />
                            </span>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-500">
                      عرض {indexOfFirstUser + 1} إلى {Math.min(indexOfLastUser, filteredUsers.length)} من أصل {filteredUsers.length} مستخدم
                    </div>
                    <div className="flex space-x-1 space-x-reverse">
                      <button
                        onClick={() => paginate(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === 1
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <FiChevronRight className="ml-1" />
                        <span>السابق</span>
                      </button>

                      {/* أرقام الصفحات */}
                      {[...Array(totalPages)].map((_, index) => {
                        const pageNumber = index + 1;
                        // عرض أول صفحتين وآخر صفحتين والصفحة الحالية وما حولها
                        if (
                          pageNumber === 1 ||
                          pageNumber === totalPages ||
                          (pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
                        ) {
                          return (
                            <button
                              key={pageNumber}
                              onClick={() => paginate(pageNumber)}
                              className={`px-3 py-1 rounded-md ${
                                currentPage === pageNumber
                                  ? 'bg-primary-600 text-white'
                                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                              }`}
                            >
                              {pageNumber}
                            </button>
                          );
                        } else if (
                          (pageNumber === 2 && currentPage > 3) ||
                          (pageNumber === totalPages - 1 && currentPage < totalPages - 2)
                        ) {
                          // عرض نقاط للصفحات المحذوفة
                          return <span key={pageNumber} className="px-2 py-1">...</span>;
                        }
                        return null;
                      })}

                      <button
                        onClick={() => paginate(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`flex items-center px-3 py-1 rounded-md ${
                          currentPage === totalPages
                            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                            : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        <span>التالي</span>
                        <FiChevronLeft className="mr-1" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add User Modal */}
      {showAddModal && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-primary-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center ml-4 shadow-md">
                    <FiPlus className="h-6 w-6 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      إضافة مستخدم جديد
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      أدخل بيانات المستخدم الجديد للنظام.
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {error && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{error}</span>
                  </div>
                )}
                <form onSubmit={handleAddUser}>
                  <div className="mb-4">
                    <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                      اسم المستخدم
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiUser className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        className="input pr-10 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 shadow-sm"
                        placeholder="أدخل اسم المستخدم"
                        value={formData.username}
                        onChange={handleInputChange}
                        required
                        autoFocus
                      />
                    </div>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        className="input pr-10 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 shadow-sm"
                        placeholder="أدخل كلمة المرور"
                        value={formData.password}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">يجب أن تكون كلمة المرور 6 أحرف على الأقل</p>
                  </div>

                  <div className="mb-4">
                    <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
                      الصلاحية
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <select
                        id="role"
                        name="role"
                        className="input pr-10 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 shadow-sm"
                        value={formData.role}
                        onChange={handleInputChange}
                      >
                        <option value="viewer">مشاهد</option>
                        <option value="editor">محرر</option>
                        <option value="admin">مدير</option>
                      </select>
                    </div>
                  </div>

                  <div className="mt-4 bg-blue-50 p-3 rounded-lg border border-blue-100">
                    <div className="flex items-start">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500 ml-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                      </svg>
                      <div className="text-sm text-blue-700">
                        <p>الصلاحيات المتاحة:</p>
                        <ul className="mt-1 list-disc list-inside mr-4">
                          <li><strong>مشاهد:</strong> يمكنه عرض الملفات فقط</li>
                          <li><strong>محرر:</strong> يمكنه إضافة وتعديل الملفات والموظفين</li>
                          <li><strong>مدير:</strong> يمكنه إدارة المستخدمين والوصول لكافة الصلاحيات</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleAddUser}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${submitting || !formData.username || !formData.password
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-primary-600 hover:bg-primary-700 hover:shadow-lg'
                    }
                  `}
                  disabled={submitting || !formData.username || !formData.password}
                >
                  <FiPlus className="ml-2" />
                  {submitting ? 'جاري الإضافة...' : 'إضافة مستخدم'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddModal(false)}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && userToEdit && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-green-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center ml-4 shadow-md">
                    <FiEdit className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      تعديل بيانات المستخدم
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      قم بتعديل بيانات المستخدم <span className="font-semibold text-green-600">"{userToEdit.username}"</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {error && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{error}</span>
                  </div>
                )}
                <form onSubmit={handleEditUser}>
                  <div className="mb-6">
                    <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-2">
                      اسم المستخدم
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <FiUsers className="text-gray-400" />
                      </div>
                      <input
                        type="text"
                        id="username"
                        name="username"
                        className={`input pr-10 focus:ring-green-500 focus:border-green-500 transition-all duration-300 shadow-sm ${
                          userToEdit?.username === 'admin' ? 'bg-gray-100 cursor-not-allowed' : ''
                        }`}
                        placeholder="أدخل اسم المستخدم"
                        value={formData.username}
                        onChange={handleInputChange}
                        required
                        disabled={userToEdit?.username === 'admin'}
                      />
                    </div>
                    {userToEdit?.username === 'admin' && (
                      <p className="mt-1 text-xs text-yellow-600">
                        <FiAlertCircle className="inline ml-1" />
                        لا يمكن تغيير اسم المستخدم الرئيسي
                      </p>
                    )}
                  </div>

                  <div className="mb-6">
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور (اتركها فارغة إذا لم ترغب في تغييرها)
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <input
                        type="password"
                        id="password"
                        name="password"
                        className="input pr-10 focus:ring-green-500 focus:border-green-500 transition-all duration-300 shadow-sm"
                        placeholder="اترك هذا الحقل فارغاً إذا لم ترغب في تغيير كلمة المرور"
                        value={formData.password}
                        onChange={handleInputChange}
                      />
                    </div>
                    <p className="mt-1 text-xs text-gray-500">إذا أردت تغيير كلمة المرور، يجب أن تكون 6 أحرف على الأقل</p>
                  </div>

                  <div className="mb-6">
                    <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-2">
                      الصلاحية
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <select
                        id="role"
                        name="role"
                        className={`input pr-10 focus:ring-green-500 focus:border-green-500 transition-all duration-300 shadow-sm ${
                          (userToEdit.username === 'admin' || (userToEdit.role === 'admin' && users.filter(u => u.role === 'admin').length <= 1))
                            ? 'bg-gray-100 cursor-not-allowed'
                            : ''
                        }`}
                        value={formData.role}
                        onChange={handleInputChange}
                        disabled={
                          userToEdit.username === 'admin' ||
                          (userToEdit.role === 'admin' &&
                          users.filter(u => u.role === 'admin').length <= 1)
                        }
                      >
                        <option value="viewer">مشاهد</option>
                        <option value="editor">محرر</option>
                        <option value="admin">مدير</option>
                      </select>
                    </div>
                    {userToEdit.username === 'admin' && (
                      <div className="mt-2 bg-yellow-50 p-3 rounded-lg border border-yellow-100">
                        <div className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 ml-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-xs text-yellow-700">
                            لا يمكن تغيير صلاحية المستخدم الرئيسي.
                          </p>
                        </div>
                      </div>
                    )}
                    {userToEdit.role === 'admin' && users.filter(u => u.role === 'admin').length <= 1 && userToEdit.username !== 'admin' && (
                      <div className="mt-2 bg-yellow-50 p-3 rounded-lg border border-yellow-100">
                        <div className="flex items-start">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-500 ml-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          <p className="text-xs text-yellow-700">
                            لا يمكن تغيير صلاحية المدير الوحيد في النظام.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </form>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleEditUser}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${submitting || !formData.username
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-green-600 hover:bg-green-700 hover:shadow-lg'
                    }
                  `}
                  disabled={submitting || !formData.username}
                >
                  <FiEdit className="ml-2" />
                  {submitting ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setUserToEdit(null);
                    setShowEditModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete User Modal */}
      {showDeleteModal && userToDelete && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen"></span>&#8203;

            <div className="inline-block align-bottom bg-white rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              {/* Header with gradient background */}
              <div className="bg-gradient-to-r from-red-50 to-white p-6 rounded-t-lg">
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center ml-4 shadow-md">
                    <FiTrash2 className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">
                      حذف مستخدم
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      تأكيد حذف المستخدم <span className="font-semibold text-red-600">"{userToDelete.username}"</span>
                    </p>
                  </div>
                </div>
              </div>

              <div className="p-6">
                {error && (
                  <div className="mb-4 p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 text-sm flex items-start">
                    <FiAlertCircle className="ml-2 mt-0.5 flex-shrink-0 text-red-500" />
                    <span>{error}</span>
                  </div>
                )}
                <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="mr-3">
                      <h3 className="text-sm font-medium text-red-800">تحذير هام</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>
                          سيتم حذف المستخدم "{userToDelete.username}" بشكل نهائي.
                        </p>
                        <p className="font-bold mt-1">
                          هذا الإجراء لا يمكن التراجع عنه.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-4 bg-gray-50 p-3 rounded-lg">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white font-bold ml-3">
                      {userToDelete.username.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <div className="font-medium">{userToDelete.username}</div>
                      <div className="text-sm text-gray-500">
                        الصلاحية: {getRoleText(userToDelete.role)}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 px-6 py-4 sm:flex sm:flex-row-reverse border-t border-gray-200">
                <button
                  type="button"
                  onClick={handleDeleteUser}
                  className={`
                    flex items-center justify-center px-6 py-3 rounded-lg text-white font-medium
                    transition-all duration-300 shadow-md
                    ${deletingUser
                      ? 'bg-gray-400 cursor-not-allowed opacity-70'
                      : 'bg-red-600 hover:bg-red-700 hover:shadow-lg'
                    }
                  `}
                  disabled={deletingUser}
                >
                  <FiTrash2 className="ml-2" />
                  {deletingUser ? 'جاري الحذف...' : 'تأكيد الحذف'}
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setUserToDelete(null);
                    setShowDeleteModal(false);
                  }}
                  className="mt-3 flex items-center justify-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 font-medium hover:bg-gray-50 focus:outline-none transition-colors duration-300 sm:mt-0 sm:ml-3"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Users;
