#!/bin/bash

echo "Starting Employee Archive System..."
echo
echo "This script will install dependencies and start the application."
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js and try again."
    echo "You can download Node.js from https://nodejs.org/"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "Installing project dependencies..."
    npm run install-all
    if [ $? -ne 0 ]; then
        echo "Failed to install dependencies."
        exit 1
    fi
fi

# Check if data directory is empty and offer to initialize sample data
if [ ! -d "backend/data/قاعدة_البيانات_المحلية" ]; then
    echo "No employee data found. Would you like to initialize sample data? (y/n)"
    read INIT_DATA
    if [ "$INIT_DATA" = "y" ] || [ "$INIT_DATA" = "Y" ]; then
        echo "Initializing sample data..."
        npm run init-data
    fi
fi

# Start the application
echo "Starting the application..."
echo
echo "The application will be available at:"
echo "Frontend: http://localhost:5173"
echo "Backend: http://localhost:5000"
echo
echo "Default login credentials:"
echo "Username: admin"
echo "Password: admin123"
echo
echo "Press Ctrl+C to stop the application."
echo

npm run dev
