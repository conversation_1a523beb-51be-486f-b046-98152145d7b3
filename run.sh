#!/bin/bash

# Employee Archive System - Linux/Mac Startup Script
# This script starts both backend and frontend servers

echo "========================================="
echo "    Employee Archive System"
echo "    Starting Servers (Linux/Mac)"
echo "========================================="
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js and npm are installed"
echo

# Install backend dependencies if needed
if [ ! -d "backend/node_modules" ]; then
    echo "📦 Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    echo "✅ Backend dependencies installed"
    echo
fi

# Install frontend dependencies if needed
if [ ! -d "frontend/node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    echo "✅ Frontend dependencies installed"
    echo
fi

# Start backend server in background
echo "🚀 Starting backend server..."
cd backend
npm start > ../backend_output.log 2>&1 &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend server in background
echo "🌐 Starting frontend server..."
cd frontend
npm run dev > ../frontend_output.log 2>&1 &
FRONTEND_PID=$!
cd ..

echo
echo "========================================="
echo "    Servers Started Successfully!"
echo "========================================="
echo
echo "🔗 Backend Server:  http://localhost:5000"
echo "🔗 Frontend Server: http://localhost:5173"
echo
echo "📋 Backend PID:  $BACKEND_PID"
echo "📋 Frontend PID: $FRONTEND_PID"
echo
echo "📝 Logs:"
echo "   Backend:  backend_output.log"
echo "   Frontend: frontend_output.log"
echo
echo "⏹️  To stop servers, press Ctrl+C"
echo

# Function to cleanup on exit
cleanup() {
    echo
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Servers stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
echo "Press Ctrl+C to stop both servers..."
wait
