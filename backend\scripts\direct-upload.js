/**
 * Direct Upload Script
 *
 * This script provides a direct method to upload files to employee folders
 * without going through the API. It's useful for debugging and fixing issues.
 */

const fs = require('fs-extra');
const path = require('path');
const { db } = require('../db/database');

// Base directory for employee data
const dataDir = path.join(__dirname, '../data/employee_database');

/**
 * Upload a file to multiple employee folders
 * @param {string} filePath - Path to the source file
 * @param {string[]} employees - Array of employee names
 * @param {string} userId - User ID for activity logging (optional)
 * @returns {Promise<object>} - Result of the operation
 */
async function uploadFileToEmployees(filePath, employees, userId = 'script') {
  console.log(`Starting direct upload of ${filePath} to ${employees.length} employees`);

  // Validate inputs
  if (!fs.existsSync(filePath)) {
    throw new Error(`Source file does not exist: ${filePath}`);
  }

  if (!Array.isArray(employees) || employees.length === 0) {
    throw new Error('No employees specified');
  }

  // Ensure data directory exists
  fs.ensureDirSync(dataDir);
  console.log(`Data directory: ${dataDir} (exists: ${fs.existsSync(dataDir)})`);

  // Get file information
  const fileName = path.basename(filePath);
  const fileStats = fs.statSync(filePath);
  const fileSize = fileStats.size;

  console.log(`File: ${fileName}, Size: ${fileSize} bytes`);

  // Read file content
  const fileContent = fs.readFileSync(filePath);
  console.log(`Read ${fileContent.length} bytes from source file`);

  const results = [];
  const errors = [];

  // Track existing files
  const existingFiles = [];

  // Process each employee
  for (const employee of employees) {
    try {
      console.log(`Processing employee: ${employee}`);

      // Create employee directory if it doesn't exist
      const employeePath = path.join(dataDir, employee);
      fs.ensureDirSync(employeePath);
      console.log(`Employee directory: ${employeePath} (exists: ${fs.existsSync(employeePath)})`);

      // Check if file already exists
      const destPath = path.join(employeePath, fileName);
      if (fs.existsSync(destPath)) {
        console.log(`File already exists: ${destPath}`);
        existingFiles.push({
          employee,
          path: destPath,
          fileName: fileName
        });
        continue; // Skip this employee
      }

      // Write file to employee directory
      fs.writeFileSync(destPath, fileContent);
      console.log(`File written to: ${destPath} (exists: ${fs.existsSync(destPath)})`);

      // Verify file size
      const destStats = fs.statSync(destPath);
      console.log(`Destination file size: ${destStats.size} bytes`);

      if (destStats.size !== fileSize) {
        throw new Error(`File size mismatch: ${destStats.size} != ${fileSize}`);
      }

      // Add file metadata to database
      await new Promise((resolve, reject) => {
        db.run(
          'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
          [employee, fileName, fileName, fileSize],
          function(err) {
            if (err) {
              console.error(`Database error for ${employee}:`, err);
              reject(err);
            } else {
              console.log(`Database entry created for ${employee}, ID: ${this.lastID}`);
              resolve(this.lastID);
            }
          }
        );
      });

      results.push({
        employee,
        path: destPath,
        size: fileSize
      });

      console.log(`Successfully processed employee: ${employee}`);
    } catch (err) {
      console.error(`Error processing employee ${employee}:`, err);
      errors.push({
        employee,
        error: err.message
      });
    }
  }

  return {
    success: errors.length === 0,
    file: fileName,
    size: fileSize,
    totalEmployees: employees.length,
    successCount: results.length,
    errorCount: errors.length,
    existingFilesCount: existingFiles.length,
    existingFiles,
    results,
    errors
  };
}

// Export the function for use in other modules
module.exports = {
  uploadFileToEmployees
};

// If this script is run directly, execute the test function
if (require.main === module) {
  // Test function
  async function test() {
    try {
      // Create a test file
      const testDir = path.join(__dirname, '../temp');
      fs.ensureDirSync(testDir);

      const testFile = path.join(testDir, 'test-file.txt');
      fs.writeFileSync(testFile, 'This is a test file for direct upload.');

      // Test employees
      const employees = ['Ahmed Mohammed', 'Sarah Ali'];

      // Upload the file
      const result = await uploadFileToEmployees(testFile, employees);

      console.log('Upload result:', JSON.stringify(result, null, 2));
    } catch (err) {
      console.error('Test failed:', err);
    }
  }

  // Run the test
  test().then(() => {
    console.log('Test completed');
  });
}
