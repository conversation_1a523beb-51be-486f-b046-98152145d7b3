const express = require('express');
const path = require('path');
const fs = require('fs-extra');
const multer = require('multer');
const { db } = require('../db/database');
const { authenticateToken, isEditor } = require('../middleware/auth');
const { logActivity } = require('../middleware/logger');

// دالة لإصلاح ترميز أسماء الملفات العربية
function fixArabicFileName(fileName) {
  let fixed = fileName;

  // إصلاح الأنماط المشوهة الموجودة فعلياً
  const encodingFixes = {
    // إصلاح النمط الأساسي الكامل
    'مرمز صمامة مرمز امرمادم': 'مرمز صيانة مرمز الرياضي',
    'هرهز صهاهØ© هرهز اهرهاده': 'مرمز صيانة مرمز الرياضي',

    // إصلاح الكلمات المفردة
    'صمامة': 'صيانة',
    'صمامØ©': 'صيانة',
    'امرمادم': 'الرياضي',
    'اهرهاده': 'الرياضي',
    'صهاهØ©': 'صيانة',
    'مم': 'من',
    'امم': 'الى',
    'هه': 'من',
    'اهه': 'الى',

    // إصلاح الأحرف المفردة المشوهة
    'Ø©': 'ة',

    // إصلاحات إضافية للترميز
    'ÙØ±ÙØ²': 'مرمز',
    'ØµÙØ§ÙØ©': 'صيانة',
    'Ø§ÙØ±ÙØ§Ø¯Ù': 'الرياضي',
    'ÙÙ': 'من',
    'Ø§ÙÙ': 'الى'
  };

  // تطبيق الإصلاحات بالترتيب
  Object.keys(encodingFixes).forEach(wrong => {
    const regex = new RegExp(wrong.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    fixed = fixed.replace(regex, encodingFixes[wrong]);
  });

  // تنظيف المسافات الزائدة
  fixed = fixed.replace(/\s+/g, ' ').trim();

  return fixed;
}

const router = express.Router();

// Base directory for employee data
const dataDir = path.join(__dirname, '../data', 'employee_database');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Store files in a temp directory first
    const tempDir = path.join(__dirname, '../temp');

    // Ensure temp directory exists
    fs.ensureDirSync(tempDir);

    cb(null, tempDir);
  },
  filename: (req, file, cb) => {
    // تسجيل اسم الملف الأصلي للتشخيص
    console.log(`Original filename: ${file.originalname}`);
    console.log(`Original filename buffer:`, Buffer.from(file.originalname).toString('hex'));

    // استخدام اسم الملف الأصلي كما هو
    cb(null, file.originalname);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    // Accept all file types
    cb(null, true);
  }
});

// Get total file count
router.get('/count', authenticateToken, (req, res) => {
  try {
    db.get('SELECT COUNT(*) as count FROM file_metadata', [], (err, row) => {
      if (err) {
        console.error('Error counting files:', err);
        return res.status(500).json({ message: 'Error counting files' });
      }

      res.json({ count: row ? row.count : 0 });
    });
  } catch (err) {
    console.error('Error counting files:', err);
    res.status(500).json({ message: 'Error counting files' });
  }
});

// Get all files for an employee
router.get('/employee/:employee', authenticateToken, (req, res) => {
  const { employee } = req.params;
  console.log(`Received request for files of employee: ${employee}`);
  console.log(`Data directory: ${dataDir}`);

  const employeePath = path.join(dataDir, employee);
  console.log(`Employee path: ${employeePath}`);
  console.log(`Employee path exists: ${fs.existsSync(employeePath)}`);

  try {
    // Check if employee directory exists
    if (!fs.existsSync(employeePath)) {
      console.error(`Employee directory does not exist: ${employeePath}`);
      return res.status(404).json({ message: 'Employee not found' });
    }

    if (!fs.statSync(employeePath).isDirectory()) {
      console.error(`Path exists but is not a directory: ${employeePath}`);
      return res.status(404).json({ message: 'Employee not found' });
    }

    console.log(`Reading files from directory: ${employeePath}`);
    // Read all files in employee directory
    const filesList = fs.readdirSync(employeePath);
    console.log(`Files found: ${filesList.length}`);

    const files = filesList.map(file => {
      const filePath = path.join(employeePath, file);
      const stats = fs.statSync(filePath);

      return {
        name: file,
        path: `${employee}/${file}`,
        size: stats.size,
        createdAt: stats.birthtime,
        updatedAt: stats.mtime
      };
    });

    // Log activity
    logActivity(req.user.id, 'list_files', `عرض ملفات الموظف: ${employee}`);

    console.log(`Sending response with ${files.length} files`);

    // تعيين رأس الاستجابة لضمان ترميز UTF-8
    res.setHeader('Content-Type', 'application/json; charset=utf-8');
    res.json(files);
  } catch (err) {
    console.error(`Error reading files for employee ${employee}:`, err);
    res.status(500).json({ message: `Error reading files for employee ${employee}` });
  }
});

// Get file by name for an employee
router.get('/employee/:employee/:filename', authenticateToken, (req, res) => {
  const { employee, filename } = req.params;
  const filePath = path.join(dataDir, employee, filename);

  try {
    console.log(`Received request for file: ${filename} for employee: ${employee}`);
    console.log(`File path: ${filePath}`);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      console.error(`File does not exist: ${filePath}`);
      return res.status(404).json({ message: 'File not found' });
    }

    if (!fs.statSync(filePath).isFile()) {
      console.error(`Path exists but is not a file: ${filePath}`);
      return res.status(404).json({ message: 'File not found' });
    }

    // Log activity
    logActivity(req.user.id, 'view_file', `عرض ملف: ${filename} للموظف: ${employee}`);

    // Determine content type based on file extension
    const ext = path.extname(filename).toLowerCase();
    let contentType = 'application/octet-stream'; // Default content type

    if (ext === '.pdf') {
      contentType = 'application/pdf';
    } else if (ext === '.jpg' || ext === '.jpeg') {
      contentType = 'image/jpeg';
    } else if (ext === '.png') {
      contentType = 'image/png';
    } else if (ext === '.doc') {
      contentType = 'application/msword';
    } else if (ext === '.docx') {
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    } else if (ext === '.xls') {
      contentType = 'application/vnd.ms-excel';
    } else if (ext === '.xlsx') {
      contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    } else if (ext === '.txt') {
      contentType = 'text/plain';
    }

    // Ensure proper encoding for the filename in Content-Disposition header
    const encodedFilename = encodeURIComponent(filename);

    // تسجيل اسم الملف للتشخيص
    console.log(`Sending file for viewing: ${filename}, Encoded: ${encodedFilename}`);
    console.log(`Content type: ${contentType}`);

    // إضافة رؤوس إضافية لمنع التخزين المؤقت
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `inline; filename*=UTF-8''${encodedFilename}`);

    // استخدام fs.createReadStream بدلاً من res.sendFile للتحكم بشكل أفضل في العملية
    const fileStream = fs.createReadStream(filePath);

    fileStream.on('error', (err) => {
      console.error(`Error streaming file ${filename} for employee ${employee}:`, err);
      if (!res.headersSent) {
        res.status(500).json({ message: `Error reading file ${filename} for employee ${employee}` });
      }
    });

    // إرسال الملف
    fileStream.pipe(res);
  } catch (err) {
    console.error(`Error reading file ${filename} for employee ${employee}:`, err);
    res.status(500).json({ message: `Error reading file ${filename} for employee ${employee}` });
  }
});

// Download file by name for an employee
router.get('/download/:employee/:filename', authenticateToken, (req, res) => {
  const { employee, filename } = req.params;
  const filePath = path.join(dataDir, employee, filename);

  try {
    // Check if file exists
    if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
      return res.status(404).json({ message: 'File not found' });
    }

    // Log activity
    logActivity(req.user.id, 'download_file', `Downloaded file: ${filename} for employee: ${employee}`);

    // Force download with original filename
    // Ensure proper encoding for the filename in Content-Disposition header
    const encodedFilename = encodeURIComponent(filename);

    // تسجيل اسم الملف للتشخيص
    console.log(`Downloading file: ${filename}, Encoded: ${encodedFilename}`);

    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
    res.sendFile(filePath, (err) => {
      if (err) {
        console.error(`Error downloading file ${filename} for employee ${employee}:`, err);
        if (!res.headersSent) {
          res.status(500).json({ message: `Error downloading file ${filename}` });
        }
      }
    });
  } catch (err) {
    console.error(`Error downloading file ${filename} for employee ${employee}:`, err);
    res.status(500).json({ message: `Error downloading file ${filename} for employee ${employee}` });
  }
});

// Upload file for an employee
router.post('/employee/:employee', authenticateToken, isEditor, upload.single('file'), (req, res) => {
  const { employee } = req.params;
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    // Ensure employee directory exists
    const employeePath = path.join(dataDir, employee);
    fs.ensureDirSync(employeePath);

    // Check if file already exists in employee directory
    const destFilePath = path.join(employeePath, file.originalname);
    if (fs.existsSync(destFilePath)) {
      // Delete the uploaded temp file since it's a duplicate
      try {
        fs.unlinkSync(file.path);
      } catch (err) {
        console.error('Error deleting duplicate file:', err);
      }

      return res.status(409).json({
        message: 'File already exists',
        fileExists: true,
        file: {
          name: file.originalname,
          path: `${employee}/${file.originalname}`,
          size: file.size
        }
      });
    }

    // إصلاح اسم الملف إذا كان يحتوي على ترميز خاطئ
    const originalFileName = file.originalname;
    const fixedFileName = fixArabicFileName(originalFileName);
    const finalDestPath = path.join(employeePath, fixedFileName);

    // Move file from temp to employee directory with fixed name
    try {
      fs.moveSync(file.path, finalDestPath);
      console.log(`File moved from ${file.path} to ${finalDestPath}`);
      if (originalFileName !== fixedFileName) {
        console.log(`File name encoding fixed: ${originalFileName} -> ${fixedFileName}`);
      }
    } catch (err) {
      console.error(`Error moving file from ${file.path} to ${finalDestPath}:`, err);
      return res.status(500).json({ message: 'Error moving file to employee directory' });
    }

    // Add file metadata to database
    db.run(
      'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
      [employee, fixedFileName, originalFileName, file.size],
      function(err) {
        if (err) {
          console.error('Database error:', err);
          return res.status(500).json({ message: 'Error saving file metadata' });
        }

        // Log activity
        logActivity(req.user.id, 'upload_file', `رفع ملف: ${fixedFileName} للموظف: ${employee}`);

        res.status(201).json({
          message: 'File uploaded successfully',
          file: {
            id: this.lastID,
            name: fixedFileName,
            originalName: originalFileName,
            path: `${employee}/${fixedFileName}`,
            size: file.size,
            createdAt: new Date(),
            encodingFixed: originalFileName !== fixedFileName
          }
        });
      }
    );
  } catch (err) {
    console.error(`Error uploading file for employee ${employee}:`, err);
    res.status(500).json({ message: `Error uploading file for employee ${employee}` });
  }
});

// Upload multiple files for an employee
router.post('/employee/:employee/multiple', authenticateToken, isEditor, upload.array('files', 20), async (req, res) => {
  const { employee } = req.params;
  const files = req.files;

  if (!files || files.length === 0) {
    return res.status(400).json({ message: 'No files uploaded' });
  }

  try {
    // Ensure employee directory exists
    const employeePath = path.join(dataDir, employee);
    fs.ensureDirSync(employeePath);

    const results = [];
    const existingFiles = [];
    const errors = [];

    // Process each file
    for (const file of files) {
      try {
        // Check if file already exists in employee directory
        const destFilePath = path.join(employeePath, file.originalname);
        if (fs.existsSync(destFilePath)) {
          console.log(`File already exists: ${destFilePath}`);

          // Delete the temp file
          try {
            fs.unlinkSync(file.path);
          } catch (err) {
            console.error(`Error deleting temp file ${file.path}:`, err);
          }

          existingFiles.push({
            fileName: file.originalname,
            path: `${employee}/${file.originalname}`,
            size: file.size
          });
          continue; // Skip this file
        }

        // إصلاح اسم الملف إذا كان يحتوي على ترميز خاطئ
        const originalFileName = file.originalname;
        const fixedFileName = fixArabicFileName(originalFileName);
        const finalDestPath = path.join(employeePath, fixedFileName);

        // Move file from temp to employee directory with fixed name
        try {
          fs.moveSync(file.path, finalDestPath);
          console.log(`File moved from ${file.path} to ${finalDestPath}`);
          if (originalFileName !== fixedFileName) {
            console.log(`File name encoding fixed: ${originalFileName} -> ${fixedFileName}`);
          }
        } catch (err) {
          console.error(`Error moving file from ${file.path} to ${finalDestPath}:`, err);
          errors.push({
            file: originalFileName,
            error: 'Error moving file to employee directory'
          });
          continue; // Skip to next file
        }

        // Add file metadata to database
        const fileId = await new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
            [employee, fixedFileName, originalFileName, file.size],
            function(err) {
              if (err) {
                console.error(`Database error for file ${fixedFileName}:`, err);
                reject(err);
              } else {
                console.log(`Database entry created for file: ${fixedFileName}, ID: ${this.lastID}`);
                resolve(this.lastID);
              }
            }
          );
        });

        results.push({
          id: fileId,
          name: fixedFileName,
          originalName: originalFileName,
          path: `${employee}/${fixedFileName}`,
          size: file.size,
          createdAt: new Date(),
          encodingFixed: originalFileName !== fixedFileName
        });
      } catch (err) {
        console.error(`Error processing file ${file.originalname}:`, err);
        errors.push({
          file: file.originalname,
          error: err.message
        });
      }
    }

    // Log activity
    logActivity(req.user.id, 'upload_multiple_files', `رفع ${results.length} ملفات للموظف: ${employee}`);

    // Determine message based on results
    let message = 'Files uploaded successfully';
    if (existingFiles.length > 0) {
      if (existingFiles.length === files.length) {
        message = 'All files already exist for the employee';
      } else {
        message = `${results.length} files uploaded successfully. ${existingFiles.length} files already exist.`;
      }
    }

    res.status(201).json({
      message,
      filesCount: files.length,
      successCount: results.length,
      errorCount: errors.length,
      existingFilesCount: existingFiles.length,
      existingFiles: existingFiles.length > 0 ? existingFiles : undefined,
      results,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (err) {
    console.error(`Error uploading files for employee ${employee}:`, err);
    res.status(500).json({
      message: `Error uploading files for employee ${employee}`,
      error: err.message
    });
  }
});

// Upload file to multiple employees
router.post('/bulk-upload', authenticateToken, isEditor, upload.single('file'), async (req, res) => {
  let employees = req.body.employees;
  const file = req.file;

  if (!file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  // Handle different formats of employees data
  if (typeof employees === 'string') {
    try {
      // Try to parse as JSON if it's a string
      employees = JSON.parse(employees);
    } catch (e) {
      // If not JSON, it might be a single employee name
      employees = [employees];
    }
  } else if (req.body['employees[0]']) {
    // Handle form-data array format
    employees = [];
    let i = 0;
    while (req.body[`employees[${i}]`] !== undefined) {
      employees.push(req.body[`employees[${i}]`]);
      i++;
    }
  }

  // Try to use the JSON backup if available
  if ((!employees || !Array.isArray(employees) || employees.length === 0) && req.body.employeesJson) {
    try {
      employees = JSON.parse(req.body.employeesJson);
      console.log('Using employeesJson backup:', employees);
    } catch (e) {
      console.error('Error parsing employeesJson:', e);
    }
  }

  // Log the employees data for debugging
  console.log('Employees data:', employees);
  console.log('Request body:', req.body);

  if (!employees || !Array.isArray(employees) || employees.length === 0) {
    return res.status(400).json({ message: 'No employees specified' });
  }

  try {
    const results = [];
    const existingFiles = [];
    const promises = [];

    // Process each employee sequentially to avoid overwhelming the system
    for (const employee of employees) {
      try {
        const employeePath = path.join(dataDir, employee);

        // Ensure employee directory exists
        fs.ensureDirSync(employeePath);

        // Check if file already exists in employee directory
        const destPath = path.join(employeePath, file.originalname);
        if (fs.existsSync(destPath)) {
          console.log(`File already exists for employee ${employee}: ${destPath}`);
          existingFiles.push({
            employee,
            fileName: file.originalname
          });
          continue; // Skip this employee
        }

        // إصلاح اسم الملف إذا كان يحتوي على ترميز خاطئ
        const originalFileName = file.originalname;
        const fixedFileName = fixArabicFileName(originalFileName);
        const finalDestPath = path.join(employeePath, fixedFileName);

        // Copy file to employee directory with fixed name
        fs.copySync(file.path, finalDestPath);
        console.log(`File copied to ${finalDestPath}`);
        if (originalFileName !== fixedFileName) {
          console.log(`File name encoding fixed for ${employee}: ${originalFileName} -> ${fixedFileName}`);
        }

        // Add file metadata to database using a Promise
        const promise = new Promise((resolve, reject) => {
          db.run(
            'INSERT INTO file_metadata (employee_folder, filename, original_name, size) VALUES (?, ?, ?, ?)',
            [employee, fixedFileName, originalFileName, file.size],
            function(err) {
              if (err) {
                console.error(`Database error for employee ${employee}:`, err);
                reject(err);
              } else {
                const result = {
                  employee,
                  fileId: this.lastID,
                  name: fixedFileName,
                  originalName: originalFileName,
                  path: `${employee}/${fixedFileName}`,
                  size: file.size,
                  encodingFixed: originalFileName !== fixedFileName
                };
                results.push(result);
                console.log(`Database entry created for ${employee}`);
                resolve(result);
              }
            }
          );
        });

        promises.push(promise);
      } catch (err) {
        console.error(`Error processing employee ${employee}:`, err);
        // Continue with other employees even if one fails
      }
    }

    // Wait for all database operations to complete
    try {
      await Promise.all(promises);
      console.log('All database operations completed successfully');
    } catch (err) {
      console.error('Error in database operations:', err);
      // Continue even if some database operations fail
    }

    // Delete the temporary file
    try {
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
        console.log('Temporary file deleted:', file.path);
      }
    } catch (err) {
      console.error('Error deleting temporary file:', err);
      // Continue even if we can't delete the temp file
    }

    // Log activity
    try {
      logActivity(req.user.id, 'bulk_upload_file', `رفع ملف: ${file.originalname} لعدد ${results.length} موظف`);
    } catch (err) {
      console.error('Error logging activity:', err);
      // Continue even if logging fails
    }

    // Determine message based on results
    let message = 'File uploaded to multiple employees successfully';
    if (existingFiles.length > 0) {
      if (existingFiles.length === employees.length) {
        message = 'File already exists for all selected employees';
      } else {
        message = `File uploaded to ${results.length} employees. File already exists for ${existingFiles.length} employees.`;
      }
    }

    res.status(201).json({
      message,
      results,
      employeesProcessed: employees.length,
      successCount: results.length,
      existingFilesCount: existingFiles.length,
      existingFiles: existingFiles.length > 0 ? existingFiles : undefined
    });
  } catch (err) {
    console.error('Error uploading file to multiple employees:', err);
    res.status(500).json({
      message: 'Error uploading file to multiple employees',
      error: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
});

// Delete file
router.delete('/employee/:employee/:filename', authenticateToken, isEditor, (req, res) => {
  const { employee, filename } = req.params;
  const filePath = path.join(dataDir, employee, filename);

  try {
    // Check if file exists
    if (!fs.existsSync(filePath) || !fs.statSync(filePath).isFile()) {
      return res.status(404).json({ message: 'File not found' });
    }

    // Delete file
    fs.unlinkSync(filePath);

    // Delete file metadata from database
    db.run(
      'DELETE FROM file_metadata WHERE employee_folder = ? AND filename = ?',
      [employee, filename],
      (err) => {
        if (err) {
          console.error('Database error:', err);
        }
      }
    );

    // Log activity
    logActivity(req.user.id, 'delete_file', `حذف ملف: ${filename} للموظف: ${employee}`);

    res.json({ message: `File ${filename} deleted successfully` });
  } catch (err) {
    console.error(`Error deleting file ${filename} for employee ${employee}:`, err);
    res.status(500).json({ message: `Error deleting file ${filename} for employee ${employee}` });
  }
});

// Search files
router.get('/search', authenticateToken, (req, res) => {
  const { query } = req.query;

  if (!query) {
    return res.status(400).json({ message: 'Search query is required' });
  }

  try {
    const results = [];

    // Read all employee directories
    const employees = fs.readdirSync(dataDir)
      .filter(item => fs.statSync(path.join(dataDir, item)).isDirectory());

    // Search for files in each employee directory
    for (const employee of employees) {
      const employeePath = path.join(dataDir, employee);

      // Read all files in employee directory
      const files = fs.readdirSync(employeePath)
        .filter(file => file.toLowerCase().includes(query.toLowerCase()))
        .map(file => {
          const filePath = path.join(employeePath, file);
          const stats = fs.statSync(filePath);

          return {
            name: file,
            employee,
            path: `${employee}/${file}`,
            size: stats.size,
            createdAt: stats.birthtime,
            updatedAt: stats.mtime
          };
        });

      results.push(...files);
    }

    // Log activity
    logActivity(req.user.id, 'search_files', `بحث عن ملفات بكلمة: ${query}`);

    res.json(results);
  } catch (err) {
    console.error(`Error searching files with query ${query}:`, err);
    res.status(500).json({ message: `Error searching files with query ${query}` });
  }
});

module.exports = router;
