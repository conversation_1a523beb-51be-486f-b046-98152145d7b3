# إصلاح مشكلة "Invalid token" في رفع الملفات

## المشكلة
كانت تظهر رسالة خطأ "Invalid token: حدث خطأ أثناء رفع الملفات" عند محاولة رفع ملفات لعدة موظفين.

## السبب
المشكلة كانت في طريقة إدارة الـ token في AuthContext والوصول إليه في مكون BulkUpload:

1. **في AuthContext**: كان الـ token يتم حفظه في localStorage/sessionStorage لكن لم يكن يتم تحديثه في الـ state
2. **في BulkUpload**: كان يتم الوصول للـ token من localStorage فقط، مما قد يسبب مشاكل إذا كان محفوظ في sessionStorage

## الحلول المطبقة

### 1. تحسين AuthContext (`frontend/src/contexts/AuthContext.jsx`)

#### إضافة token state:
```javascript
const [token, setToken] = useState(null);
```

#### إضافة useEffect لتحديث الـ token:
```javascript
useEffect(() => {
  const storedToken = localStorage.getItem('token') || sessionStorage.getItem('token');
  if (storedToken) {
    setToken(storedToken);
  }
}, []);
```

#### تحديث دالة login:
```javascript
setToken(receivedToken);
setUser(user);
setIsAuthenticated(true);
```

#### تحديث دالة logout:
```javascript
setToken(null);
setUser(null);
setIsAuthenticated(false);
```

#### تحديث دالة verifyToken:
```javascript
setToken(storedToken);
setUser(response.data.user);
setIsAuthenticated(true);
```

### 2. تحسين BulkUpload (`frontend/src/pages/BulkUpload.jsx`)

#### استخدام token من useAuth:
```javascript
const { user, token, getToken } = useAuth();
```

#### إضافة fallback للـ token:
```javascript
const currentToken = token || getToken();
```

#### إضافة تحقق من وجود الـ token:
```javascript
if (!currentToken) {
  setError('لم يتم العثور على رمز المصادقة. يرجى تسجيل الدخول مرة أخرى.');
  return;
}
```

### 3. إضافة Logging في Backend (`backend/middleware/auth.js`)

#### في authenticateToken middleware:
```javascript
console.log('Auth middleware - Authorization header:', authHeader);
console.log('Auth middleware - Token verified successfully for user:', decoded.id);
console.log('Auth middleware - User found:', user.username, 'Role:', user.role);
```

#### في isEditor middleware:
```javascript
console.log('isEditor middleware - Checking user role:', req.user ? req.user.role : 'No user');
console.log('isEditor middleware - User has editor privileges');
```

## كيفية اختبار الإصلاح

1. **تسجيل الدخول**: تأكد من تسجيل الدخول بحساب له صلاحيات editor أو admin
2. **الانتقال لصفحة رفع الملفات**: اذهب إلى `/bulk-upload`
3. **اختيار الملفات والموظفين**: اختر ملفات وموظفين
4. **محاولة الرفع**: اضغط على زر "رفع الملفات للموظفين المحددين"
5. **مراقبة Console**: افتح Developer Tools وراقب الـ console للتأكد من:
   - وجود الـ token
   - نجاح التوثيق في الـ backend

## رسائل Console المتوقعة

### في Frontend:
```
Token available: true
Token value: [JWT_TOKEN]
Sending request to: /api/direct-upload-multiple
```

### في Backend:
```
Auth middleware - Authorization header: Bearer [JWT_TOKEN]
Auth middleware - Extracted token: Token present
Auth middleware - Token verified successfully for user: [USER_ID]
Auth middleware - User found: [USERNAME] Role: [ROLE]
Auth middleware - Authentication successful, proceeding to next middleware
isEditor middleware - Checking user role: [ROLE]
isEditor middleware - User has editor privileges
```

## ملاحظات إضافية

- تأكد من أن المستخدم له صلاحيات `editor` أو `admin`
- تأكد من أن الـ token لم ينته صلاحيته (24 ساعة)
- إذا استمرت المشكلة، تحقق من الـ console logs في كل من Frontend و Backend

## الملفات المعدلة

1. `frontend/src/contexts/AuthContext.jsx`
2. `frontend/src/pages/BulkUpload.jsx`
3. `backend/middleware/auth.js`
