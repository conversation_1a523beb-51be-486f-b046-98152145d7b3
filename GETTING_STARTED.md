# دليل البدء السريع

هذا الدليل يساعدك على تثبيت وتشغيل نظام أرشفة ملفات الموظفين بسرعة.

## الخطوات

### 1. تثبيت الاعتماديات

قم بتثبيت جميع الاعتماديات المطلوبة للمشروع:

```bash
npm run install-all
```

هذا الأمر سيقوم بتثبيت الاعتماديات للمشروع الرئيسي والخادم والواجهة.

### 2. إنشاء بيانات تجريبية (اختياري)

لإنشاء بيانات تجريبية للاختبار:

```bash
npm run init-data
```

هذا الأمر سينشئ مجلدات للموظفين وملفات PDF تجريبية.

### 3. تشغيل التطبيق

لتشغيل الخادم والواجهة معًا:

```bash
npm run dev
```

هذا الأمر سيقوم بتشغيل:
- الخادم على المنفذ 5000
- الواجهة على المنفذ 5173

### 4. الوصول إلى التطبيق

افتح المتصفح وانتقل إلى:

```
http://localhost:5173
```

### 5. تسجيل الدخول

استخدم بيانات الدخول الافتراضية:

- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## هيكل الملفات

النظام يعتمد على هيكل ملفات محدد:

```
قاعدة_البيانات_المحلية/
├── أحمد محمد/
│   ├── كشف_شهر_2023.pdf
│   └── تقرير_أداء.pdf
├── سارة علي/
│   └── إجازة_سنوية.pdf
└── ...
```

## الصلاحيات

النظام يدعم ثلاثة أنواع من الصلاحيات:

1. **مدير (admin)**: يملك كافة الصلاحيات في النظام
2. **محرر (editor)**: يستطيع عرض وإضافة وتعديل الملفات
3. **مشاهد (viewer)**: يستطيع فقط عرض الملفات

## المساعدة

إذا واجهتك أي مشكلة، يرجى الرجوع إلى ملف README.md للحصول على معلومات إضافية.
