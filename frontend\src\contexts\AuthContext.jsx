import { createContext, useContext, useState, useCallback, useEffect } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [token, setToken] = useState(null);

  // Initialize token on component mount
  useEffect(() => {
    const storedToken = localStorage.getItem('token') || sessionStorage.getItem('token');
    if (storedToken) {
      setToken(storedToken);
    }
  }, []);

  // Login function
  const login = async (username, password, rememberMe = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await axios.post('/api/auth/login', { username, password });

      const { token: receivedToken, user } = response.data;

      // حفظ الرمز في localStorage أو sessionStorage حسب خيار "تذكرني"
      if (rememberMe) {
        // حفظ الرمز في localStorage للاحتفاظ به بعد إغلاق المتصفح
        localStorage.setItem('token', receivedToken);
      } else {
        // حفظ الرمز في sessionStorage ليتم مسحه عند إغلاق المتصفح
        sessionStorage.setItem('token', receivedToken);
      }

      // Set axios default headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${receivedToken}`;

      setToken(receivedToken);
      setUser(user);
      setIsAuthenticated(true);

      return true;
    } catch (err) {
      setError(err.response?.data?.message || 'حدث خطأ أثناء تسجيل الدخول');
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    // Remove token from localStorage and sessionStorage
    localStorage.removeItem('token');
    sessionStorage.removeItem('token');

    // Remove axios default headers
    delete axios.defaults.headers.common['Authorization'];

    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
  };

  // Verify token function
  const verifyToken = useCallback(async () => {
    try {
      setLoading(true);

      // التحقق من وجود الرمز في localStorage أو sessionStorage
      let storedToken = localStorage.getItem('token') || sessionStorage.getItem('token');

      if (!storedToken) {
        setToken(null);
        setIsAuthenticated(false);
        setUser(null);
        setLoading(false);
        return;
      }

      // Set axios default headers
      axios.defaults.headers.common['Authorization'] = `Bearer ${storedToken}`;

      const response = await axios.get('/api/auth/verify');

      setToken(storedToken);
      setUser(response.data.user);
      setIsAuthenticated(true);
    } catch (err) {
      // Token is invalid or expired
      localStorage.removeItem('token');
      sessionStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];

      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Get token from localStorage or sessionStorage
  const getToken = () => localStorage.getItem('token') || sessionStorage.getItem('token');

  const value = {
    user,
    isAuthenticated,
    loading,
    error,
    login,
    logout,
    verifyToken,
    getToken, // إضافة دالة الحصول على الرمز
    token // إضافة رمز المصادقة إلى القيمة المُصدرة
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
