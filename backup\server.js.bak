const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const { initializeDatabase } = require('./db/database');

// Import routes
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const fileRoutes = require('./routes/files');
const bulkUploadRoutes = require('./routes/bulk-upload');
const directUploadRoutes = require('./routes/direct-upload');
const directUploadMultipleRoutes = require('./routes/direct-upload-multiple');
const userRoutes = require('./routes/users');
const logRoutes = require('./routes/logs');
const backupRoutes = require('./routes/backup');

// Initialize express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Increase timeout
app.use((req, res, next) => {
  res.setTimeout(300000); // 5 minutes
  next();
});

// Initialize database
initializeDatabase();

// Create data directory structure if it doesn't exist
const dataDir = path.join(__dirname, 'data', 'employee_database');

// Ensure the directory exists
fs.ensureDirSync(dataDir);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/employees', employeeRoutes);
app.use('/api/files', fileRoutes);
app.use('/api/bulk-upload', bulkUploadRoutes);
// تسجيل مسارات الرفع المباشر بمسارات مختلفة لتجنب التداخل
console.log('Registering direct upload routes at /api/direct-upload');
app.use('/api/direct-upload', directUploadRoutes);

// تسجيل مسارات الرفع المباشر المتعدد بمسار مختلف
console.log('Registering direct upload multiple routes at /api/direct-upload-multiple');
app.use('/api/direct-upload-multiple', directUploadMultipleRoutes);
app.use('/api/users', userRoutes);
app.use('/api/logs', logRoutes);
app.use('/api/backup', backupRoutes);

// Serve static files (for production)
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../frontend/dist')));
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/dist/index.html'));
  });
}

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
