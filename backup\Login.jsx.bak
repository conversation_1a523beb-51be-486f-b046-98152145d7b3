import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { FiUser, FiLock, FiAlertCircle } from 'react-icons/fi';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login, loading } = useAuth();
  const navigate = useNavigate();
  
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!username || !password) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }
    
    const success = await login(username, password);
    
    if (success) {
      navigate('/dashboard');
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      {error && (
        <div className="p-3 mb-4 text-sm text-red-700 bg-red-100 rounded-lg flex items-center">
          <FiAlertCircle className="ml-2 flex-shrink-0" />
          <span>{error}</span>
        </div>
      )}
      
      <div className="mb-4">
        <label htmlFor="username" className="block mb-2 text-sm font-medium text-gray-700">
          اسم المستخدم
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <FiUser className="text-gray-400" />
          </div>
          <input
            type="text"
            id="username"
            className="input pr-10"
            placeholder="أدخل اسم المستخدم"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            disabled={loading}
          />
        </div>
      </div>
      
      <div className="mb-6">
        <label htmlFor="password" className="block mb-2 text-sm font-medium text-gray-700">
          كلمة المرور
        </label>
        <div className="relative">
          <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <FiLock className="text-gray-400" />
          </div>
          <input
            type="password"
            id="password"
            className="input pr-10"
            placeholder="أدخل كلمة المرور"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
        </div>
      </div>
      
      <button
        type="submit"
        className="w-full btn btn-primary py-2.5"
        disabled={loading}
      >
        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
      </button>
      
      <div className="mt-4 text-center text-sm text-gray-600">
        <p>اسم المستخدم الافتراضي: admin</p>
        <p>كلمة المرور الافتراضية: admin123</p>
      </div>
    </form>
  );
};

export default Login;
