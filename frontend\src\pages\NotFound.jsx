import { Link } from 'react-router-dom';
import { FiAlertTriangle, FiHome } from 'react-icons/fi';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4">
      <div className="text-center">
        <FiAlertTriangle className="mx-auto h-16 w-16 text-yellow-500" />
        <h1 className="mt-4 text-3xl font-bold text-gray-900">404</h1>
        <h2 className="mt-2 text-xl font-medium text-gray-700">الصفحة غير موجودة</h2>
        <p className="mt-2 text-gray-500">
          عذراً، الصفحة التي تبحث عنها غير موجودة.
        </p>
        <div className="mt-6">
          <Link
            to="/"
            className="btn btn-primary flex items-center justify-center mx-auto"
          >
            <FiHome className="ml-2" />
            العودة إلى الصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
